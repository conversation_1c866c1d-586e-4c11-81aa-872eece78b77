import request from '@/utils/request'

// 访客基础信息接口
export function listVisitorBasicInfo(query) {
  return request({
    url: '/asc/visitor/basics/list',
    method: 'get',
    params: query
  })
}

export function getVisitorBasicInfo(visitorId) {
  return request({
    url: '/asc/visitor/basics/' + visitorId,
    method: 'get'
  })
}

export function getVisitorByIdCard(idCardNumber) {
  return request({
    url: '/asc/visitor/basics/idcard/' + idCardNumber,
    method: 'get',
    headers: { isToken: false }
  })
}

export function addVisitorBasicInfo(data) {
  return request({
    url: '/asc/visitor/basics',
    method: 'post',
    data: data
  })
}

export function updateVisitorBasicInfo(data) {
  return request({
    url: '/asc/visitor/basics',
    method: 'put',
    data: data
  })
}

// 访客登记信息接口
export function listRegistrationInfo(query) {
  return request({
    url: '/asc/visitor/registration/list',
    method: 'get',
    params: query
  })
}

export function getRegistrationInfo(registrationId) {
  return request({
    url: '/asc/visitor/registration/' + registrationId,
    method: 'get'
  })
}

export function submitVisitorRegistration(data) {
  return request({
    url: '/asc/visitor/registration',
    method: 'post',
    data: data,
    headers: { isToken: false }
  })
}

export function updateRegistrationInfo(data) {
  return request({
    url: '/asc/visitor/registration',
    method: 'put',
    data: data
  })
}

// 查询今日登记信息
export function getTodayRegistrations() {
  return request({
    url: '/asc/visitor/registration/today',
    method: 'get'
  })
}

// 查询待审核的登记信息
export function getPendingRegistrations() {
  return request({
    url: '/asc/visitor/registration/pending',
    method: 'get'
  })
}

// 根据被访人查询登记信息
export function getRegistrationsByHost(hostEmployeeId) {
  return request({
    url: '/asc/visitor/registration/host/' + hostEmployeeId,
    method: 'get'
  })
}

// 导出访问登记信息列表
export function exportRegistrations(query) {
  return request({
    url: '/asc/visitor/registration/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 审核通过访问登记
export function approveRegistration(registrationId) {
  return request({
    url: '/asc/visitor/registration/approve/' + registrationId,
    method: 'put'
  })
}

// 拒绝访问登记
export function rejectRegistration(registrationId, rejectionData) {
  return request({
    url: '/asc/visitor/registration/reject/' + registrationId,
    method: 'put',
    data: rejectionData,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 取消访问登记
export function cancelRegistration(registrationId) {
  return request({
    url: '/asc/visitor/registration/cancel/' + registrationId,
    method: 'put'
  })
}

// 完成访问登记
export function completeRegistration(registrationId) {
  return request({
    url: '/asc/visitor/registration/complete/' + registrationId,
    method: 'put'
  })
}

// 删除访问登记信息
export function deleteRegistrations(registrationIds) {
  return request({
    url: '/asc/visitor/registration/' + registrationIds,
    method: 'delete'
  })
}

// 访客来访记录接口
export function listVisitRecords(query) {
  return request({
    url: '/asc/visitor/visit/list',
    method: 'get',
    params: query
  })
}

export function getCurrentVisits(query) {
  return request({
    url: '/asc/visitor/visit/current',
    method: 'get',
    params: query
  })
}

// 访客进出记录管理接口
export function listAccessLog(query) {
  return request({
    url: '/asc/visitor/access/list',
    method: 'get',
    params: query
  })
}

// 获取当前在场访客列表
export function getCurrentVisitors() {
  return request({
    url: '/asc/visitor/access/list',
    method: 'get',
    params: { 
      // 只查询未离场的访客（实际离场时间为空）
      actualExitDatetime: null 
    }
  })
}
