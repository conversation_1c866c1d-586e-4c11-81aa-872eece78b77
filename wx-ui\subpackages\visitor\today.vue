<template>
  <view class="today-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <uni-easyinput 
        v-model="searchKeyword" 
        placeholder="搜索访客姓名、电话或被访人"
        prefixIcon="search"
        :clearable="true"
        @confirm="handleSearch"
        @clear="handleSearch"
      />
    </view>

    <!-- 今日统计 -->
    <view class="stats-bar">
      <view class="stat-item">
        <text class="stat-number">{{ totalCount }}</text>
        <text class="stat-label">今日访客</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ checkedInCount }}</text>
        <text class="stat-label">已签入</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ checkedOutCount }}</text>
        <text class="stat-label">已签出</text>
      </view>
    </view>

    <!-- 访客列表 -->
    <view class="visitor-list">
      <uni-list v-if="visitorList.length > 0">
        <uni-list-item 
          v-for="visitor in visitorList.filter(v => v && v.registrationId)" 
          :key="visitor.registrationId"
          :title="visitor.primaryContactName || '未知访客'"
          :note="`${visitor.reasonForVisit || '无事由'} | ${visitor.primaryContactPhone || '无联系方式'}`"
          :rightText="getStatusText(visitor.status)"
          :clickable="true"
          @click="viewVisitorDetail(visitor)"
        >
          <template v-slot:header>
            <view class="visitor-avatar">
              <text class="avatar-text">{{ (visitor.primaryContactName || '未').charAt(0) }}</text>
            </view>
          </template>
          <template v-slot:footer>
            <view class="visitor-info">
              <text class="visit-purpose">被访人：{{ visitor.hostEmployeeName || '未指定' }}</text>
              <text class="visit-time">预计来访：{{ formatTime(visitor.plannedEntryDatetime) }}</text>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <uni-icons type="person" size="60" color="#ccc"></uni-icons>
        <text class="empty-text">今日暂无访客</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore" class="load-more" @click="loadMore">
      <text class="load-more-text">{{ loading ? '加载中...' : '加载更多' }}</text>
    </view>
  </view>
</template>

<script>
import { listRegistrationInfo } from '@/api/system/visitor'

export default {
  data() {
    return {
      searchKeyword: '',
      visitorList: [],
      loading: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 20,
      total: 0,
      totalCount: 0,
      checkedInCount: 0,
      checkedOutCount: 0
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '今日访客'
    });
    
    this.loadTodayVisitors();
  },
  onPullDownRefresh() {
    this.refreshList();
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore();
    }
  },
  methods: {
    // 加载今日访客列表
    async loadTodayVisitors(isLoadMore = false) {
      if (this.loading) return;
      
      this.loading = true;
      
      try {
        const today = new Date().toISOString().split('T')[0];
        const params = {
          pageNum: isLoadMore ? this.pageNum + 1 : 1,
          pageSize: this.pageSize,
          status: 'approved', // 只显示已通过审核的访客
          params: {
            beginTime: today,
            endTime: today
          }
        };
        
        // 添加搜索条件
        if (this.searchKeyword) {
          params.primaryContactName = this.searchKeyword;
          params.primaryContactPhone = this.searchKeyword;
          params.hostEmployeeName = this.searchKeyword;
        }
        
        const response = await listRegistrationInfo(params);
        
        if (response.code === 200) {
          const newList = response.rows || [];
          
          if (isLoadMore) {
            this.visitorList = [...this.visitorList, ...newList];
            this.pageNum += 1;
          } else {
            this.visitorList = newList;
            this.pageNum = 1;
            
            // 更新统计数据
            this.updateStats(newList);
          }
          
          this.total = response.total || 0;
          this.hasMore = this.visitorList.length < this.total;
        } else {
          this.$modal.showError(response.msg || '加载失败');
        }
      } catch (error) {
        console.error('加载今日访客失败:', error);
        this.$modal.showError('加载失败，请检查网络连接');
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },

    // 更新统计数据
    updateStats(visitorList) {
      this.totalCount = visitorList.length;
      this.checkedInCount = visitorList.filter(v => 
        v.status === 'checked_in' || v.status === 'CHECKED_IN'
      ).length;
      this.checkedOutCount = visitorList.filter(v => 
        v.status === 'checked_out' || v.status === 'CHECKED_OUT' || v.status === 'completed'
      ).length;
    },

    // 搜索处理
    handleSearch() {
      this.loadTodayVisitors();
    },

    // 刷新列表
    refreshList() {
      this.pageNum = 1;
      this.hasMore = true;
      this.loadTodayVisitors();
    },

    // 加载更多
    loadMore() {
      this.loadTodayVisitors(true);
    },

    // 查看访客详情
    viewVisitorDetail(visitor) {
      if (!visitor) {
        console.warn('viewVisitorDetail: visitor is undefined', visitor);
        this.$modal && this.$modal.showError
          ? this.$modal.showError('无效的访客数据')
          : alert('无效的访客数据');
        return;
      }
      const visitorId = visitor.registrationId;
      if (!visitorId) {
        console.warn('viewVisitorDetail: registrationId is missing', visitor);
        this.$modal && this.$modal.showError
          ? this.$modal.showError('访客ID缺失，无法查看详情')
          : alert('访客ID缺失，无法查看详情');
        return;
      }
      uni.navigateTo({
        url: `/subpackages/visitor/detail?registrationId=${visitorId}`,
        fail: (err) => {
          console.error('跳转到访客详情失败:', err, visitor);
          this.$modal && this.$modal.showError
            ? this.$modal.showError('页面跳转失败')
            : alert('页面跳转失败');
        }
      });
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'approved': '已通过',
        'in_progress': '进行中',
        'checked_in': '已签入',
        'checked_out': '已签出',
        'completed': '已完成',
        // 兼容大写格式
        'APPROVED': '已通过',
        'IN_PROGRESS': '进行中',
        'CHECKED_IN': '已签入',
        'CHECKED_OUT': '已签出',
        'COMPLETED': '已完成'
      };
      return statusMap[status] || '已通过';
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      
      const date = new Date(timeStr);
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
  }
};
</script>

<style lang="scss" scoped>
.today-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-bar {
  padding: 15px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.stats-bar {
  display: flex;
  background: white;
  padding: 15px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
  
  .stat-item {
    flex: 1;
    text-align: center;
    
    .stat-number {
      display: block;
      font-size: 24px;
      font-weight: bold;
      color: #667eea;
    }
    
    .stat-label {
      display: block;
      font-size: 12px;
      color: #999;
      margin-top: 5px;
    }
  }
}

.visitor-list {
  :deep(.uni-list-item) {
    .uni-list-item__container {
      padding: 15px;
      
      .uni-list-item__content {
        .uni-list-item__content-title {
          font-weight: bold;
          font-size: 16px;
        }
        
        .uni-list-item__content-note {
          color: #666;
          font-size: 12px;
        }
      }
      
      .uni-list-item__extra {
        .uni-list-item__extra-text {
          color: #667eea;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }
  }
}

.visitor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  
  .avatar-text {
    color: white;
    font-weight: bold;
    font-size: 16px;
  }
}

.visitor-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  
  .visit-purpose {
    font-size: 12px;
    color: #999;
    margin-bottom: 2px;
  }
  
  .visit-time {
    font-size: 11px;
    color: #ccc;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  
  .empty-text {
    display: block;
    margin-top: 15px;
    color: #999;
    font-size: 14px;
  }
}

.load-more {
  text-align: center;
  padding: 20px;
  
  .load-more-text {
    color: #666;
    font-size: 14px;
  }
}
</style>
