{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750987744033}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}