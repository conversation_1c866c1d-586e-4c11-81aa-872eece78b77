<template>
	<view>
		<view class="charts-box">
			<qiun-data-charts
			  type="line"
			  :opts="opts"
			  :chartData="chartData"/>
		</view>
		<button @click="queryChart('hour')">一小时</button>
		<button @click="queryChart('day')" >一天</button>
	    <button @click="queryChart('weekly')" >一星期</button>
	</view>

</template>

<script>
import uCharts from './uni_modules/qiun-data-charts/js_sdk/u-charts/u-charts.js';
var uChartsInstance = {};
import { api_query_chart } from "@/api/ems/ems.js"

export default {
  data() {
    return {
		showHour:true,
		showDay:false,
		showWeekly:false,
		globalData : getApp().globalData,
      chartData: {},
      //您可以通过修改 config-ucharts.js 文件中下标为 ['line'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
        padding: [15,10,0,15],
        legend: {},
		dataPointShape:false,
		dataLabel:false,
        xAxis: {
          disableGrid: true,
		  labelCount:8
        },
        yAxis: {
          gridType: "dash",
          dashLength: 2
        },
        extra: {
          line: {
            type: "curve",
            width: 2,
            activeType: "hollow"
          }
        }
      }
    };
  },

  onReady() {
    //this.getServerData();
	this.queryChart();
  },

  onLoad(e) {
	console.log(e)
	if(e.tagId){
		this.tagId = e.tagId;
	}
	uni.setNavigationBarTitle({
		title:e.title
	})
  },
  methods: {
	  queryChart(e){
		  if(e === undefined){
			  e = "hour"
		  }
	  		var request_data = {
	  			"tagId": this.tagId,
				"period": e
	  		};
			api_query_chart(request_data).then(response => {
					this.chartData = response.data;
	  				console.log(this.chartData);
	  				uni.showToast({
	  					title:"刷新成功",
	  					duration:1000,
	  				})
			}).catch(e=>{
				uni.showToast({
					title:e,
					duration:1000,
				})
			})
	  	},


    getServerData() {
      //模拟从服务器获取数据时的延时
      setTimeout(() => {
        //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res = {
            categories: ["2016","2017","2018","2019","2020","2021"],
            series: [
              {
                name: "成交量A",
                lineType: "dash",
                data: [35,8,25,37,4,20]
              },
              {
                name: "成交量B",
                data: [70,40,65,100,44,68]
              },
              {
                name: "成交量C",
                data: [100,80,95,150,112,132]
              }
            ]
          };
        this.chartData = JSON.parse(JSON.stringify(res));
		console.log(this.chartData);
      }, 500);
    },
  }
};
</script>

<style scoped>
  /* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
  .charts-box {
    width: 100%;
    height: 300px;
  }
</style>
