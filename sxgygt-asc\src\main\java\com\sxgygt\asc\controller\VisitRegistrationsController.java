package com.sxgygt.asc.controller;

import java.util.List;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.sxgygt.common.utils.StringUtils;
import com.sxgygt.common.annotation.Log;
import com.sxgygt.common.annotation.RateLimiter;
import com.sxgygt.common.annotation.RepeatSubmit;
import com.sxgygt.common.annotation.Anonymous;
import com.sxgygt.common.core.controller.BaseController;
import com.sxgygt.common.core.domain.AjaxResult;
import com.sxgygt.common.enums.BusinessType;
import com.sxgygt.common.enums.LimitType;
import com.sxgygt.asc.domain.VisitRegistrations;
import com.sxgygt.asc.domain.RegistrationAttendees;
import com.sxgygt.asc.service.IVisitRegistrationsService;
import com.sxgygt.asc.service.IRegistrationAttendeesService;
import com.sxgygt.asc.utils.VisitorSecurityUtils;
import com.sxgygt.common.utils.poi.ExcelUtil;
import com.sxgygt.common.utils.SecurityUtils;
import com.sxgygt.common.core.page.TableDataInfo;

/**
 * 访问登记信息Controller - 重构方案核心业务控制器
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/asc/visitor/registration")
public class VisitRegistrationsController extends BaseController
{
    @Autowired
    private IVisitRegistrationsService visitRegistrationsService;

    @Autowired
    private IRegistrationAttendeesService registrationAttendeesService;

    @Autowired
    private VisitorSecurityUtils visitorSecurityUtils;

    /**
     * 查询访问登记信息列表
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:list')")
    @GetMapping("/list")
    public TableDataInfo list(VisitRegistrations visitRegistrations)
    {
        startPage();
        List<VisitRegistrations> list = visitRegistrationsService.selectVisitRegistrationsList(visitRegistrations);
        return getDataTable(list);
    }

    /**
     * 查询待审核的登记信息
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:list')")
    @GetMapping("/pending")
    public AjaxResult getPendingRegistrations()
    {
        List<VisitRegistrations> list = visitRegistrationsService.selectPendingRegistrations();
        return success(list);
    }

    /**
     * 查询今日登记信息
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:list')")
    @GetMapping("/today")
    public AjaxResult getTodayRegistrations()
    {
        List<VisitRegistrations> list = visitRegistrationsService.selectTodayRegistrations();
        return success(list);
    }

    /**
     * 根据被访人查询登记信息
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:list')")
    @GetMapping("/host/{hostEmployeeId}")
    public AjaxResult getRegistrationsByHost(@PathVariable("hostEmployeeId") Long hostEmployeeId)
    {
        List<VisitRegistrations> list = visitRegistrationsService.selectRegistrationsByHostEmployeeId(hostEmployeeId);
        return success(list);
    }

    /**
     * 访客查询今日登记记录（根据身份证号或手机号）
     */
    @Anonymous
    @GetMapping("/query")
    public AjaxResult queryTodayRegistrations(
            @RequestParam(value = "idCardNumber", required = false) String idCardNumber,
            @RequestParam(value = "primaryContactPhone", required = false) String primaryContactPhone)
    {
        try {
            // 参数验证
            if (StringUtils.isEmpty(idCardNumber) && StringUtils.isEmpty(primaryContactPhone)) {
                return error("请提供身份证号或手机号进行查询");
            }

            // 验证身份证号格式（如果提供）
            if (StringUtils.isNotEmpty(idCardNumber)) {
                String idPattern = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
                if (!Pattern.matches(idPattern, idCardNumber)) {
                    return error("身份证号格式不正确");
                }
            }

            // 验证手机号格式（如果提供）
            if (StringUtils.isNotEmpty(primaryContactPhone)) {
                String phonePattern = "^1[3-9]\\d{9}$";
                if (!Pattern.matches(phonePattern, primaryContactPhone)) {
                    return error("手机号格式不正确");
                }
            }

            // 查询今日登记记录
            List<VisitRegistrations> list = visitRegistrationsService.queryTodayRegistrationsByVisitor(idCardNumber, primaryContactPhone);
            return success(list);
        } catch (Exception e) {
            logger.error("查询今日登记记录异常", e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 导出访问登记信息列表
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:export')")
    @Log(title = "访问登记信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VisitRegistrations visitRegistrations)
    {
        List<VisitRegistrations> list = visitRegistrationsService.selectVisitRegistrationsList(visitRegistrations);
        ExcelUtil<VisitRegistrations> util = new ExcelUtil<VisitRegistrations>(VisitRegistrations.class);
        util.exportExcel(response, list, "访问登记信息数据");
    }

    /**
     * 获取访问登记信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:query')")
    @GetMapping(value = "/{registrationId}")
    public AjaxResult getInfo(@PathVariable("registrationId") Long registrationId)
    {
        VisitRegistrations registration = visitRegistrationsService.selectVisitRegistrationsById(registrationId);
        if (registration != null) {
            // 同时获取参与人员信息
            List<RegistrationAttendees> attendeesList = registrationAttendeesService.selectAttendeesByRegistrationId(registrationId);
            registration.setAttendeesList(attendeesList);
        }
        return success(registration);
    }

    /**
     * 新增访问登记信息（支持多人登记 - 重构方案核心接口）
     */
    @Anonymous
    @Log(title = "访客登记", businessType = BusinessType.INSERT)
    @RateLimiter(key = "visitor_registration", time = 60, count = 10, limitType = LimitType.IP)
    @RepeatSubmit(interval = 3000, message = "访客登记提交过于频繁，请稍后再试")
    @PostMapping
    public AjaxResult add(@RequestBody VisitRegistrationRequest request)
    {
        try {
            // 1. 基础参数验证
            if (request == null || request.getRegistration() == null) {
                return error("请求参数不能为空");
            }

            // 2. 验证参与人员信息
            if (request.getAttendeesList() == null || request.getAttendeesList().isEmpty()) {
                return error("参与人员信息不能为空");
            }

            // 3. IP频率限制检查
            if (!visitorSecurityUtils.checkIpFrequency()) {
                return error("访问过于频繁，请稍后再试");
            }

            // 4. 手机号频率限制检查
            if (!visitorSecurityUtils.checkPhoneFrequency(request.getRegistration().getPrimaryContactPhone())) {
                return error("该手机号今日登记次数已达上限，请明日再试");
            }

            // 5. 验证访问时间合理性
            if (!visitorSecurityUtils.isValidVisitTime(request.getRegistration())) {
                return error("访问时间设置不合理，请重新选择");
            }

            // 6. 验证访客信息完整性和合理性
            if (!visitorSecurityUtils.validateVisitorInfo(request.getRegistration(), request.getAttendeesList())) {
                return error("访客信息不完整或不合理，请检查后重新提交");
            }

            // 7. 可疑请求检查
            if (visitorSecurityUtils.isSuspiciousRequest(request.getRegistration(), request.getAttendeesList())) {
                logger.warn("检测到可疑访客登记请求，IP: {}, 手机号: {}",
                    request.getRegistration().getPrimaryContactPhone());
                return error("请求包含异常信息，请联系管理员");
            }

            // 8. 验证主访客信息（使用前端提交的临时字段进行验证）
            boolean hasPrimary = request.getAttendeesList().stream()
                .anyMatch(attendee -> attendee.getVisitorName() != null &&
                         attendee.getVisitorName().equals(request.getPrimaryContactName()));

            if (!hasPrimary) {
                return error("主联系人必须在参与人员列表中");
            }

            // 9. 执行登记流程（按重构方案：先创建登记记录，再处理访客信息）
            int result = visitRegistrationsService.insertVisitRegistrations(
                request.getRegistration(),
                request.getAttendeesList()
            );

            if (result > 0) {
                return success("访客登记成功，等待审核");
            } else {
                return error("访客登记失败");
            }
        } catch (Exception e) {
            logger.error("访客登记异常", e);
            return error("访客登记异常：" + e.getMessage());
        }
    }

    /**
     * 修改访问登记信息
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:edit')")
    @Log(title = "访问登记信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VisitRegistrations visitRegistrations)
    {
        return toAjax(visitRegistrationsService.updateVisitRegistrations(visitRegistrations));
    }

    /**
     * 删除访问登记信息
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:remove')")
    @Log(title = "访问登记信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{registrationIds}")
    public AjaxResult remove(@PathVariable Long[] registrationIds)
    {
        return toAjax(visitRegistrationsService.deleteVisitRegistrationsByIds(registrationIds));
    }

    /**
     * 审核通过访问登记（重构方案核心业务）
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:approve')")
    @Log(title = "访客登记审核", businessType = BusinessType.UPDATE)
    @PutMapping("/approve/{registrationId}")
    public AjaxResult approve(@PathVariable("registrationId") Long registrationId)
    {
        try {
            Long approvalStaffId = SecurityUtils.getUserId();
            String approvalStaffName = SecurityUtils.getUsername();
            
            int result = visitRegistrationsService.approveRegistration(registrationId, approvalStaffId, approvalStaffName);
            
            if (result > 0) {
                return success("审核通过，已为访客生成通行凭证");
            } else {
                return error("审核失败");
            }
        } catch (Exception e) {
            logger.error("访客登记审核异常", e);
            return error("审核异常：" + e.getMessage());
        }
    }

    /**
     * 拒绝访问登记
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:approve')")
    @Log(title = "访客登记审核", businessType = BusinessType.UPDATE)
    @PutMapping("/reject/{registrationId}")
    public AjaxResult reject(@PathVariable("registrationId") Long registrationId, @RequestBody String rejectionReason)
    {
        try {
            Long approvalStaffId = SecurityUtils.getUserId();
            String approvalStaffName = SecurityUtils.getUsername();
            
            int result = visitRegistrationsService.rejectRegistration(registrationId, rejectionReason, approvalStaffId, approvalStaffName);
            
            if (result > 0) {
                return success("已拒绝该登记申请");
            } else {
                return error("操作失败");
            }
        } catch (Exception e) {
            logger.error("访客登记拒绝异常", e);
            return error("操作异常：" + e.getMessage());
        }
    }

    /**
     * 取消访问登记
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:edit')")
    @Log(title = "访问登记取消", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{registrationId}")
    public AjaxResult cancel(@PathVariable("registrationId") Long registrationId)
    {
        return toAjax(visitRegistrationsService.cancelRegistration(registrationId));
    }

    /**
     * 完成访问登记
     */
    @PreAuthorize("@ss.hasPermi('asc:registration:edit')")
    @Log(title = "访问登记完成", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{registrationId}")
    public AjaxResult complete(@PathVariable("registrationId") Long registrationId)
    {
        return toAjax(visitRegistrationsService.completeRegistration(registrationId));
    }

    /**
     * 访客登记请求DTO
     */
    public static class VisitRegistrationRequest {
        private VisitRegistrations registration;
        private List<RegistrationAttendees> attendeesList;

        public VisitRegistrations getRegistration() {
            return registration;
        }

        public void setRegistration(VisitRegistrations registration) {
            this.registration = registration;
        }

        public List<RegistrationAttendees> getAttendeesList() {
            return attendeesList;
        }

        public void setAttendeesList(List<RegistrationAttendees> attendeesList) {
            this.attendeesList = attendeesList;
        }

        public String getPrimaryContactName() {
            return registration != null ? registration.getPrimaryContactName() : null;
        }
    }

}
