<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sxgygt.asc.mapper.VisitRegistrationsMapper">
    
    <resultMap type="VisitRegistrations" id="VisitRegistrationsResult">
        <result property="registrationId"           column="registration_id"           />
        <result property="primaryContactName"       column="primary_contact_name"      />
        <result property="primaryContactPhone"      column="primary_contact_phone"     />
        <result property="reasonForVisit"           column="reason_for_visit"          />
        <result property="hostEmployeeName"         column="host_employee_name"        />
        <result property="hostEmployeeId"           column="host_employee_id"          />
        <result property="departmentVisited"        column="department_visited"        />
        <result property="plannedEntryDatetime"     column="planned_entry_datetime"    />
        <result property="plannedExitDatetime"      column="planned_exit_datetime"     />
        <result property="vehiclePlateNumber"       column="vehicle_plate_number"      />
        <result property="totalCompanions"          column="total_companions"          />
        <result property="submittedDatetime"        column="submitted_datetime"        />
        <result property="status"                   column="status"                    />
        <result property="approvalStaffId"          column="approval_staff_id"         />
        <result property="approvalStaffName"        column="approval_staff_name"       />
        <result property="approvalDatetime"         column="approval_datetime"         />
        <result property="rejectionReason"          column="rejection_reason"          />
        <result property="delFlag"                  column="del_flag"                  />
        <result property="createBy"                 column="create_by"                 />
        <result property="createTime"               column="create_time"               />
        <result property="updateBy"                 column="update_by"                 />
        <result property="updateTime"               column="update_time"               />
        <result property="remark"                   column="remark"                    />
    </resultMap>

    <!-- 包含关联访客信息的结果映射 -->
    <resultMap type="VisitRegistrations" id="VisitRegistrationsWithAttendeesResult" extends="VisitRegistrationsResult">
        <collection property="attendeesList" ofType="RegistrationAttendees" column="registration_id" select="selectAttendeesByRegistrationId"/>
    </resultMap>

    <sql id="selectVisitRegistrationsVo">
        select registration_id, primary_contact_name, primary_contact_phone, reason_for_visit, 
               host_employee_name, host_employee_id, department_visited, planned_entry_datetime, 
               planned_exit_datetime, vehicle_plate_number, total_companions, submitted_datetime, 
               status, approval_staff_id, approval_staff_name, approval_datetime, rejection_reason, 
               del_flag, create_by, create_time, update_by, update_time, remark 
        from asc_visit_registrations
    </sql>

    <select id="selectVisitRegistrationsList" parameterType="VisitRegistrations" resultMap="VisitRegistrationsResult">
        <include refid="selectVisitRegistrationsVo"/>
        <where>  
            <if test="primaryContactName != null  and primaryContactName != ''"> and primary_contact_name like concat('%', #{primaryContactName}, '%')</if>
            <if test="primaryContactPhone != null  and primaryContactPhone != ''"> and primary_contact_phone like concat('%', #{primaryContactPhone}, '%')</if>
            <if test="reasonForVisit != null  and reasonForVisit != ''"> and reason_for_visit like concat('%', #{reasonForVisit}, '%')</if>
            <if test="hostEmployeeName != null  and hostEmployeeName != ''"> and host_employee_name like concat('%', #{hostEmployeeName}, '%')</if>
            <if test="hostEmployeeId != null "> and host_employee_id = #{hostEmployeeId}</if>
            <if test="departmentVisited != null  and departmentVisited != ''"> and department_visited like concat('%', #{departmentVisited}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(planned_entry_datetime,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(planned_entry_datetime,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            and del_flag = '0'
        </where>
        order by submitted_datetime desc
    </select>
    
    <select id="selectVisitRegistrationsById" parameterType="Long" resultMap="VisitRegistrationsWithAttendeesResult">
        <include refid="selectVisitRegistrationsVo"/>
        where registration_id = #{registrationId} and del_flag = '0'
    </select>
    
    <select id="selectPendingRegistrations" resultMap="VisitRegistrationsResult">
        <include refid="selectVisitRegistrationsVo"/>
        where status = 'pending' and del_flag = '0'
        order by submitted_datetime asc
    </select>
    
    <select id="selectRegistrationsByHostEmployeeId" parameterType="Long" resultMap="VisitRegistrationsResult">
        <include refid="selectVisitRegistrationsVo"/>
        where host_employee_id = #{hostEmployeeId} and del_flag = '0'
        order by submitted_datetime desc
    </select>
    
    <select id="selectTodayRegistrations" resultMap="VisitRegistrationsResult">
        <include refid="selectVisitRegistrationsVo"/>
        where DATE(planned_entry_datetime) = CURDATE() and status = 'approved' and del_flag = '0'
        order by planned_entry_datetime asc
    </select>

    <select id="queryTodayRegistrationsByVisitor" resultMap="VisitRegistrationsWithAttendeesResult">
        <include refid="selectVisitRegistrationsVo"/>
        where DATE(submitted_datetime) = CURDATE() and del_flag = '0'
        <if test="idCardNumber != null and idCardNumber != ''">
            and (
                registration_id in (
                    select distinct ra.registration_id
                    from asc_registration_attendees ra
                    left join asc_visitor_basics vb on ra.visitor_id = vb.visitor_id
                    where vb.id_card_number = #{idCardNumber} and ra.del_flag = '0'
                )
                or registration_id in (
                    select distinct ra.registration_id
                    from asc_registration_attendees ra
                    where ra.visitor_id_card = #{idCardNumber} and ra.del_flag = '0'
                )
            )
        </if>
        <if test="primaryContactPhone != null and primaryContactPhone != ''">
            and (
                primary_contact_phone = #{primaryContactPhone}
                or registration_id in (
                    select distinct ra.registration_id
                    from asc_registration_attendees ra
                    left join asc_visitor_basics vb on ra.visitor_id = vb.visitor_id
                    where vb.phone = #{primaryContactPhone} and ra.del_flag = '0'
                )
                or registration_id in (
                    select distinct ra.registration_id
                    from asc_registration_attendees ra
                    where ra.visitor_phone = #{primaryContactPhone} and ra.del_flag = '0'
                )
            )
        </if>
        order by submitted_datetime desc
    </select>

    <select id="selectAttendeesByRegistrationId" parameterType="Long" resultType="RegistrationAttendees">
        select ra.attendee_id, ra.registration_id, ra.visitor_id, ra.is_primary, 
               ra.access_credential, ra.credential_expires_at, ra.del_flag, 
               ra.create_by, ra.create_time,
               vb.name as visitorName, vb.phone as visitorPhone, vb.id_card_number as visitorIdCard,
               vb.company as visitorCompany, vb.status as visitorStatus
        from asc_registration_attendees ra
        left join asc_visitor_basics vb on ra.visitor_id = vb.visitor_id
        where ra.registration_id = #{registrationId} and ra.del_flag = '0'
        order by ra.is_primary desc, ra.create_time asc
    </select>
        
    <insert id="insertVisitRegistrations" parameterType="VisitRegistrations" useGeneratedKeys="true" keyProperty="registrationId">
        insert into asc_visit_registrations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="primaryContactName != null and primaryContactName != ''">primary_contact_name,</if>
            <if test="primaryContactPhone != null and primaryContactPhone != ''">primary_contact_phone,</if>
            <if test="reasonForVisit != null and reasonForVisit != ''">reason_for_visit,</if>
            <if test="hostEmployeeName != null and hostEmployeeName != ''">host_employee_name,</if>
            <if test="hostEmployeeId != null">host_employee_id,</if>
            <if test="departmentVisited != null">department_visited,</if>
            <if test="plannedEntryDatetime != null">planned_entry_datetime,</if>
            <if test="plannedExitDatetime != null">planned_exit_datetime,</if>
            <if test="vehiclePlateNumber != null">vehicle_plate_number,</if>
            <if test="totalCompanions != null">total_companions,</if>
            <if test="submittedDatetime != null">submitted_datetime,</if>
            <if test="status != null">status,</if>
            <if test="approvalStaffId != null">approval_staff_id,</if>
            <if test="approvalStaffName != null">approval_staff_name,</if>
            <if test="approvalDatetime != null">approval_datetime,</if>
            <if test="rejectionReason != null">rejection_reason,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="primaryContactName != null and primaryContactName != ''">#{primaryContactName},</if>
            <if test="primaryContactPhone != null and primaryContactPhone != ''">#{primaryContactPhone},</if>
            <if test="reasonForVisit != null and reasonForVisit != ''">#{reasonForVisit},</if>
            <if test="hostEmployeeName != null and hostEmployeeName != ''">#{hostEmployeeName},</if>
            <if test="hostEmployeeId != null">#{hostEmployeeId},</if>
            <if test="departmentVisited != null">#{departmentVisited},</if>
            <if test="plannedEntryDatetime != null">#{plannedEntryDatetime},</if>
            <if test="plannedExitDatetime != null">#{plannedExitDatetime},</if>
            <if test="vehiclePlateNumber != null">#{vehiclePlateNumber},</if>
            <if test="totalCompanions != null">#{totalCompanions},</if>
            <if test="submittedDatetime != null">#{submittedDatetime},</if>
            <if test="status != null">#{status},</if>
            <if test="approvalStaffId != null">#{approvalStaffId},</if>
            <if test="approvalStaffName != null">#{approvalStaffName},</if>
            <if test="approvalDatetime != null">#{approvalDatetime},</if>
            <if test="rejectionReason != null">#{rejectionReason},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVisitRegistrations" parameterType="VisitRegistrations">
        update asc_visit_registrations
        <trim prefix="SET" suffixOverrides=",">
            <if test="primaryContactName != null and primaryContactName != ''">primary_contact_name = #{primaryContactName},</if>
            <if test="primaryContactPhone != null and primaryContactPhone != ''">primary_contact_phone = #{primaryContactPhone},</if>
            <if test="reasonForVisit != null and reasonForVisit != ''">reason_for_visit = #{reasonForVisit},</if>
            <if test="hostEmployeeName != null and hostEmployeeName != ''">host_employee_name = #{hostEmployeeName},</if>
            <if test="hostEmployeeId != null">host_employee_id = #{hostEmployeeId},</if>
            <if test="departmentVisited != null">department_visited = #{departmentVisited},</if>
            <if test="plannedEntryDatetime != null">planned_entry_datetime = #{plannedEntryDatetime},</if>
            <if test="plannedExitDatetime != null">planned_exit_datetime = #{plannedExitDatetime},</if>
            <if test="vehiclePlateNumber != null">vehicle_plate_number = #{vehiclePlateNumber},</if>
            <if test="totalCompanions != null">total_companions = #{totalCompanions},</if>
            <if test="submittedDatetime != null">submitted_datetime = #{submittedDatetime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="approvalStaffId != null">approval_staff_id = #{approvalStaffId},</if>
            <if test="approvalStaffName != null">approval_staff_name = #{approvalStaffName},</if>
            <if test="approvalDatetime != null">approval_datetime = #{approvalDatetime},</if>
            <if test="rejectionReason != null">rejection_reason = #{rejectionReason},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where registration_id = #{registrationId}
    </update>

    <delete id="deleteVisitRegistrationsById" parameterType="Long">
        update asc_visit_registrations set del_flag = '2' where registration_id = #{registrationId}
    </delete>

    <delete id="deleteVisitRegistrationsByIds" parameterType="String">
        update asc_visit_registrations set del_flag = '2' where registration_id in 
        <foreach item="registrationId" collection="array" open="(" separator="," close=")">
            #{registrationId}
        </foreach>
    </delete>

</mapper>
