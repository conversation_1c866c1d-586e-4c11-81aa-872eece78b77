import request from '@/utils/request'

// 访客基础信息接口
export function listVisitorBasicInfo(query) {
  return request({
    url: '/asc/visitor/basics/list',
    method: 'get',
    params: query
  })
}

export function getVisitorBasicInfo(visitorId) {
  return request({
    url: '/asc/visitor/basics/' + visitorId,
    method: 'get'
  })
}

export function getVisitorByIdCard(idCardNumber) {
  return request({
    url: '/asc/visitor/basics/idcard/' + idCardNumber,
    method: 'get'
  })
}

export function addVisitorBasicInfo(data) {
  return request({
    url: '/asc/visitor/basics',
    method: 'post',
    data: data
  })
}

export function updateVisitorBasicInfo(data) {
  return request({
    url: '/asc/visitor/basics',
    method: 'put',
    data: data
  })
}

export function removeVisitorAvatar(visitorId) {
  return request({
    url: '/asc/visitor/basics/avatar/' + visitorId,
    method: 'delete'
  })
}

// 删除上传的临时文件
export function deleteUploadedFile(fileName) {
  return request({
    url: '/common/deleteFile',
    method: 'post',
    params: {
      fileName
    }
  })
}

export function setBlacklistStatus(visitorId, isBlacklisted, blacklistReason) {
  return request({
    url: '/asc/visitor/basics/blacklist/' + visitorId,
    method: 'put',
    params: {
      isBlacklisted,
      blacklistReason
    }
  })
}

export function deleteVisitorBasicInfo(visitorIds) {
  return request({
    url: '/asc/visitor/basics/' + visitorIds,
    method: 'delete'
  })
}

// 获取访客统计信息
export function getVisitorStatistics() {
  return request({
    url: '/asc/visitor/basics/statistics',
    method: 'get'
  })
}

// 访客登记信息接口
export function listRegistrationInfo(query) {
  return request({
    url: '/asc/visitor/registration/list',
    method: 'get',
    params: query
  })
}

export function getPendingRegistrations(query) {
  return request({
    url: '/asc/visitor/registration/pending',
    method: 'get',
    params: query
  })
}

export function getRegistrationInfo(registrationId) {
  return request({
    url: '/asc/visitor/registration/' + registrationId,
    method: 'get'
  })
}

export function addVisitorRegistration(data) {
  return request({
    url: '/asc/visitor/registration',
    method: 'post',
    data: data
  })
}

// 提交访客登记（支持多人登记）
export function submitVisitorRegistration(data) {
  return request({
    url: '/asc/visitor/registration',
    method: 'post',
    data: data
  })
}

// 查询今日登记信息
export function getTodayRegistrations() {
  return request({
    url: '/asc/visitor/registration/today',
    method: 'get'
  })
}

// 访客查询今日登记记录（根据身份证号或手机号）
export function queryTodayRegistrations(params) {
  return request({
    url: '/asc/visitor/registration/query',
    method: 'get',
    params: params
  })
}

// 根据被访人查询登记信息
export function getRegistrationsByHost(hostEmployeeId) {
  return request({
    url: '/asc/visitor/registration/host/' + hostEmployeeId,
    method: 'get'
  })
}

export function updateRegistrationInfo(data) {
  return request({
    url: '/asc/visitor/registration',
    method: 'put',
    data: data
  })
}

export function delVisitorRegistrationInfo(registrationIds) {
  return request({
    url: '/asc/visitor/registration/' + registrationIds,
    method: 'delete'
  })
}

export function approveRegistration(registrationId, approved, rejectionReason) {
  return request({
    url: '/asc/visitor/registration/approve/' + registrationId,
    method: 'put',
    params: {
      approved,
      rejectionReason
    }
  })
}

// 审核通过访问登记
export function approveRegistrationPass(registrationId) {
  return request({
    url: '/asc/visitor/registration/approve/' + registrationId,
    method: 'put'
  })
}

// 拒绝访问登记
export function rejectRegistration(registrationId, rejectionReason) {
  return request({
    url: '/asc/visitor/registration/reject/' + registrationId,
    method: 'put',
    data: rejectionReason
  })
}

export function cancelRegistration(registrationId) {
  return request({
    url: '/asc/visitor/registration/cancel/' + registrationId,
    method: 'put'
  })
}

export function completeRegistration(registrationId) {
  return request({
    url: '/asc/visitor/registration/complete/' + registrationId,
    method: 'put'
  })
}

export function getRegistrationQrCode(registrationId) {
  return request({
    url: '/asc/visitor/registration/qrcode/' + registrationId,
    method: 'get'
  })
}

// 访客来访记录接口
export function listVisitRecords(query) {
  return request({
    url: '/asc/visitor/visit/list',
    method: 'get',
    params: query
  })
}

export function getCurrentVisits(query) {
  return request({
    url: '/asc/visitor/visit/current',
    method: 'get',
    params: query
  })
}

export function getOverstayVisits(query) {
  return request({
    url: '/asc/visitor/visit/overstay',
    method: 'get',
    params: query
  })
}

export function getVisitRecord(visitLogId) {
  return request({
    url: '/asc/visitor/visit/' + visitLogId,
    method: 'get'
  })
}

export function getVisitorVisitHistory(visitorId) {
  return request({
    url: '/asc/visitor/access/history/' + visitorId,
    method: 'get'
  })
}

export function getVisitByRegistration(registrationId) {
  return request({
    url: '/asc/visitor/visit/registration/' + registrationId,
    method: 'get'
  })
}

export function recordVisitorEntry(registrationId, entryMethod, entryGate, visitorBadgeId, temperature) {
  return request({
    url: '/asc/visitor/visit/entry',
    method: 'post',
    params: {
      registrationId,
      entryMethod,
      entryGate,
      visitorBadgeId,
      temperature
    }
  })
}

export function recordVisitorExit(visitLogId, exitMethod, exitGate) {
  return request({
    url: '/asc/visitor/visit/exit/' + visitLogId,
    method: 'put',
    params: {
      exitMethod,
      exitGate
    }
  })
}

// 导出接口
export function exportVisitorBasicInfo(query) {
  return request({
    url: '/asc/visitor/basic/export',
    method: 'get',
    params: query
  })
}

export function exportRegistrationInfo(query) {
  return request({
    url: '/asc/visitor/registration/export',
    method: 'get',
    params: query
  })
}

export function exportVisitRecords(query) {
  return request({
    url: '/asc/visitor/visit/export',
    method: 'get',
    params: query
  })
}

// 访客进出记录管理接口
export function listAccessLog(query) {
  return request({
    url: '/asc/visitor/access/list',
    method: 'get',
    params: query
  })
}

export function getAccessLogInfo(visitLogId) {
  return request({
    url: '/asc/visitor/access/' + visitLogId,
    method: 'get'
  })
}

export function handleVisitorExit(data) {
  return request({
    url: '/asc/visitor/access/exit',
    method: 'put',
    data: data
  })
}

export function deleteAccessLog(visitLogIds) {
  return request({
    url: '/asc/visitor/access/' + visitLogIds,
    method: 'delete'
  })
}

export function checkOvertimeVisitors() {
  return request({
    url: '/asc/visitor/access/overtime/check',
    method: 'post'
  })
}

export function getOvertimeVisitors() {
  return request({
    url: '/asc/visitor/access/overtime',
    method: 'get'
  })
}

export function getCurrentVisitors() {
  return request({
    url: '/asc/visitor/access/current',
    method: 'get'
  })
}

export function getDailyStatistics(date) {
  return request({
    url: '/asc/visitor/statistics/daily',
    method: 'get',
    params: { date }
  })
}

export function getMonthlyStatistics(yearMonth) {
  return request({
    url: '/asc/visitor/statistics/monthly',
    method: 'get',
    params: { yearMonth }
  })
}
