{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750987744033}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBzdWJtaXRWaXNpdG9yUmVnaXN0cmF0aW9uIH0gZnJvbSAiQC9hcGkvYXNjL3Zpc2l0b3IiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJTZWxmUmVnaXN0ZXIiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzdWJtaXR0aW5nOiBmYWxzZSwNCiAgICAgIHJlZ2lzdHJhdGlvbkNvbXBsZXRlZDogZmFsc2UsDQoNCiAgICAgIC8vIOihqOWNleaVsOaNriAtIOS4juW+ruS/oeerr+S/neaMgeS4gOiHtOeahOe7k+aehA0KICAgICAgZm9ybURhdGE6IHsNCiAgICAgICAgLy8g6K6/5a6i5YiX6KGo77yI56ys5LiA5L2N5Li65Li76IGU57O75Lq677yJDQogICAgICAgIHZpc2l0b3JzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICBwaG9uZTogJycsDQogICAgICAgICAgICBpZENhcmQ6ICcnLA0KICAgICAgICAgICAgY29tcGFueTogJycsDQogICAgICAgICAgICBpc01haW5Db250YWN0OiB0cnVlDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICAvLyDmnaXorr/kv6Hmga8NCiAgICAgICAgdmlzaXRJbmZvOiB7DQogICAgICAgICAgcmVhc29uRm9yVmlzaXQ6ICcnLA0KICAgICAgICAgIGhvc3RFbXBsb3llZU5hbWU6ICcnLA0KICAgICAgICAgIGRlcGFydG1lbnRWaXNpdGVkOiAnJywNCiAgICAgICAgICB2ZWhpY2xlUGxhdGVOdW1iZXI6ICcnLA0KICAgICAgICAgIHBsYW5uZWRFbnRyeURhdGV0aW1lOiAnJywNCiAgICAgICAgICBwbGFubmVkRXhpdERhdGV0aW1lOiAnJw0KICAgICAgICB9DQogICAgICB9LA0KDQogICAgICAvLyDooajljZXpqozor4Hop4TliJkgLSDkuI7lvq7kv6Hnq6/kv53mjIHkuIDoh7QNCiAgICAgIHZpc2l0UnVsZXM6IHsNCiAgICAgICAgJ3Zpc2l0SW5mby5yZWFzb25Gb3JWaXNpdCc6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5p2l6K6/5LqL55SxJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBtaW46IDIsIG1heDogMjAwLCBtZXNzYWdlOiAn5p2l6K6/5LqL55Sx6ZW/5bqm5bqU5ZyoMi0yMDDkuKrlrZfnrKbkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICAndmlzaXRJbmZvLmhvc3RFbXBsb3llZU5hbWUnOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeiiq+iuv+S6uuWnk+WQjScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDIwLCBtZXNzYWdlOiAn6KKr6K6/5Lq65aeT5ZCN6ZW/5bqm5bqU5ZyoMi0yMOS4quWtl+espuS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgICd2aXNpdEluZm8uZGVwYXJ0bWVudFZpc2l0ZWQnOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeiiq+iuv+mDqOmXqCcsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDUwLCBtZXNzYWdlOiAn6YOo6Zeo5ZCN56ew6ZW/5bqm5bqU5ZyoMi01MOS4quWtl+espuS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgICd2aXNpdEluZm8ucGxhbm5lZEVudHJ5RGF0ZXRpbWUnOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqemihOiuoeadpeiuv+aXtumXtCcsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgJ3Zpc2l0SW5mby5wbGFubmVkRXhpdERhdGV0aW1lJzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6npooTorqHnprvlvIDml7bpl7QnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIC8vIOWKqOaAgeiuv+WuoumqjOivgeinhOWImQ0KICAgICAgICAndmlzaXRvcnMuMC5uYW1lJzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXkuLvogZTns7vkurrlp5PlkI0nLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAyMCwgbWVzc2FnZTogJ+Wnk+WQjemVv+W6puW6lOWcqDItMjDkuKrlrZfnrKbkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICAndmlzaXRvcnMuMC5waG9uZSc6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5Li76IGU57O75Lq65omL5py65Y+3JywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBwYXR0ZXJuOiAvXjFbMy05XVxkezl9JC8sIG1lc3NhZ2U6ICfor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7cnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICAndmlzaXRvcnMuMC5pZENhcmQnOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeS4u+iBlOezu+S6uui6q+S7veivgeWPtycsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQoNCiAgICAgIC8vIOeZu+iusOe7k+aenA0KICAgICAgcmVnaXN0cmF0aW9uSWQ6IG51bGwsDQoNCiAgICAgIC8vIOaXtumXtOmAieaLqeWZqOmFjee9rg0KICAgICAgZW50cnlQaWNrZXJPcHRpb25zOiB7DQogICAgICAgIHNob3J0Y3V0czogW3sNCiAgICAgICAgICB0ZXh0OiAn546w5ZyoJywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgbmV3IERhdGUoKSk7DQogICAgICAgICAgfQ0KICAgICAgICB9LCB7DQogICAgICAgICAgdGV4dDogJzHlsI/ml7blkI4nLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSArIDM2MDAgKiAxMDAwKTsNCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIGRhdGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwgew0KICAgICAgICAgIHRleHQ6ICfmmI7lpKnkuIrljYg554K5JywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBkYXRlLnNldERhdGUoZGF0ZS5nZXREYXRlKCkgKyAxKTsNCiAgICAgICAgICAgIGRhdGUuc2V0SG91cnMoOSwgMCwgMCwgMCk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBkYXRlKTsNCiAgICAgICAgICB9DQogICAgICAgIH1dLA0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICAgIC8vIOWFgeiuuOmAieaLqeW9k+WJjeaXtumXtOWJjTHlsI/ml7bliLDmnKrmnaUzMOWkqQ0KICAgICAgICAgIGNvbnN0IG9uZUhvdXJCZWZvcmUgPSBEYXRlLm5vdygpIC0gMzYwMCAqIDEwMDA7DQogICAgICAgICAgY29uc3QgdGhpcnR5RGF5c0xhdGVyID0gRGF0ZS5ub3coKSArIDMwICogMjQgKiAzNjAwICogMTAwMDsNCiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBvbmVIb3VyQmVmb3JlIHx8IHRpbWUuZ2V0VGltZSgpID4gdGhpcnR5RGF5c0xhdGVyOw0KICAgICAgICB9DQogICAgICB9LA0KDQogICAgICBleGl0UGlja2VyT3B0aW9uczogew0KICAgICAgICBzaG9ydGN1dHM6IFt7DQogICAgICAgICAgdGV4dDogJzLlsI/ml7blkI4nLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSArIDIgKiAzNjAwICogMTAwMCk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBkYXRlKTsNCiAgICAgICAgICB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICB0ZXh0OiAn5Lit5Y2IMTLngrknLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGRhdGUuc2V0SG91cnMoMTIsIDAsIDAsIDApOw0KICAgICAgICAgICAgaWYgKGRhdGUuZ2V0VGltZSgpIDwgRGF0ZS5ub3coKSkgew0KICAgICAgICAgICAgICBkYXRlLnNldERhdGUoZGF0ZS5nZXREYXRlKCkgKyAxKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIGRhdGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwgew0KICAgICAgICAgIHRleHQ6ICfku4rlpKnkuIvljYg254K5JywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBkYXRlLnNldEhvdXJzKDE4LCAwLCAwLCAwKTsNCiAgICAgICAgICAgIGlmIChkYXRlLmdldFRpbWUoKSA8IERhdGUubm93KCkpIHsNCiAgICAgICAgICAgICAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpICsgMSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBkYXRlKTsNCiAgICAgICAgICB9DQogICAgICAgIH1dLA0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICAgIC8vIOWFgeiuuOmAieaLqeW9k+WJjeaXtumXtOWIsOacquadpTMw5aSpDQogICAgICAgICAgY29uc3QgdGhpcnR5RGF5c0xhdGVyID0gRGF0ZS5ub3coKSArIDMwICogMjQgKiAzNjAwICogMTAwMDsNCiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBEYXRlLm5vdygpIC0gOC42NGU3IHx8IHRpbWUuZ2V0VGltZSgpID4gdGhpcnR5RGF5c0xhdGVyOw0KICAgICAgICB9DQogICAgICB9DQogICAgfTsNCiAgfSwNCg0KICBjb21wdXRlZDogew0KICAgIC8vIOaAu+iuv+WuouS6uuaVsA0KICAgIHRvdGFsVmlzaXRvcnMoKSB7DQogICAgICByZXR1cm4gdGhpcy5mb3JtRGF0YS52aXNpdG9ycy5sZW5ndGg7DQogICAgfSwNCg0KICAgIC8vIOS4u+iBlOezu+S6uuS/oeaBrw0KICAgIG1haW5Db250YWN0KCkgew0KICAgICAgcmV0dXJuIHRoaXMuZm9ybURhdGEudmlzaXRvcnNbMF0gfHwge307DQogICAgfQ0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmt7vliqDorr/lrqINCiAgICBhZGRWaXNpdG9yKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybURhdGEudmlzaXRvcnMubGVuZ3RoID49IDEwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pyA5aSa5Y+q6IO95re75YqgMTDlkI3orr/lrqInKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzLnB1c2goew0KICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgcGhvbmU6ICcnLA0KICAgICAgICBpZENhcmQ6ICcnLA0KICAgICAgICBjb21wYW55OiAnJywNCiAgICAgICAgaXNNYWluQ29udGFjdDogZmFsc2UNCiAgICAgIH0pOw0KDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3sua3u+WKoOiuv+WuoicpOw0KICAgIH0sDQoNCiAgICAvLyDnp7vpmaTorr/lrqINCiAgICByZW1vdmVWaXNpdG9yKGluZGV4KSB7DQogICAgICBpZiAoaW5kZXggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfkuI3og73liKDpmaTkuLvogZTns7vkuronKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzLnNwbGljZShpbmRleCwgMSk7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suenu+mZpOiuv+WuoicpOw0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/kuLrlkI7nq6/mnJ/mnJvnmoTmoLzlvI8NCiAgICBmb3JtYXREYXRlRm9yQmFja2VuZChkYXRlU3RyKSB7DQogICAgICBpZiAoIWRhdGVTdHIpIHJldHVybiAnJzsNCg0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHIpOw0KICAgICAgaWYgKGlzTmFOKGRhdGUuZ2V0VGltZSgpKSkgcmV0dXJuICcnOw0KDQogICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IGRheSA9IFN0cmluZyhkYXRlLmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IGhvdXJzID0gU3RyaW5nKGRhdGUuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IG1pbnV0ZXMgPSBTdHJpbmcoZGF0ZS5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBzZWNvbmRzID0gU3RyaW5nKGRhdGUuZ2V0U2Vjb25kcygpKS5wYWRTdGFydCgyLCAnMCcpOw0KDQogICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9ICR7aG91cnN9OiR7bWludXRlc306JHtzZWNvbmRzfWA7DQogICAgfSwNCg0KICAgIC8qKiDmnaXorr/ml7bpl7Tlj5jmm7Tkuovku7YgKi8NCiAgICBvbkFycml2YWxUaW1lQ2hhbmdlKHZhbHVlKSB7DQogICAgICBjb25zb2xlLmxvZygn6aKE6K6h5p2l6K6/5pe26Ze05Y+Y5pu0OicsIHZhbHVlKTsNCiAgICAgIC8vIOiHquWKqOiuvue9ruemu+W8gOaXtumXtOS4uuadpeiuv+aXtumXtOWQjjTlsI/ml7YNCiAgICAgIGlmICh2YWx1ZSAmJiAhdGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEV4aXREYXRldGltZSkgew0KICAgICAgICBjb25zdCBhcnJpdmFsVGltZSA9IG5ldyBEYXRlKHZhbHVlKTsNCiAgICAgICAgY29uc3QgZGVwYXJ0dXJlVGltZSA9IG5ldyBEYXRlKGFycml2YWxUaW1lLmdldFRpbWUoKSArIDQgKiA2MCAqIDYwICogMTAwMCk7DQoNCiAgICAgICAgY29uc3QgeWVhciA9IGRlcGFydHVyZVRpbWUuZ2V0RnVsbFllYXIoKTsNCiAgICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgICAgY29uc3QgZGF5ID0gU3RyaW5nKGRlcGFydHVyZVRpbWUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkZXBhcnR1cmVUaW1lLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICAgIGNvbnN0IG1pbnV0ZXMgPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICAgIGNvbnN0IHNlY29uZHMgPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQoNCiAgICAgICAgdGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEV4aXREYXRldGltZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog56a75byA5pe26Ze05Y+Y5pu05LqL5Lu2ICovDQogICAgb25EZXBhcnR1cmVUaW1lQ2hhbmdlKHZhbHVlKSB7DQogICAgICBjb25zb2xlLmxvZygn6aKE6K6h56a75byA5pe26Ze05Y+Y5pu0OicsIHZhbHVlKTsNCiAgICAgIC8vIOmqjOivgeemu+W8gOaXtumXtOS4jeiDveaXqeS6juadpeiuv+aXtumXtA0KICAgICAgaWYgKHZhbHVlICYmIHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnBsYW5uZWRFbnRyeURhdGV0aW1lKSB7DQogICAgICAgIGNvbnN0IGFycml2YWxUaW1lID0gbmV3IERhdGUodGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEVudHJ5RGF0ZXRpbWUpOw0KICAgICAgICBjb25zdCBkZXBhcnR1cmVUaW1lID0gbmV3IERhdGUodmFsdWUpOw0KDQogICAgICAgIGlmIChkZXBhcnR1cmVUaW1lIDw9IGFycml2YWxUaW1lKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfpooTorqHnprvlvIDml7bpl7TkuI3og73ml6nkuo7miJbnrYnkuo7mnaXorr/ml7bpl7QnKTsNCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLnZpc2l0SW5mby5wbGFubmVkRXhpdERhdGV0aW1lID0gJyc7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOaPkOS6pOihqOWNlSAqLw0KICAgIGFzeW5jIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDpqozor4HooajljZUNCiAgICAgICAgYXdhaXQgdGhpcy4kcmVmcy52aXNpdEZvcm0udmFsaWRhdGUoKTsNCg0KICAgICAgICAvLyDpqozor4Horr/lrqLkv6Hmga8NCiAgICAgICAgaWYgKCF0aGlzLnZhbGlkYXRlVmlzaXRvcnMoKSkgew0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmqjOivgeaXtumXtA0KICAgICAgICBpZiAoIXRoaXMudmFsaWRhdGVUaW1lcygpKSB7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5zdWJtaXR0aW5nID0gdHJ1ZTsNCg0KICAgICAgICAvLyDojrflj5bkuLvogZTns7vkurrkv6Hmga8NCiAgICAgICAgY29uc3QgbWFpbkNvbnRhY3QgPSB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzWzBdOw0KDQogICAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB7DQogICAgICAgICAgLy8gVmlzaXRSZWdpc3RyYXRpb25zIOWvueixoQ0KICAgICAgICAgIHJlZ2lzdHJhdGlvbjogew0KICAgICAgICAgICAgcHJpbWFyeUNvbnRhY3ROYW1lOiBtYWluQ29udGFjdC5uYW1lLnRyaW0oKSwNCiAgICAgICAgICAgIHByaW1hcnlDb250YWN0UGhvbmU6IG1haW5Db250YWN0LnBob25lLnRyaW0oKSwNCiAgICAgICAgICAgIHJlYXNvbkZvclZpc2l0OiB0aGlzLmZvcm1EYXRhLnZpc2l0SW5mby5yZWFzb25Gb3JWaXNpdCwNCiAgICAgICAgICAgIGhvc3RFbXBsb3llZU5hbWU6IHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLmhvc3RFbXBsb3llZU5hbWUsDQogICAgICAgICAgICBob3N0RW1wbG95ZWVJZDogbnVsbCwgLy8g5YmN56uv5pqC5pe25rKh5pyJ6KKr6K6/5Lq6SUTvvIzlkI7nq6/lj6/og73kvJroh6rliqjlpITnkIYNCiAgICAgICAgICAgIGRlcGFydG1lbnRWaXNpdGVkOiB0aGlzLmZvcm1EYXRhLnZpc2l0SW5mby5kZXBhcnRtZW50VmlzaXRlZCwNCiAgICAgICAgICAgIHBsYW5uZWRFbnRyeURhdGV0aW1lOiB0aGlzLmZvcm1hdERhdGVGb3JCYWNrZW5kKHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnBsYW5uZWRFbnRyeURhdGV0aW1lKSwNCiAgICAgICAgICAgIHBsYW5uZWRFeGl0RGF0ZXRpbWU6IHRoaXMuZm9ybWF0RGF0ZUZvckJhY2tlbmQodGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEV4aXREYXRldGltZSksDQogICAgICAgICAgICB2ZWhpY2xlUGxhdGVOdW1iZXI6IHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnZlaGljbGVQbGF0ZU51bWJlciB8fCAnJywNCiAgICAgICAgICAgIHRvdGFsQ29tcGFuaW9uczogdGhpcy5mb3JtRGF0YS52aXNpdG9ycy5sZW5ndGggLSAxDQogICAgICAgICAgfSwNCiAgICAgICAgICAvLyBSZWdpc3RyYXRpb25BdHRlbmRlZXMg5pWw57uEIC0g5L2/55So5ZCO56uv5pyf5pyb55qE5Li05pe25a2X5q61DQogICAgICAgICAgYXR0ZW5kZWVzTGlzdDogdGhpcy5mb3JtRGF0YS52aXNpdG9ycy5tYXAoKHZpc2l0b3IsIGluZGV4KSA9PiAoew0KICAgICAgICAgICAgdmlzaXRvck5hbWU6IHZpc2l0b3IubmFtZS50cmltKCksDQogICAgICAgICAgICB2aXNpdG9yUGhvbmU6IHZpc2l0b3IucGhvbmUudHJpbSgpLA0KICAgICAgICAgICAgdmlzaXRvcklkQ2FyZDogdmlzaXRvci5pZENhcmQudHJpbSgpLnRvVXBwZXJDYXNlKCksDQogICAgICAgICAgICB2aXNpdG9yQ29tcGFueTogKHZpc2l0b3IuY29tcGFueSB8fCAnJykudHJpbSgpLA0KICAgICAgICAgICAgaXNQcmltYXJ5OiBpbmRleCA9PT0gMCA/ICIxIiA6ICIwIiwgLy8g56ys5LiA5Liq6K6/5a6i5b+F6aG75piv5Li76IGU57O75Lq6DQogICAgICAgICAgICB2aXNpdG9yQXZhdGFyUGhvdG86IG51bGwgLy8g5pqC5pe25LiN5pSv5oyB5aS05YOP5LiK5LygDQogICAgICAgICAgfSkpDQogICAgICAgIH07DQoNCiAgICAgICAgY29uc29sZS5sb2coJ+aPkOS6pOaVsOaNrue7k+aehDonLCBKU09OLnN0cmluZ2lmeShzdWJtaXREYXRhLCBudWxsLCAyKSk7DQoNCiAgICAgICAgLy8g6LCD55SoQVBJ5o+Q5Lqk5pWw5o2uDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc3VibWl0VmlzaXRvclJlZ2lzdHJhdGlvbihzdWJtaXREYXRhKTsNCg0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5yZWdpc3RyYXRpb25JZCA9IHJlc3BvbnNlLmRhdGEgfHwgJ1ZSJyArIERhdGUubm93KCk7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGAke3RoaXMudG90YWxWaXNpdG9yc33lkI3orr/lrqLnmbvorrDmiJDlip/vvIFgKTsNCiAgICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbkNvbXBsZXRlZCA9IHRydWU7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+eZu+iusOWksei0pe+8jOivt+mHjeivlScpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmj5DkuqTooajljZXlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnmbvorrDlpLHotKXvvIzor7fmo4Dmn6XnvZHnu5zov57mjqUnKTsNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuc3VibWl0dGluZyA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDpqozor4Horr/lrqLkv6Hmga8NCiAgICB2YWxpZGF0ZVZpc2l0b3JzKCkgew0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IHZpc2l0b3IgPSB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzW2ldOw0KICAgICAgICBjb25zdCB2aXNpdG9yVGl0bGUgPSBpID09PSAwID8gJ+S4u+iBlOezu+S6uicgOiBg6K6/5a6iJHtpICsgMX1gOw0KDQogICAgICAgIC8vIOmqjOivgeWnk+WQjQ0KICAgICAgICBpZiAoIXZpc2l0b3IubmFtZSB8fCB2aXNpdG9yLm5hbWUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOivt+i+k+WFpSR7dmlzaXRvclRpdGxlfeeahOWnk+WQjWApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh2aXNpdG9yLm5hbWUudHJpbSgpLmxlbmd0aCA8IDIgfHwgdmlzaXRvci5uYW1lLnRyaW0oKS5sZW5ndGggPiAyMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYCR7dmlzaXRvclRpdGxlfeeahOWnk+WQjemVv+W6puW6lOWcqDItMjDkuKrlrZfnrKbkuYvpl7RgKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDpqozor4HmiYvmnLrlj7cNCiAgICAgICAgaWYgKCF2aXNpdG9yLnBob25lIHx8IHZpc2l0b3IucGhvbmUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOivt+i+k+WFpSR7dmlzaXRvclRpdGxlfeeahOaJi+acuuWPt2ApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmqjOivgeaJi+acuuWPt+agvOW8j++8iOS4juWQjuerr+S/neaMgeS4gOiHtO+8iQ0KICAgICAgICBjb25zdCBwaG9uZVBhdHRlcm4gPSAvXjFbMy05XVxkezl9JC87DQogICAgICAgIGlmICghcGhvbmVQYXR0ZXJuLnRlc3QodmlzaXRvci5waG9uZS50cmltKCkpKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV955qE5omL5py65Y+35qC85byP5LiN5q2j56GuYCk7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6aqM6K+B6Lqr5Lu96K+B5Y+3DQogICAgICAgIGlmICghdmlzaXRvci5pZENhcmQgfHwgdmlzaXRvci5pZENhcmQudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOivt+i+k+WFpSR7dmlzaXRvclRpdGxlfeeahOi6q+S7veivgeWPt2ApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGlkQ2FyZCA9IHZpc2l0b3IuaWRDYXJkLnRyaW0oKS50b1VwcGVyQ2FzZSgpOw0KDQogICAgICAgIC8vIOWmguaenOaYrzE45L2N6Lqr5Lu96K+B5Y+377yM6aqM6K+B5qC85byPDQogICAgICAgIGlmIChpZENhcmQubGVuZ3RoID09PSAxOCkgew0KICAgICAgICAgIGNvbnN0IGlkUGF0dGVybiA9IC9eWzEtOV1cZHs1fSgxOHwxOXwyMClcZHsyfSgoMFsxLTldKXwoMVswLTJdKSkoKFswLTJdWzEtOV0pfDEwfDIwfDMwfDMxKVxkezN9WzAtOVh4XSQvOw0KICAgICAgICAgIGlmICghaWRQYXR0ZXJuLnRlc3QoaWRDYXJkKSkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV955qE6Lqr5Lu96K+B5Y+35qC85byP5LiN5q2j56GuYCk7DQogICAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qOA5p+l6Lqr5Lu96K+B5Y+35piv5ZCm6YeN5aSNDQogICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgdGhpcy5mb3JtRGF0YS52aXNpdG9ycy5sZW5ndGg7IGorKykgew0KICAgICAgICAgIGlmIChpICE9PSBqICYmIHZpc2l0b3IuaWRDYXJkLnRyaW0oKS50b1VwcGVyQ2FzZSgpID09PSB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzW2pdLmlkQ2FyZC50cmltKCkudG9VcHBlckNhc2UoKSkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV95LiO6K6/5a6iJHtqID09PSAwID8gJ+S4u+iBlOezu+S6uicgOiBqICsgMX3nmoTouqvku73or4Hlj7fph43lpI1gKTsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmo4Dmn6XmiYvmnLrlj7fmmK/lkKbph43lpI0NCiAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzLmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgaWYgKGkgIT09IGogJiYgdmlzaXRvci5waG9uZS50cmltKCkgPT09IHRoaXMuZm9ybURhdGEudmlzaXRvcnNbal0ucGhvbmUudHJpbSgpKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGAke3Zpc2l0b3JUaXRsZX3kuI7orr/lrqIke2ogPT09IDAgPyAn5Li76IGU57O75Lq6JyA6IGogKyAxfeeahOaJi+acuuWPt+mHjeWkje+8jOivt+ehruiupOaYr+WQpuS4uuWQjOS4gOS6umApOw0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa4heeQhuWSjOagh+WHhuWMluaVsOaNrg0KICAgICAgICB2aXNpdG9yLm5hbWUgPSB2aXNpdG9yLm5hbWUudHJpbSgpOw0KICAgICAgICB2aXNpdG9yLnBob25lID0gdmlzaXRvci5waG9uZS50cmltKCk7DQogICAgICAgIHZpc2l0b3IuaWRDYXJkID0gdmlzaXRvci5pZENhcmQudHJpbSgpLnRvVXBwZXJDYXNlKCk7DQogICAgICAgIHZpc2l0b3IuY29tcGFueSA9ICh2aXNpdG9yLmNvbXBhbnkgfHwgJycpLnRyaW0oKTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCg0KICAgIC8vIOmqjOivgeaXtumXtA0KICAgIHZhbGlkYXRlVGltZXMoKSB7DQogICAgICBpZiAoIXRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnBsYW5uZWRFbnRyeURhdGV0aW1lKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqemihOiuoeadpeiuv+aXtumXtCcpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEV4aXREYXRldGltZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6npooTorqHnprvlvIDml7bpl7QnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBlbnRyeVRpbWUgPSBuZXcgRGF0ZSh0aGlzLmZvcm1EYXRhLnZpc2l0SW5mby5wbGFubmVkRW50cnlEYXRldGltZSk7DQogICAgICBjb25zdCBleGl0VGltZSA9IG5ldyBEYXRlKHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnBsYW5uZWRFeGl0RGF0ZXRpbWUpOw0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCg0KICAgICAgLy8g5qOA5p+l5p2l6K6/5pe26Ze05LiN6IO95pep5LqO5b2T5YmN5pe26Ze0MeWwj+aXtuWJjQ0KICAgICAgY29uc3Qgb25lSG91ckJlZm9yZSA9IG5ldyBEYXRlKG5vdy5nZXRUaW1lKCkgLSA2MCAqIDYwICogMTAwMCk7DQogICAgICBpZiAoZW50cnlUaW1lIDwgb25lSG91ckJlZm9yZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfpooTorqHmnaXorr/ml7bpl7TkuI3og73ml6nkuo7lvZPliY3ml7bpl7Qx5bCP5pe25YmNJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l56a75byA5pe26Ze05b+F6aG75pma5LqO5p2l6K6/5pe26Ze0DQogICAgICBpZiAoZXhpdFRpbWUgPD0gZW50cnlUaW1lKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+mihOiuoeemu+W8gOaXtumXtOW/hemhu+aZmuS6juadpeiuv+aXtumXtCcpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIC8vIOajgOafpeiuv+mXruaXtumVv+S4jeiDvei2hei/hzI05bCP5pe2DQogICAgICBjb25zdCB2aXNpdER1cmF0aW9uID0gZXhpdFRpbWUuZ2V0VGltZSgpIC0gZW50cnlUaW1lLmdldFRpbWUoKTsNCiAgICAgIGNvbnN0IG1heER1cmF0aW9uID0gMjQgKiA2MCAqIDYwICogMTAwMDsgLy8gMjTlsI/ml7YNCiAgICAgIGlmICh2aXNpdER1cmF0aW9uID4gbWF4RHVyYXRpb24pIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y2V5qyh6K6/6Zeu5pe26ZW/5LiN6IO96LaF6L+HMjTlsI/ml7YnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KDQogICAgLy8g6YeN572u6KGo5Y2VDQogICAgcmVzZXRGb3JtKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB6YeN572u6KGo5Y2V5ZCX77yf5b2T5YmN5aGr5YaZ55qE5L+h5oGv5bCG5Lya5Lii5aSx44CCJywgJ+ehruiupOmHjee9ricsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g6YeN572u5omA5pyJ5pWw5o2uDQogICAgICAgIHRoaXMucmVnaXN0cmF0aW9uQ29tcGxldGVkID0gZmFsc2U7DQoNCiAgICAgICAgdGhpcy5mb3JtRGF0YSA9IHsNCiAgICAgICAgICB2aXNpdG9yczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgcGhvbmU6ICcnLA0KICAgICAgICAgICAgICBpZENhcmQ6ICcnLA0KICAgICAgICAgICAgICBjb21wYW55OiAnJywNCiAgICAgICAgICAgICAgaXNNYWluQ29udGFjdDogdHJ1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgdmlzaXRJbmZvOiB7DQogICAgICAgICAgICByZWFzb25Gb3JWaXNpdDogJycsDQogICAgICAgICAgICBob3N0RW1wbG95ZWVOYW1lOiAnJywNCiAgICAgICAgICAgIGRlcGFydG1lbnRWaXNpdGVkOiAnJywNCiAgICAgICAgICAgIHZlaGljbGVQbGF0ZU51bWJlcjogJycsDQogICAgICAgICAgICBwbGFubmVkRW50cnlEYXRldGltZTogJycsDQogICAgICAgICAgICBwbGFubmVkRXhpdERhdGV0aW1lOiAnJw0KICAgICAgICAgIH0NCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbklkID0gbnVsbDsNCg0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+ihqOWNleW3sumHjee9ricpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOaJk+WNsOWHreivgQ0KICAgIHByaW50Q3JlZGVudGlhbCgpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5omT5Y2w5Yqf6IO95byA5Y+R5LitLi4uJyk7DQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMluaXtumXtOaYvuekug0KICAgIHBhcnNlVGltZSh0aW1lKSB7DQogICAgICBpZiAoIXRpbWUpIHJldHVybiAnJzsNCiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lKTsNCiAgICAgIGlmIChpc05hTihkYXRlLmdldFRpbWUoKSkpIHJldHVybiAnJzsNCg0KICAgICAgY29uc3QgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBkYXkgPSBTdHJpbmcoZGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkYXRlLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBtaW51dGVzID0gU3RyaW5nKGRhdGUuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpOw0KDQogICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9ICR7aG91cnN9OiR7bWludXRlc31gOw0KICAgIH0sDQoNCiAgICAvLyDmmL7npLrluK7liqnkv6Hmga8NCiAgICBzaG93SGVscCgpIHsNCiAgICAgIHRoaXMuJGFsZXJ0KGANCiAgICAgICAgPGRpdiBzdHlsZT0idGV4dC1hbGlnbjogbGVmdDsiPg0KICAgICAgICAgIDxoND7kvb/nlKjor7TmmI48L2g0Pg0KICAgICAgICAgIDxwPjxzdHJvbmc+MS4g5aGr5YaZ5Z+65pys5L+h5oGvPC9zdHJvbmc+PC9wPg0KICAgICAgICAgIDx1bD4NCiAgICAgICAgICAgIDxsaT7or7flpoLlrp7loavlhpnmnaXorr/kuovnlLHjgIHooqvorr/kurrnrYnkv6Hmga88L2xpPg0KICAgICAgICAgICAgPGxpPuS4u+iBlOezu+S6uuS/oeaBr+W/hemhu+WHhuehru+8jOeUqOS6juWQjue7reiBlOezuzwvbGk+DQogICAgICAgICAgPC91bD4NCiAgICAgICAgICA8cD48c3Ryb25nPjIuIOa3u+WKoOWQjOihjOS6uuWRmDwvc3Ryb25nPjwvcD4NCiAgICAgICAgICA8dWw+DQogICAgICAgICAgICA8bGk+5aaC5pyJ5ZCM6KGM5Lq65ZGY77yM6K+354K55Ye7Iua3u+WKoOiuv+WuoiLmjInpkq48L2xpPg0KICAgICAgICAgICAgPGxpPuavj+S9jeiuv+WuoumDvemcgOimgeWhq+WGmeWujOaVtOi6q+S7veS/oeaBrzwvbGk+DQogICAgICAgICAgPC91bD4NCiAgICAgICAgICA8cD48c3Ryb25nPjMuIOmAieaLqeiuv+mXruaXtumXtDwvc3Ryb25nPjwvcD4NCiAgICAgICAgICA8dWw+DQogICAgICAgICAgICA8bGk+6K+35ZCI55CG5a6J5o6S6K6/6Zeu5pe26Ze077yM6YG/5YWN6L+H6ZW/6YCX55WZPC9saT4NCiAgICAgICAgICAgIDxsaT7lpoLpnIDlu7bplb/orr/pl67ml7bpl7TvvIzor7fogZTns7vooqvorr/kuro8L2xpPg0KICAgICAgICAgIDwvdWw+DQogICAgICAgICAgPHA+PHN0cm9uZz40LiDlrqHmoLjkuI7pgJrooYw8L3N0cm9uZz48L3A+DQogICAgICAgICAgPHVsPg0KICAgICAgICAgICAgPGxpPuaPkOS6pOWQjuetieW+heWuoeaguO+8jOmAmui/h+WQjuWPr+WHreS6jOe7tOeggei/m+WFpeWbreWMujwvbGk+DQogICAgICAgICAgICA8bGk+6K+35oyJ54Wn6aKE57qm5pe26Ze05YeG5pe25Yiw6K6/PC9saT4NCiAgICAgICAgICA8L3VsPg0KICAgICAgICA8L2Rpdj4NCiAgICAgIGAsICfkvb/nlKjluK7liqknLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn55+l6YGT5LqGJywNCiAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g6IGU57O75a6i5pyNDQogICAgY29udGFjdFNlcnZpY2UoKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfpnIDopoHluK7liqnlkJfvvJ8nLCAn6IGU57O75a6i5pyNJywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+aLqOaJk+eUteivnScsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnaW5mbycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAvLyDov5nph4zlj6/ku6Xlrp7njrDmi6jmiZPlrqLmnI3nlLXor53nmoTlip/og70NCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgIG1lc3NhZ2U6ICflrqLmnI3nlLXor53vvJo0MDAteHh4LXh4eHgnDQogICAgICAgIH0pOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAvLyDnlKjmiLflj5bmtogNCiAgICAgIH0pOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, null]}