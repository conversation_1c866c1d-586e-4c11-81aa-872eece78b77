<template>
  <view class="list-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <uni-easyinput 
        v-model="searchKeyword" 
        placeholder="搜索访客姓名、电话或被访人"
        prefixIcon="search"
        :clearable="true"
        @confirm="handleSearch"
        @clear="handleSearch"
      />
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <!-- 日期选择 -->
      <view class="date-picker">
        <uni-datetime-picker 
          v-model="searchDate" 
          type="date" 
          :clearable="true"
          placeholder="选择日期"
          @change="handleDateChange"
        />
      </view>
      
      <!-- 状态筛选 -->
      <uni-data-checkbox 
        v-model="statusFilter"
        :localdata="statusOptions"
        mode="button"
        @change="handleFilterChange"
      />
    </view>

    <!-- 访客列表 -->
    <view class="visitor-list">
      <uni-list v-if="visitorList.length > 0">
        <uni-list-item 
          v-for="visitor in visitorList.filter(v => v && v.registrationId)" 
          :key="visitor.registrationId"
          :title="visitor.primaryContactName || '未知访客'"
          :note="`${visitor.reasonForVisit || '无事由'} | ${visitor.primaryContactPhone || '无联系方式'}`"
          :rightText="getStatusText(visitor.status)"
          :clickable="true"
          @click="viewVisitorDetail(visitor)"
        >
          <template v-slot:header>
            <view class="visitor-avatar">
              <text class="avatar-text">{{ (visitor.primaryContactName || '未').charAt(0) }}</text>
            </view>
          </template>
          <template v-slot:footer>
            <view class="visitor-info">
              <text class="visit-purpose">被访人：{{ visitor.hostEmployeeName || '未指定' }}</text>
              <text class="visit-time">预计来访：{{ formatTime(visitor.plannedEntryDatetime) }}</text>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <uni-icons type="person" size="60" color="#ccc"></uni-icons>
        <text class="empty-text">暂无访客记录</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore" class="load-more" @click="loadMore">
      <text class="load-more-text">{{ loading ? '加载中...' : '加载更多' }}</text>
    </view>

    <!-- 浮动按钮 -->
    <view class="fab" @click="goToRegistration">
      <uni-icons type="plus" size="24" color="white"></uni-icons>
    </view>
  </view>
</template>

<script>
import { listRegistrationInfo, getCurrentVisitors } from '@/api/system/visitor'

export default {
  data() {
    return {
      searchKeyword: '',
      searchDate: '',
      statusFilter: '',
      visitorList: [],
      loading: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 20,
      total: 0,
      statusOptions: [
        { value: '', text: '全部' },
        { value: 'pending', text: '待审核' },
        { value: 'approved', text: '已通过' },
        { value: 'rejected', text: '已拒绝' },
        { value: 'cancelled', text: '已取消' },
        { value: 'completed', text: '已完成' }
      ]
    };
  },
  onLoad(options) {
    uni.setNavigationBarTitle({
      title: '登记列表'
    });
    
    this.loadVisitorList();
  },
  onPullDownRefresh() {
    this.refreshList();
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore();
    }
  },
  methods: {
    // 加载访客列表
    async loadVisitorList(isLoadMore = false) {
      if (this.loading) return;
      
      this.loading = true;
      
      try {
        const params = {
          pageNum: isLoadMore ? this.pageNum + 1 : 1,
          pageSize: this.pageSize
        };
        
        // 添加搜索条件
        if (this.searchKeyword) {
          params.primaryContactName = this.searchKeyword;
          params.primaryContactPhone = this.searchKeyword;
          params.hostEmployeeName = this.searchKeyword;
        }
        
        // 添加状态筛选
        if (this.statusFilter) {
          params.status = this.statusFilter;
        }
        
        // 添加日期筛选
        if (this.searchDate) {
          // 使用正确的参数名进行日期过滤
          params.params = {
            beginTime: this.searchDate,
            endTime: this.searchDate
          };
        }
        
        const response = await listRegistrationInfo(params);
        
        if (response.code === 200) {
          const newList = response.rows || [];
          
          if (isLoadMore) {
            this.visitorList = [...this.visitorList, ...newList];
            this.pageNum += 1;
          } else {
            this.visitorList = newList;
            this.pageNum = 1;
          }
          
          this.total = response.total || 0;
          this.hasMore = this.visitorList.length < this.total;
        } else {
          this.$modal.showError(response.msg || '加载失败');
        }
      } catch (error) {
        console.error('加载访客列表失败:', error);
        this.$modal.showError('加载失败，请检查网络连接');
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },

    // 搜索处理
    handleSearch() {
      this.loadVisitorList();
    },

    // 筛选变更处理
    handleFilterChange() {
      this.loadVisitorList();
    },

    // 日期变更处理
    handleDateChange() {
      this.loadVisitorList();
    },

    // 刷新列表
    refreshList() {
      this.pageNum = 1;
      this.hasMore = true;
      this.loadVisitorList();
    },

    // 加载更多
    loadMore() {
      this.loadVisitorList(true);
    },

    // 查看访客详情
    viewVisitorDetail(visitor) {
      // 增加健壮性判断和调试输出
      if (!visitor) {
        console.warn('viewVisitorDetail: visitor is undefined', visitor);
        this.$modal && this.$modal.showError
          ? this.$modal.showError('无效的访客数据')
          : alert('无效的访客数据');
        return;
      }
      const visitorId = visitor.registrationId;
      if (!visitorId) {
        console.warn('viewVisitorDetail: registrationId is missing', visitor);
        this.$modal && this.$modal.showError
          ? this.$modal.showError('访客ID缺失，无法查看详情')
          : alert('访客ID缺失，无法查看详情');
        return;
      }
      uni.navigateTo({
        url: `/subpackages/visitor/detail?registrationId=${visitorId}`,
        fail: (err) => {
          console.error('跳转到访客详情失败:', err, visitor);
          this.$modal && this.$modal.showError
            ? this.$modal.showError('页面跳转失败')
            : alert('页面跳转失败');
        }
      });
    },

    // 跳转到访客登记
    goToRegistration() {
      uni.navigateTo({
        url: '/subpackages/visitor/registration',
        fail: (err) => {
          console.error('跳转到访客登记失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝',
        'cancelled': '已取消',
        'completed': '已完成',
        'in_progress': '进行中',
        'checked_in': '已签入',
        'checked_out': '已签出',
        // 兼容大写格式
        'PENDING': '待审核',
        'APPROVED': '已通过',
        'REJECTED': '已拒绝',
        'CANCELLED': '已取消',
        'COMPLETED': '已完成',
        'IN_PROGRESS': '进行中',
        'CHECKED_IN': '已签入',
        'CHECKED_OUT': '已签出'
      };
      return statusMap[status] || '未知';
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      
      const date = new Date(timeStr);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      
      if (targetDate.getTime() === today.getTime()) {
        // 今天
        return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } else {
        // 其他日期
        return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px; // 为浮动按钮留出空间
}

.search-bar {
  padding: 15px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.filter-bar {
  padding: 10px 15px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  
  .date-picker {
    margin-bottom: 10px;
  }
  
  :deep(.uni-data-checkbox) {
    .checklist-group {
      display: flex;
      gap: 10px;
      
      .checklist-box {
        border-radius: 15px;
        padding: 5px 12px;
        font-size: 12px;
        
        &.is--checked {
          background-color: #667eea;
          border-color: #667eea;
          color: white;
        }
      }
    }
  }
}

.visitor-list {
  margin-top: 10px;
  
  :deep(.uni-list-item) {
    .uni-list-item__container {
      padding: 15px;
      
      .uni-list-item__content {
        .uni-list-item__content-title {
          font-weight: bold;
          font-size: 16px;
        }
        
        .uni-list-item__content-note {
          color: #666;
          font-size: 12px;
        }
      }
      
      .uni-list-item__extra {
        .uni-list-item__extra-text {
          color: #667eea;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }
  }
}

.visitor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  
  .avatar-text {
    color: white;
    font-weight: bold;
    font-size: 16px;
  }
}

.visitor-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  
  .visit-purpose {
    font-size: 12px;
    color: #999;
    margin-bottom: 2px;
  }
  
  .visit-time {
    font-size: 11px;
    color: #ccc;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  
  .empty-text {
    display: block;
    margin-top: 15px;
    color: #999;
    font-size: 14px;
  }
}

.load-more {
  text-align: center;
  padding: 20px;
  
  .load-more-text {
    color: #666;
    font-size: 14px;
  }
}

.fab {
  position: fixed;
  right: 20px;
  bottom: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  z-index: 999;
  
  &:active {
    transform: scale(0.95);
  }
}
</style>
