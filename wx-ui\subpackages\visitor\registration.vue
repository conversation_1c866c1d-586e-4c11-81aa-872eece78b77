<template>
  <view class="container">
    <!-- 表单区域 -->
    <uni-forms ref="visitorForm" :model="formData" :rules="rules" label-width="100px">
      <!-- 访问信息 -->
      <uni-card title="访问信息" :is-shadow="false" margin="15px 0">
        <uni-forms-item label="来访事由" name="visitInfo.reasonForVisit" required>
          <uni-easyinput
            v-model="formData.visitInfo.reasonForVisit"
            placeholder="请输入来访事由"
            type="textarea"
            :auto-height="true"
            :clearable="true"
            maxlength="200"
            @blur="validateField('visitInfo.reasonForVisit')"
          />
        </uni-forms-item>

        <uni-forms-item label="被访人姓名" name="visitInfo.hostEmployeeName" required>
          <uni-easyinput
            v-model="formData.visitInfo.hostEmployeeName"
            placeholder="请输入被访人姓名"
            :clearable="true"
            @blur="validateField('visitInfo.hostEmployeeName')"
          />
        </uni-forms-item>

        <uni-forms-item label="被访部门" name="visitInfo.departmentVisited" required>
          <uni-easyinput
            v-model="formData.visitInfo.departmentVisited"
            placeholder="请输入被访部门"
            :clearable="true"
            @blur="validateField('visitInfo.departmentVisited')"
          />
        </uni-forms-item>

        <uni-forms-item label="车牌号" name="visitInfo.vehiclePlateNumber">
          <uni-easyinput
            v-model="formData.visitInfo.vehiclePlateNumber"
            placeholder="如有车辆请填写车牌号，多个用逗号分隔"
            :clearable="true"
          />
        </uni-forms-item>

        <uni-forms-item label="预计来访时间" name="visitInfo.plannedEntryDatetime" required>
          <uni-datetime-picker
            v-model="formData.visitInfo.plannedEntryDatetime"
            type="datetime"
            :clear-icon="false"
            placeholder="选择预计来访时间"
            @change="onArrivalTimeChange"
          />
        </uni-forms-item>

        <uni-forms-item label="预计离开时间" name="visitInfo.plannedExitDatetime" required>
          <uni-datetime-picker
            v-model="formData.visitInfo.plannedExitDatetime"
            type="datetime"
            :clear-icon="false"
            placeholder="选择预计离开时间"
            @change="onDepartureTimeChange"
          />
        </uni-forms-item>
      </uni-card>

      <!-- 访客信息 -->
      <uni-card title="访客信息" :is-shadow="false" margin="15px 0">
        <view class="visitor-header">
          <text class="visitor-count">访客列表（第一位为主联系人）共 {{ formData.visitors.length }} 人</text>
          <button class="add-visitor-btn" @click="addVisitor" size="mini" type="primary">
            <uni-icons type="plus" size="14"></uni-icons>
            添加访客
          </button>
        </view>

        <view v-for="(visitor, index) in formData.visitors" :key="index" class="visitor-item">
          <view class="visitor-header-item">
            <text class="visitor-title">
              {{ index === 0 ? '主联系人' : `访客 ${index + 1}` }}
            </text>
            <button
              v-if="index > 0"
              class="remove-visitor-btn"
              @click="removeVisitor(index)"
              size="mini"
              type="warn"
            >
              <uni-icons type="trash" size="12"></uni-icons>
            </button>
          </view>

          <uni-forms-item :label="`姓名`" :name="`visitors.${index}.name`" required>
            <uni-easyinput
              v-model="visitor.name"
              placeholder="请输入姓名"
              :clearable="true"
              @blur="validateVisitorField(index, 'name')"
            />
          </uni-forms-item>

          <uni-forms-item :label="`手机号`" :name="`visitors.${index}.phone`" required>
            <uni-easyinput
              v-model="visitor.phone"
              placeholder="请输入手机号"
              type="number"
              :clearable="true"
              @blur="validateVisitorField(index, 'phone')"
            />
          </uni-forms-item>

          <uni-forms-item :label="`身份证号`" :name="`visitors.${index}.idCard`" required>
            <uni-easyinput
              v-model="visitor.idCard"
              placeholder="请输入身份证号"
              :clearable="true"
            />
          </uni-forms-item>

          <uni-forms-item :label="`公司名称`" :name="`visitors.${index}.company`">
            <uni-easyinput
              v-model="visitor.company"
              placeholder="请输入公司名称"
              :clearable="true"
            />
          </uni-forms-item>
        </view>
      </uni-card>
    </uni-forms>

    <!-- 操作按钮 -->
    <view class="button-group">
      <button class="submit-btn" type="primary" @click="submitForm" :loading="submitting">
        {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}
      </button>
      <button class="reset-btn" @click="resetForm">重置表单</button>
    </view>
  </view>
</template>

<script>
import { submitVisitorRegistration, getVisitorByIdCard } from '@/api/system/visitor'

export default {
  data() {
    return {
      submitting: false,
      formData: {
        // 访客列表（第一位为主联系人）
        visitors: [
          {
            name: '',
            phone: '',
            idCard: '',
            company: '',
            isMainContact: true
          }
        ],
        // 来访信息
        visitInfo: {
          reasonForVisit: '',
          hostEmployeeName: '',
          departmentVisited: '',
          vehiclePlateNumber: '',
          plannedEntryDatetime: '',
          plannedExitDatetime: ''
        }
      },

      // 表单验证规则
      rules: {
        'visitInfo.reasonForVisit': {
          rules: [
            { required: true, errorMessage: '请输入来访事由' },
            { minLength: 2, maxLength: 200, errorMessage: '来访事由长度应在2-200个字符之间' }
          ]
        },
        'visitInfo.hostEmployeeName': {
          rules: [
            { required: true, errorMessage: '请输入被访人姓名' },
            { minLength: 2, maxLength: 20, errorMessage: '被访人姓名长度应在2-20个字符之间' }
          ]
        },
        'visitInfo.departmentVisited': {
          rules: [
            { required: true, errorMessage: '请输入被访部门' },
            { minLength: 2, maxLength: 50, errorMessage: '部门名称长度应在2-50个字符之间' }
          ]
        },
        'visitInfo.plannedEntryDatetime': {
          rules: [
            { required: true, errorMessage: '请选择预计来访时间' }
          ]
        },
        'visitInfo.plannedExitDatetime': {
          rules: [
            { required: true, errorMessage: '请选择预计离开时间' }
          ]
        }
      }
    };
  },
  computed: {
    // 总访客人数
    totalVisitors() {
      return this.formData.visitors.length;
    }
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '来访人员登记'
    });
  },
  methods: {
    // 验证单个字段
    validateField(field) {
      this.$refs.visitorForm.validateField(field);
    },

    // 验证访客字段
    validateVisitorField(index, field) {
      this.$refs.visitorForm.validateField(`visitors.${index}.${field}`);
    },

    // 添加访客
    addVisitor() {
      if (this.formData.visitors.length >= 10) {
        this.$modal.alert('最多只能添加10名访客');
        return;
      }

      this.formData.visitors.push({
        name: '',
        phone: '',
        idCard: '',
        company: '',
        isMainContact: false
      });

      this.$modal.showSuccess('已添加访客');
    },

    // 移除访客
    removeVisitor(index) {
      if (index === 0) {
        this.$modal.alert('不能删除主联系人');
        return;
      }

      this.formData.visitors.splice(index, 1);
      this.$modal.showSuccess('已移除访客');
    },    
    // 格式化日期为后端期望的格式
    formatDateForBackend(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 格式化日期字符串，兼容iOS
    formatDateForIOS(dateStr) {
      if (!dateStr) return dateStr;
      // 将 "2025-06-27 15:18:38" 格式转换为 "2025/06/27 15:18:38" 或 ISO 格式
      return dateStr.replace(/-/g, '/');
    },

    // 来访时间变更事件
    onArrivalTimeChange(value) {
      console.log('预计来访时间变更:', value);
      // 自动设置离开时间为来访时间后4小时
      if (value && !this.formData.visitInfo.plannedExitDatetime) {
        const arrivalTime = new Date(this.formatDateForIOS(value));
        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);
        // 转换为本地时间字符串，uni-datetime-picker 需要这种格式
        const localTimeStr = departureTime.getFullYear() + '-' + 
          String(departureTime.getMonth() + 1).padStart(2, '0') + '-' + 
          String(departureTime.getDate()).padStart(2, '0') + ' ' + 
          String(departureTime.getHours()).padStart(2, '0') + ':' + 
          String(departureTime.getMinutes()).padStart(2, '0') + ':' + 
          String(departureTime.getSeconds()).padStart(2, '0');
        this.formData.visitInfo.plannedExitDatetime = localTimeStr;
      }
    },

    // 离开时间变更事件
    onDepartureTimeChange(value) {
      console.log('预计离开时间变更:', value);
      // 验证离开时间不能早于来访时间
      if (value && this.formData.visitInfo.plannedEntryDatetime) {
        const arrivalTime = new Date(this.formatDateForIOS(this.formData.visitInfo.plannedEntryDatetime));
        const departureTime = new Date(this.formatDateForIOS(value));

        if (departureTime <= arrivalTime) {
          this.$modal.alert('预计离开时间不能早于或等于来访时间');
          this.formData.visitInfo.plannedExitDatetime = '';
        }
      }
    },

    // 提交表单
    async submitForm() {
      try {
        // 表单验证
        const valid = await this.$refs.visitorForm.validate();
        if (!valid) {
          this.$modal.alert('请完善表单信息');
          return;
        }

        // 验证访客信息
        if (!this.validateVisitors()) {
          return;
        }

        // 验证时间
        if (!this.validateTimes()) {
          return;
        }

        this.submitting = true;
        this.$modal.showLoading(`正在登记${this.totalVisitors}名访客...`);

        // 准备提交数据 - 按照后端期望的数据结构
        const mainContact = this.formData.visitors[0];
        
        const submitData = {
          // VisitRegistrations 对象
          registration: {
            primaryContactName: mainContact.name.trim(),
            primaryContactPhone: mainContact.phone.trim(),
            reasonForVisit: this.formData.visitInfo.reasonForVisit,
            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,
            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理
            departmentVisited: this.formData.visitInfo.departmentVisited,
            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),
            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),
            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',
            totalCompanions: this.formData.visitors.length - 1
          },
          // RegistrationAttendees 数组 - 使用后端期望的临时字段
          attendeesList: this.formData.visitors.map((visitor, index) => ({
            visitorName: visitor.name.trim(),
            visitorPhone: visitor.phone.trim(),
            visitorIdCard: visitor.idCard.trim().toUpperCase(),
            visitorCompany: (visitor.company || '').trim(),
            isPrimary: index === 0 ? "1" : "0", // 第一个访客必须是主联系人
            visitorAvatarPhoto: null // 暂时不支持头像上传
          }))
        };

        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));
        
        // 调用API提交数据
        const response = await submitVisitorRegistration(submitData);

        if (response.code === 200) {
          this.$modal.showSuccess(`${this.totalVisitors}名访客登记成功！`);

          // 询问是否打印访客凭证
          const printResult = await this.$modal.confirm(`登记成功！是否需要打印${this.totalVisitors}名访客的凭证？`);
          if (printResult) {
            this.printTickets(response.data);
          }

          // 重置表单
          this.resetForm();
        } else {
          this.$modal.alert(response.msg || '登记失败，请重试');
        }
      } catch (error) {
        console.error('提交表单失败:', error);
        this.$modal.showError('登记失败，请检查网络连接');
      } finally {
        this.submitting = false;
        this.$modal.hideLoading();
      }
    },

    // 验证访客信息
    validateVisitors() {
      for (let i = 0; i < this.formData.visitors.length; i++) {
        const visitor = this.formData.visitors[i];
        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;

        // 验证姓名
        if (!visitor.name || visitor.name.trim() === '') {
          this.$modal.alert(`请输入${visitorTitle}的姓名`);
          return false;
        }
        
        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {
          this.$modal.alert(`${visitorTitle}的姓名长度应在2-20个字符之间`);
          return false;
        }

        // 验证手机号
        if (!visitor.phone || visitor.phone.trim() === '') {
          this.$modal.alert(`请输入${visitorTitle}的手机号`);
          return false;
        }

        // 验证手机号格式（与后端保持一致）
        const phonePattern = /^1[3-9]\d{9}$/;
        if (!phonePattern.test(visitor.phone.trim())) {
          this.$modal.alert(`${visitorTitle}的手机号格式不正确`);
          return false;
        }

        // 验证身份证号（如果提供的话，与后端保持一致）
        if (!visitor.idCard || visitor.idCard.trim() === '') {
          this.$modal.alert(`请输入${visitorTitle}的身份证`);
          return false;
        }
        
        const idCard = visitor.idCard.trim().toUpperCase();
        
        // 如果是18位身份证号，验证格式
        if (idCard.length === 18) {
          const idPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
          if (!idPattern.test(idCard)) {
            this.$modal.alert(`${visitorTitle}的身份证号格式不正确`);
            return false;
          }
        }

        // 验证姓名格式（只允许中文、英文字母和常见符号，与后端保持一致）
        const namePattern = /^[\u4e00-\u9fa5a-zA-Z·\s]+$/;
        if (!namePattern.test(visitor.name.trim())) {
          this.$modal.alert(`${visitorTitle}的姓名只能包含中文、英文字母和常见符号`);
          return false;
        }

        // 检查身份证号是否重复
        for (let j = 0; j < this.formData.visitors.length; j++) {
          if (i !== j && visitor.idCard.trim() === this.formData.visitors[j].idCard.trim()) {
            const otherTitle = j === 0 ? '主联系人' : `访客${j + 1}`;
            this.$modal.alert(`${visitorTitle}与${otherTitle}的身份证号重复`);
            return false;
          }
        }

        // 检查手机号是否重复（避免后端创建重复访客记录）
        for (let j = 0; j < this.formData.visitors.length; j++) {
          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {
            const otherTitle = j === 0 ? '主联系人' : `访客${j + 1}`;
            this.$modal.alert(`${visitorTitle}与${otherTitle}的手机号重复，请确认是否为同一人`);
            return false;
          }
        }
        
        // 清理和标准化数据
        visitor.name = visitor.name.trim();
        visitor.phone = visitor.phone.trim();
        visitor.idCard = visitor.idCard.trim().toUpperCase();
        visitor.company = (visitor.company || '').trim();
      }

      // 清理访问信息并验证
      this.formData.visitInfo.reasonForVisit = (this.formData.visitInfo.reasonForVisit || '').trim();
      this.formData.visitInfo.hostEmployeeName = (this.formData.visitInfo.hostEmployeeName || '').trim();
      this.formData.visitInfo.departmentVisited = (this.formData.visitInfo.departmentVisited || '').trim();
      this.formData.visitInfo.vehiclePlateNumber = (this.formData.visitInfo.vehiclePlateNumber || '').trim();

      // 验证访问事由长度（与后端保持一致，最大200字符）
      if (this.formData.visitInfo.reasonForVisit.length > 200) {
        this.$modal.alert('来访事由不能超过200个字符');
        return false;
      }

      if (this.formData.visitInfo.reasonForVisit.length === 0) {
        this.$modal.alert('请输入来访事由');
        return false;
      }

      return true;
    },    // 验证时间
    validateTimes() {
      if (!this.formData.visitInfo.plannedEntryDatetime) {
        this.$modal.alert('请选择预计来访时间');
        return false;
      }

      if (!this.formData.visitInfo.plannedExitDatetime) {
        this.$modal.alert('请选择预计离开时间');
        return false;
      }

      const arrivalTime = new Date(this.formatDateForIOS(this.formData.visitInfo.plannedEntryDatetime));
      const departureTime = new Date(this.formatDateForIOS(this.formData.visitInfo.plannedExitDatetime));
      const now = new Date();

      // if (arrivalTime <= now) {
      //   this.$modal.alert('预计来访时间不能早于当前时间');
      //   return false;
      // }

      if (departureTime <= arrivalTime) {
        this.$modal.alert('预计离开时间不能早于或等于来访时间');
        return false;
      }

      return true;
    },

    // 打印访客凭证
    async printTickets(registrationData) {
      try {
        this.$modal.showLoading('准备打印...');
        // 这里可以调用打印API或跳转到打印页面
        // 可以根据registrationData中的访客信息批量打印
        this.$modal.showSuccess('打印任务已发送');
      } catch (error) {
        console.error('打印失败:', error);
        this.$modal.alert('打印失败，请稍后重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        // 访客列表（第一位为主联系人）
        visitors: [
          {
            name: '',
            phone: '',
            idCard: '',
            company: '',
            isMainContact: true
          }
        ],
        // 来访信息
        visitInfo: {
          reasonForVisit: '',
          hostEmployeeName: '',
          departmentVisited: '',
          vehiclePlateNumber: '',
          plannedEntryDatetime: '',
          plannedExitDatetime: ''
        }
      };
      this.$refs.visitorForm.clearValidate();
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.button-group {
  margin-top: 30px;
  padding: 0 15px 30px;

  .submit-btn {
    width: 100%;
    height: 50px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;

    &:active {
      opacity: 0.8;
    }
  }

  .reset-btn {
    width: 100%;
    height: 45px;
    border-radius: 22px;
    font-size: 14px;
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;

    &:active {
      background-color: #e9ecef;
    }
  }
}

// 表单样式优化
:deep(.uni-forms-item) {
  margin-bottom: 20px;

  .uni-forms-item__label {
    font-weight: 500;
    color: #333;
  }

  .uni-forms-item__content {
    .uni-easyinput {
      .uni-easyinput__content {
        border-radius: 8px;
        border-color: #e0e0e0;

        &:focus-within {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }
    }
  }
}

// 卡片样式优化
:deep(.uni-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .uni-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;

    .uni-card__header-title {
      color: white;
      font-weight: bold;
    }
  }

  .uni-card__content {
    padding: 20px;
  }
}

// 访客样式
.visitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .visitor-count {
    font-size: 14px;
    color: #666;
    flex: 1;
  }

  .add-visitor-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 12px;
    background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
    border: none;
    color: white;

    &:active {
      opacity: 0.8;
    }
  }
}

.visitor-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fafafa;

  &:first-child {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-color: #667eea;
  }

  .visitor-header-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .visitor-title {
      font-weight: bold;
      color: #333;
      font-size: 14px;

      &:first-child {
        color: #667eea;
      }
    }

    .remove-visitor-btn {
      padding: 4px 8px;
      border-radius: 10px;
      font-size: 10px;
      background-color: #F56C6C;
      border: none;
      color: white;
      display: flex;
      align-items: center;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

// 数据选择器样式
:deep(.uni-data-checkbox) {
  .checklist-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .checklist-box {
      border-radius: 20px;
      border: 1px solid #e0e0e0;
      padding: 8px 16px;
      font-size: 14px;
      transition: all 0.3s ease;

      &.is--checked {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
      }

      &:not(.is--checked):active {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>