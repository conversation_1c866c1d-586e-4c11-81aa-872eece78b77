<template>
  <view class="container">
    <view class="header">
      <view class="title">EMS能源管理系统</view>
      <view class="subtitle">Energy Management System</view>
    </view>
    
    <view class="content">
      <view class="card-list">
        <view class="card" @click="navigateToDetail">
          <view class="card-icon">
            <uni-icons type="bars" size="24" color="#007AFF"></uni-icons>
          </view>
          <view class="card-content">
            <view class="card-title">能源监控</view>
            <view class="card-desc">实时监控能源使用情况</view>
          </view>
          <view class="card-arrow">
            <uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
          </view>
        </view>
        
        <view class="card" @click="navigateToCharts">
          <view class="card-icon">
            <uni-icons type="chart" size="24" color="#67C23A"></uni-icons>
          </view>
          <view class="card-content">
            <view class="card-title">数据分析</view>
            <view class="card-desc">查看能源使用统计图表</view>
          </view>
          <view class="card-arrow">
            <uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'EmsIndex',
  data() {
    return {
      
    }
  },
  methods: {
    navigateToDetail() {
      uni.navigateTo({
        url: '/subpackages/ems/detail'
      })
    },
    navigateToCharts() {
      uni.navigateTo({
        url: '/subpackages/charts/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    font-size: 24rpx;
    color: #666;
  }
}

.content {
  .card-list {
    .card {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      
      .card-icon {
        margin-right: 20rpx;
      }
      
      .card-content {
        flex: 1;
        
        .card-title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .card-desc {
          font-size: 26rpx;
          color: #666;
        }
      }
      
      .card-arrow {
        margin-left: 20rpx;
      }
    }
  }
}
</style>
