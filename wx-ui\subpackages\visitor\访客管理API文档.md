# 访客管理 API 文档

## 概述

访客管理模块提供完整的访客登记、审核、通行管理功能。支持多访客同时登记、安全防护、审核流程等核心业务功能。

### 基础信息
- **基础路径**: `/asc/visitor/registration`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 状态码说明
- `200`: 成功
- `500`: 服务器内部错误
- `401`: 未授权
- `403`: 权限不足

### 通用响应格式

```json
{
  "msg": "操作成功",
  "code": 200,
  "data": {}
}
```

---

## 访客登记相关接口

### 1. 新增访客登记（匿名接口）

**POST** `/asc/visitor/registration`

> **重要**: 这是核心业务接口，支持多访客同时登记，包含完整的安全防护机制

#### 请求头
```
Content-Type: application/json
```

#### 请求参数

```json
{
  "registration": {
    "primaryContactName": "张三",
    "primaryContactPhone": "13800138000",
    "reasonForVisit": "商务洽谈",
    "hostEmployeeName": "李四",
    "hostEmployeeId": 1001,
    "departmentVisited": "研发部",
    "plannedEntryDatetime": "2025-06-27 10:00:00",
    "plannedExitDatetime": "2025-06-27 18:00:00",
    "vehiclePlateNumber": "京A12345,京B67890",
    "totalCompanions": 1
  },
  "attendeesList": [
    {
      "visitorName": "张三",
      "visitorPhone": "13800138000",
      "visitorIdCard": "110101199001011234",
      "visitorCompany": "XX科技有限公司",
      "isPrimary": "1",
      "visitorAvatarPhoto": null
    },
    {
      "visitorName": "王五",
      "visitorPhone": "13900139000",
      "visitorIdCard": "110101199002021234", 
      "visitorCompany": "XX科技有限公司",
      "isPrimary": "0",
      "visitorAvatarPhoto": null
    }
  ]
}
```

#### 字段说明

**registration (访问登记信息 - VisitRegistrations)**
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| primaryContactName | string | ✓ | 主联系人姓名 |
| primaryContactPhone | string | ✓ | 主联系人手机号 |
| reasonForVisit | string | ✓ | 来访事由 |
| hostEmployeeName | string | ✓ | 被访人姓名 |
| hostEmployeeId | number | ✓ | 被访人用户ID |
| departmentVisited | string | ✓ | 被访部门 |
| plannedEntryDatetime | string | ✓ | 预计来访时间 (yyyy-MM-dd HH:mm:ss) |
| plannedExitDatetime | string | ✓ | 预计离开时间 (yyyy-MM-dd HH:mm:ss) |
| vehiclePlateNumber | string | ✗ | 车牌号，多个用逗号分隔 |
| totalCompanions | number | ✓ | 同行人数(不含主访客) |

**attendeesList (参与人列表 - RegistrationAttendees)**
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| visitorName | string | ✓ | 访客姓名 |
| visitorPhone | string | ✓ | 访客手机号 |
| visitorIdCard | string | ✓ | 访客身份证号 |
| visitorCompany | string | ✓ | 访客所属公司 |
| isPrimary | string | ✓ | 是否为主联系人 ("1"-是, "0"-否) |
| visitorAvatarPhoto | string | ✗ | 访客头像照片数据 (Base64编码) |

#### 成功响应

```json
{
  "msg": "访客登记成功，等待审核",
  "code": 200,
  "data": null
}
```

#### 错误响应

```json
{
  "msg": "请求参数不能为空",
  "code": 500,
  "data": null
}
```

#### 安全限制
- IP限制: 每小时最多5次登记
- 手机号限制: 每天最多3次登记  
- 访客数量限制: 每次最多20人
- 访问时间限制: 最多提前30天预约
- 包含敏感词检查和重复身份证检查

---

### 2. 查询访客登记列表

**GET** `/asc/visitor/registration/list`

#### 权限要求
- 权限标识: `asc:registration:list`

#### 请求参数 (Query)

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | number | ✗ | 页码，默认1 |
| pageSize | number | ✗ | 每页数量，默认10 |
| primaryContactName | string | ✗ | 主联系人姓名(模糊查询) |
| primaryContactPhone | string | ✗ | 主联系人手机号 |
| status | string | ✗ | 状态筛选 |
| hostEmployeeName | string | ✗ | 被访人姓名(模糊查询) |
| departmentVisited | string | ✗ | 被访部门(模糊查询) |

#### 状态值说明
- `pending`: 待审核
- `approved`: 已批准
- `rejected`: 已拒绝
- `cancelled`: 已取消
- `completed`: 已完成

#### 成功响应

```json
{
  "msg": "查询成功",
  "code": 200,
  "rows": [
    {
      "registrationId": 1001,
      "primaryContactName": "张三",
      "primaryContactPhone": "13800138000",
      "reasonForVisit": "商务洽谈",
      "hostEmployeeName": "李四",
      "hostEmployeeId": 1001,
      "departmentVisited": "研发部",
      "plannedEntryDatetime": "2025-06-27 10:00:00",
      "plannedExitDatetime": "2025-06-27 18:00:00",
      "vehiclePlateNumber": "京A12345",
      "totalCompanions": 1,
      "submittedDatetime": "2025-06-26 15:30:00",
      "status": "pending",
      "approvalStaffName": null,
      "approvalDatetime": null,
      "rejectionReason": null
    }
  ],
  "total": 1
}
```

---

### 3. 查询访客登记详情

**GET** `/asc/visitor/registration/{registrationId}`

#### 权限要求
- 权限标识: `asc:registration:query`

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| registrationId | number | ✓ | 登记ID |

#### 成功响应

```json
{
  "msg": "查询成功",
  "code": 200,
  "data": {
    "registrationId": 1001,
    "primaryContactName": "张三",
    "primaryContactPhone": "13800138000",
    "reasonForVisit": "商务洽谈",
    "hostEmployeeName": "李四",
    "hostEmployeeId": 1001,
    "departmentVisited": "研发部",
    "plannedEntryDatetime": "2025-06-27 10:00:00",
    "plannedExitDatetime": "2025-06-27 18:00:00",
    "vehiclePlateNumber": "京A12345",
    "totalCompanions": 1,
    "submittedDatetime": "2025-06-26 15:30:00",
    "status": "pending",
    "attendeesList": [
      {
        "attendeeId": 2001,
        "registrationId": 1001,
        "visitorId": 3001,
        "isPrimary": "1",
        "accessCredential": null,
        "credentialExpiresAt": null,
        "visitorBasics": {
          "visitorId": 3001,
          "name": "张三",
          "idCardType": "1",
          "idCardNumber": "110101199001011234",
          "phone": "13800138000",
          "company": "XX科技有限公司",
          "gender": "1",
          "status": "provisional"
        }
      }
    ]
  }
}
```

---

### 4. 审核访客登记

#### 4.1 审核通过

**PUT** `/asc/visitor/registration/approve/{registrationId}`

#### 权限要求
- 权限标识: `asc:registration:approve`

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| registrationId | number | ✓ | 登记ID |

#### 成功响应

```json
{
  "msg": "审核通过，已为访客生成通行凭证",
  "code": 200,
  "data": null
}
```

#### 4.2 审核拒绝

**PUT** `/asc/visitor/registration/reject/{registrationId}`

#### 权限要求
- 权限标识: `asc:registration:approve`

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| registrationId | number | ✓ | 登记ID |

#### 请求体

```json
{
  "rejectionReason": "访问时间不合适，请重新申请"
}
```

#### 成功响应

```json
{
  "msg": "已拒绝该登记申请",
  "code": 200,
  "data": null
}
```

---

### 5. 快捷查询接口

#### 5.1 查询待审核登记

**GET** `/asc/visitor/registration/pending`

#### 权限要求
- 权限标识: `asc:registration:list`

#### 成功响应

```json
{
  "msg": "查询成功",
  "code": 200,
  "data": [
    {
      "registrationId": 1001,
      "primaryContactName": "张三",
      "primaryContactPhone": "13800138000",
      "reasonForVisit": "商务洽谈",
      "submittedDatetime": "2025-06-26 15:30:00",
      "status": "pending"
    }
  ]
}
```

#### 5.2 查询今日登记

**GET** `/asc/visitor/registration/today`

#### 权限要求
- 权限标识: `asc:registration:list`

#### 5.3 按被访人查询

**GET** `/asc/visitor/registration/host/{hostEmployeeId}`

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| hostEmployeeId | number | ✓ | 被访人用户ID |

---

### 6. 登记状态管理

#### 6.1 取消登记

**PUT** `/asc/visitor/registration/cancel/{registrationId}`

#### 权限要求
- 权限标识: `asc:registration:edit`

#### 6.2 完成登记

**PUT** `/asc/visitor/registration/complete/{registrationId}`

#### 权限要求
- 权限标识: `asc:registration:edit`

---

### 7. 修改访客登记

**PUT** `/asc/visitor/registration`

#### 权限要求
- 权限标识: `asc:registration:edit`

#### 请求体

```json
{
  "registrationId": 1001,
  "reasonForVisit": "技术交流",
  "plannedEntryDatetime": "2025-06-27 14:00:00",
  "plannedExitDatetime": "2025-06-27 17:00:00",
  "vehiclePlateNumber": "京A12345"
}
```

---

### 8. 删除访客登记

**DELETE** `/asc/visitor/registration/{registrationIds}`

#### 权限要求
- 权限标识: `asc:registration:remove`

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| registrationIds | string | ✓ | 登记ID列表，多个用逗号分隔 |

---

### 9. 导出访客登记

**POST** `/asc/visitor/registration/export`

#### 权限要求
- 权限标识: `asc:registration:export`

#### 请求体
```json
{
  "status": "approved",
  "startDate": "2025-06-01",
  "endDate": "2025-06-30"
}
```

#### 响应
返回Excel文件流

---

## 数据模型说明

### VisitRegistrations (访问登记)

```typescript
interface VisitRegistrations {
  registrationId?: number;          // 登记ID
  primaryContactName: string;       // 主联系人姓名
  primaryContactPhone: string;      // 主联系人手机号
  reasonForVisit: string;          // 来访事由
  hostEmployeeName: string;        // 被访人姓名
  hostEmployeeId: number;          // 被访人用户ID
  departmentVisited: string;       // 被访部门
  plannedEntryDatetime: string;    // 预计来访时间
  plannedExitDatetime: string;     // 预计离开时间
  vehiclePlateNumber?: string;     // 车牌号
  totalCompanions: number;         // 同行人数
  submittedDatetime?: string;      // 提交时间
  status: 'pending' | 'approved' | 'rejected' | 'cancelled' | 'completed';
  approvalStaffId?: number;        // 审核人ID
  approvalStaffName?: string;      // 审核人姓名
  approvalDatetime?: string;       // 审核时间
  rejectionReason?: string;        // 拒绝原因
  attendeesList?: RegistrationAttendees[]; // 参与人列表
}
```

### RegistrationAttendees (登记参与人)

```typescript
interface RegistrationAttendees {
  attendeeId?: number;             // 参与人ID
  registrationId: number;          // 登记ID
  visitorId?: number;              // 访客ID
  isPrimary: string;               // 是否主联系人 (1-是, 0-否)
  accessCredential?: string;       // 通行凭证
  credentialExpiresAt?: string;    // 凭证失效时间
  visitorBasics?: VisitorBasics;   // 访客基础信息
  // 临时字段(用于创建新访客)
  visitorName?: string;            // 访客姓名
  visitorPhone?: string;           // 访客手机号
  visitorIdCard?: string;          // 访客身份证号
  visitorCompany?: string;         // 访客公司
  visitorAvatarPhoto?: string;     // 头像照片数据
}
```

### VisitorBasics (访客基础信息)

```typescript
interface VisitorBasics {
  visitorId?: number;              // 访客ID
  name: string;                    // 访客姓名
  idCardType: string;              // 证件类型 (1-身份证, 2-护照)
  idCardNumber: string;            // 证件号码
  phone: string;                   // 手机号
  company: string;                 // 所属公司
  gender: string;                  // 性别 (0-未知, 1-男, 2-女)
  avatarPhotoUrl?: string;         // 头像照片路径
  status: 'provisional' | 'verified' | 'blacklisted'; // 状态
  blacklistReason?: string;        // 拉黑原因
  firstVisitDate?: string;         // 首次来访日期
  lastVisitDate?: string;          // 最近来访日期
}
```

---

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 500 | 请求参数不能为空 | 检查必填参数 |
| 500 | 参与人员信息不能为空 | 至少提供一个访客信息 |
| 500 | 访问过于频繁，请稍后再试 | IP限制，等待后重试 |
| 500 | 该手机号今日登记次数已达上限 | 手机号限制，明日重试 |
| 500 | 访问时间设置不合理 | 检查时间格式和合理性 |
| 500 | 访客信息不完整或不合理 | 检查身份证、手机号格式 |
| 500 | 请求包含异常信息 | 包含敏感词或异常数据 |
| 500 | 主联系人必须在参与人员列表中 | 确保主联系人在访客列表中 |
| 401 | 未授权 | 检查Token是否有效 |
| 403 | 权限不足 | 检查用户权限配置 |

---

## 最佳实践建议

### 前端开发建议

1. **表单验证**
   - 手机号格式: `^1[3-9]\d{9}$`
   - 身份证格式: `^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|31)\d{3}[0-9Xx]$`
   - 时间格式: `yyyy-MM-dd HH:mm:ss`

2. **错误处理**
   ```javascript
   try {
     const response = await api.post('/asc/visitor/registration', data);
     if (response.code === 200) {
       // 成功处理
     } else {
       // 显示错误信息
       message.error(response.msg);
     }
   } catch (error) {
     message.error('网络错误，请稍后重试');
   }
   ```

3. **状态管理**
   - 使用枚举管理状态值
   - 提供状态中文映射
   - 不同状态显示不同操作按钮

4. **分页处理**
   ```javascript
   const [pagination, setPagination] = useState({
     current: 1,
     pageSize: 10,
     total: 0
   });
   ```

5. **日期时间处理**
   ```javascript
   // 使用moment.js或dayjs处理时间
   const formatDateTime = (dateTime) => {
     return moment(dateTime).format('YYYY-MM-DD HH:mm:ss');
   };
   ```

### 安全注意事项

1. **敏感信息脱敏**
   - 手机号显示: `138****8000`
   - 身份证显示: `110101****1234`

2. **权限控制**
   - 根据用户权限显示/隐藏功能按钮
   - 接口调用前检查权限

3. **数据校验**
   - 前端校验 + 后端校验双重保障
   - 防止XSS攻击，对用户输入进行转义

---

## 测试用例

### 正常场景测试

1. **单人登记**
   ```json
   {
     "registration": {
       "primaryContactName": "测试用户",
       "primaryContactPhone": "13800138000",
       "reasonForVisit": "测试访问",
       "hostEmployeeName": "接待员",
       "hostEmployeeId": 1,
       "departmentVisited": "测试部",
       "plannedEntryDatetime": "2025-06-27 10:00:00",
       "plannedExitDatetime": "2025-06-27 18:00:00",
       "totalCompanions": 0
     },
     "attendeesList": [
       {
         "visitorName": "测试用户",
         "visitorPhone": "13800138000",
         "visitorIdCard": "110101199001011234",
         "visitorCompany": "测试公司",
         "isPrimary": "1",
         "visitorAvatarPhoto": null
       }
     ]
   }
   ```

2. **多人登记**
   - 增加多个访客到attendeesList数组
   - 调整totalCompanions数量
   - 确保只有一个访客的isPrimary为"1"

### 异常场景测试

1. **参数缺失**: 删除必填字段测试
2. **格式错误**: 错误的手机号、身份证格式
3. **时间错误**: 结束时间早于开始时间
4. **频率限制**: 快速多次提交测试
5. **敏感词**: 在reasonForVisit中包含敏感词

---

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2025-06-26 | 初始版本，包含核心访客登记功能 |

---

## 联系方式

如有疑问，请联系开发团队：
- 技术支持: <EMAIL>
- 项目负责人: <EMAIL>
