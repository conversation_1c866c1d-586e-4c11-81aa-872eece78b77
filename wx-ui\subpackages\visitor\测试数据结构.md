# 微信端访客登记"主联系不能为空"问题修复说明

## 问题分析

### 原始问题
微信端新建来访信息时提示"主联系不能为空"。

### 根本原因
后端 `VisitRegistrationsController.java` 第173-176行的验证逻辑错误：
```java
// 错误的验证逻辑
boolean hasPrimary = request.getAttendeesList().stream()
    .anyMatch(attendee -> attendee.getVisitorBasics() != null &&
             attendee.getVisitorBasics().getName().equals(request.getPrimaryContactName()));
```

**问题所在**：
1. **时序错误**：`visitorBasics` 对象是在 `Service` 层的 `createOrGetVisitor()` 方法中创建的，而验证发生在 `Controller` 层，此时 `visitorBasics` 还未被设置
2. **逻辑错误**：前端（包括ruoyi-ui管理端和微信端）都只提供临时字段（`visitorName`, `visitorPhone` 等），不提供 `visitorBasics` 对象

### 后端访客管理流程
1. **前端提交**：临时字段（`visitorName`, `visitorPhone`, `visitorIdCard`, `visitorCompany`, `visitorAvatarPhoto`）
2. **Controller验证**：应该使用临时字段进行验证
3. **Service处理**：`createOrGetVisitor()` 使用临时字段创建或查找 `VisitorBasics` 对象
4. **数据库存储**：关联访客ID到 `RegistrationAttendees` 表

## 修复方案

### 1. 修复Controller验证逻辑
```java
// 修复后的验证逻辑
boolean hasPrimary = request.getAttendeesList().stream()
    .anyMatch(attendee -> attendee.getVisitorName() != null &&
             attendee.getVisitorName().equals(request.getPrimaryContactName()));
```

### 2. 修复VisitorSecurityUtils验证逻辑
将所有使用 `attendee.getVisitorBasics()` 的地方改为使用临时字段：
- `validateVisitorInfo()` 方法
- `isSuspiciousRequest()` 方法  
- `hasDuplicateIdNumbers()` 方法

### 3. 简化微信端数据结构
微信端只需要提供临时字段，不需要 `visitorBasics` 对象：
```javascript
{
  "registration": {
    "primaryContactName": "张三",
    "primaryContactPhone": "13800138000",
    // ... 其他登记信息
  },
  "attendeesList": [
    {
      "visitorName": "张三",
      "visitorPhone": "13800138000",
      "visitorIdCard": "110101199001011234",
      "visitorCompany": "XX科技有限公司",
      "isPrimary": "1",
      "visitorAvatarPhoto": null
    }
  ]
}
```

## 验证
- ✅ ruoyi-ui管理端：使用临时字段，正常工作
- ✅ 微信端：使用临时字段，修复后应该正常工作
- ✅ 后端Service层：`createOrGetVisitor()` 正确处理临时字段

## 总结
**真正的问题**：后端验证逻辑时序错误，而不是前端数据结构问题
**解决方案**：修复后端验证逻辑使用正确的字段，前端保持原有简单结构
