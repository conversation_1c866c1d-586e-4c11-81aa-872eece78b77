{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-4d42a6e4\"],{\"0ccb\":function(t,e,i){var a=i(\"e330\"),r=i(\"50c4\"),s=i(\"577e\"),n=i(\"1148\"),o=i(\"1d80\"),c=a(n),l=a(\"\".slice),m=Math.ceil,u=function(t){return function(e,i,a){var n,u,d=s(o(e)),f=r(i),p=d.length,v=void 0===a?\" \":s(a);return f<=p||\"\"==v?d:(n=f-p,u=c(v,m(n/v.length)),u.length>n&&(u=l(u,0,n)),t?d+u:u+d)}};t.exports={start:u(!1),end:u(!0)}},\"22cd\":function(t,e,i){},\"469a\":function(t,e,i){\"use strict\";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i(\"div\",{staticClass:\"pc-register-container\"},[t._m(0),i(\"div\",{staticClass:\"main-content mobile-layout\"},[i(\"div\",{staticClass:\"mobile-form-container\"},[t.registrationCompleted?i(\"div\",{staticClass:\"mobile-form-header\"},[t._m(2),i(\"p\",{staticClass:\"form-subtitle\"},[t._v(\"您的访客登记已提交成功\")])]):i(\"div\",{staticClass:\"mobile-form-header\"},[t._m(1),i(\"p\",{staticClass:\"form-subtitle\"},[t._v(\"共\"+t._s(t.totalVisitors)+\"人，请确保信息准确\")])]),t.registrationCompleted?t._e():i(\"div\",{staticClass:\"mobile-form-content\"},[i(\"el-form\",{ref:\"visitForm\",staticClass:\"mobile-visit-form\",attrs:{model:t.formData,rules:t.visitRules,\"label-width\":\"100px\"}},[i(\"div\",{staticClass:\"mobile-form-card\"},[i(\"div\",{staticClass:\"card-header\"},[i(\"i\",{staticClass:\"el-icon-document\"}),i(\"span\",{staticClass:\"card-title\"},[t._v(\"访问信息\")])]),i(\"div\",{staticClass:\"card-content\"},[i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"来访事由\",prop:\"visitInfo.reasonForVisit\"}},[i(\"el-input\",{staticClass:\"mobile-input\",attrs:{placeholder:\"请输入来访事由\",type:\"textarea\",rows:3,maxlength:\"200\",\"show-word-limit\":\"\"},model:{value:t.formData.visitInfo.reasonForVisit,callback:function(e){t.$set(t.formData.visitInfo,\"reasonForVisit\",e)},expression:\"formData.visitInfo.reasonForVisit\"}})],1),i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"被访人\",prop:\"visitInfo.hostEmployeeName\"}},[i(\"el-input\",{staticClass:\"mobile-input\",attrs:{placeholder:\"请输入被访人姓名\",maxlength:\"20\"},model:{value:t.formData.visitInfo.hostEmployeeName,callback:function(e){t.$set(t.formData.visitInfo,\"hostEmployeeName\",e)},expression:\"formData.visitInfo.hostEmployeeName\"}})],1),i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"被访部门\",prop:\"visitInfo.departmentVisited\"}},[i(\"el-input\",{staticClass:\"mobile-input\",attrs:{placeholder:\"请输入被访部门\",maxlength:\"50\"},model:{value:t.formData.visitInfo.departmentVisited,callback:function(e){t.$set(t.formData.visitInfo,\"departmentVisited\",e)},expression:\"formData.visitInfo.departmentVisited\"}})],1),i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"车牌号\"}},[i(\"el-input\",{staticClass:\"mobile-input\",attrs:{placeholder:\"如有车辆请填写（可选）\"},model:{value:t.formData.visitInfo.vehiclePlateNumber,callback:function(e){t.$set(t.formData.visitInfo,\"vehiclePlateNumber\",e)},expression:\"formData.visitInfo.vehiclePlateNumber\"}})],1)],1)]),i(\"div\",{staticClass:\"mobile-form-card\"},[i(\"div\",{staticClass:\"card-header\"},[i(\"i\",{staticClass:\"el-icon-time\"}),i(\"span\",{staticClass:\"card-title\"},[t._v(\"访问时间\")])]),i(\"div\",{staticClass:\"card-content\"},[i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"到访时间\",prop:\"visitInfo.plannedEntryDatetime\"}},[i(\"el-date-picker\",{staticClass:\"mobile-date-picker\",staticStyle:{width:\"100%\"},attrs:{type:\"datetime\",placeholder:\"选择到访时间\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"picker-options\":t.entryPickerOptions},on:{change:t.onArrivalTimeChange},model:{value:t.formData.visitInfo.plannedEntryDatetime,callback:function(e){t.$set(t.formData.visitInfo,\"plannedEntryDatetime\",e)},expression:\"formData.visitInfo.plannedEntryDatetime\"}})],1),i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"离开时间\",prop:\"visitInfo.plannedExitDatetime\"}},[i(\"el-date-picker\",{staticClass:\"mobile-date-picker\",staticStyle:{width:\"100%\"},attrs:{type:\"datetime\",placeholder:\"选择离开时间\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"picker-options\":t.exitPickerOptions},on:{change:t.onDepartureTimeChange},model:{value:t.formData.visitInfo.plannedExitDatetime,callback:function(e){t.$set(t.formData.visitInfo,\"plannedExitDatetime\",e)},expression:\"formData.visitInfo.plannedExitDatetime\"}})],1)],1)]),i(\"div\",{staticClass:\"mobile-form-card\"},[i(\"div\",{staticClass:\"card-header\"},[i(\"i\",{staticClass:\"el-icon-user\"}),i(\"span\",{staticClass:\"card-title\"},[t._v(\"访客信息（共\"+t._s(t.totalVisitors)+\"人）\")]),i(\"el-button\",{staticClass:\"add-visitor-btn\",attrs:{type:\"primary\",size:\"mini\",icon:\"el-icon-plus\",disabled:t.formData.visitors.length>=10},on:{click:t.addVisitor}},[t._v(\" 添加 \")])],1),i(\"div\",{staticClass:\"card-content\"},t._l(t.formData.visitors,(function(e,a){return i(\"div\",{key:a,staticClass:\"mobile-visitor-item\"},[i(\"div\",{staticClass:\"visitor-header\"},[i(\"div\",{staticClass:\"visitor-badge\",class:{primary:0===a}},[t._v(\" \"+t._s(0===a?\"主联系人\":\"访客\"+(a+1))+\" \")]),a>0?i(\"el-button\",{staticClass:\"remove-btn\",attrs:{size:\"mini\",type:\"danger\",icon:\"el-icon-delete\",circle:\"\"},on:{click:function(e){return t.removeVisitor(a)}}}):t._e()],1),i(\"div\",{staticClass:\"visitor-form\"},[i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"姓名\",prop:\"visitors.\"+a+\".name\"}},[i(\"el-input\",{staticClass:\"mobile-input\",attrs:{placeholder:\"请输入姓名\",maxlength:\"20\"},model:{value:e.name,callback:function(i){t.$set(e,\"name\",i)},expression:\"visitor.name\"}})],1),i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"手机号\",prop:\"visitors.\"+a+\".phone\"}},[i(\"el-input\",{staticClass:\"mobile-input\",attrs:{placeholder:\"请输入手机号\"},model:{value:e.phone,callback:function(i){t.$set(e,\"phone\",i)},expression:\"visitor.phone\"}})],1),i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"身份证号\",prop:\"visitors.\"+a+\".idCard\"}},[i(\"el-input\",{staticClass:\"mobile-input\",attrs:{placeholder:\"请输入身份证号\"},model:{value:e.idCard,callback:function(i){t.$set(e,\"idCard\",i)},expression:\"visitor.idCard\"}})],1),i(\"el-form-item\",{staticClass:\"mobile-form-item\",attrs:{label:\"公司名称\"}},[i(\"el-input\",{staticClass:\"mobile-input\",attrs:{placeholder:\"请输入公司名称（可选）\",maxlength:\"100\"},model:{value:e.company,callback:function(i){t.$set(e,\"company\",i)},expression:\"visitor.company\"}})],1)],1)])})),0)])])],1),t.registrationCompleted?i(\"div\",{staticClass:\"success-content\"},[t._m(3),i(\"h4\",[t._v(\"登记成功！\")]),i(\"p\",{staticClass:\"success-message\"},[t._v(\"您的访客登记已提交，请等待审核通过\")]),i(\"div\",{staticClass:\"register-summary\"},[i(\"h5\",[t._v(\"登记信息摘要\")]),i(\"el-descriptions\",{attrs:{column:2,border:\"\"}},[i(\"el-descriptions-item\",{attrs:{label:\"主联系人\"}},[t._v(\" \"+t._s(t.mainContact.name)+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"联系电话\"}},[t._v(\" \"+t._s(t.mainContact.phone)+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"被访人\"}},[t._v(\" \"+t._s(t.formData.visitInfo.hostEmployeeName)+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"被访部门\"}},[t._v(\" \"+t._s(t.formData.visitInfo.departmentVisited)+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"来访事由\",span:\"2\"}},[t._v(\" \"+t._s(t.formData.visitInfo.reasonForVisit)+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"预计到访时间\"}},[t._v(\" \"+t._s(t.parseTime(t.formData.visitInfo.plannedEntryDatetime))+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"预计离开时间\"}},[t._v(\" \"+t._s(t.parseTime(t.formData.visitInfo.plannedExitDatetime))+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"访客总数\"}},[t._v(\" \"+t._s(t.totalVisitors)+\" 人 \")])],1)],1),t.registrationId?i(\"div\",{staticClass:\"access-credential\"},[i(\"h5\",[t._v(\"访问凭证\")]),i(\"div\",{staticClass:\"credential-card\"},[i(\"div\",{staticClass:\"qr-code-container\"},[t._m(4),i(\"p\",{staticClass:\"qr-code-id\"},[t._v(t._s(t.registrationId))])]),t._m(5)])]):t._e()]):t._e(),i(\"div\",{staticClass:\"mobile-actions\"},[t.registrationCompleted?t._e():i(\"div\",{staticClass:\"submit-actions\"},[i(\"el-button\",{staticClass:\"mobile-submit-btn\",attrs:{type:\"primary\",size:\"large\",loading:t.submitting},on:{click:t.submitForm}},[t.submitting?t._e():i(\"i\",{staticClass:\"el-icon-check\"}),t.submitting?i(\"i\",{staticClass:\"el-icon-loading\"}):t._e(),t._v(\" \"+t._s(t.submitting?\"登记中...\":\"确认登记 (\"+t.totalVisitors+\"人)\")+\" \")]),i(\"el-button\",{staticClass:\"mobile-reset-btn\",attrs:{size:\"large\"},on:{click:t.resetForm}},[i(\"i\",{staticClass:\"el-icon-refresh-left\"}),t._v(\" 重置表单 \")])],1),t.registrationCompleted?i(\"div\",{staticClass:\"success-actions\"},[i(\"el-button\",{staticClass:\"mobile-action-btn\",attrs:{type:\"primary\",size:\"large\"},on:{click:t.printCredential}},[i(\"i\",{staticClass:\"el-icon-printer\"}),t._v(\" 打印凭证 \")]),i(\"el-button\",{staticClass:\"mobile-action-btn\",attrs:{size:\"large\"},on:{click:t.resetForm}},[i(\"i\",{staticClass:\"el-icon-refresh\"}),t._v(\" 重新登记 \")])],1):t._e()])])])])},r=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i(\"div\",{staticClass:\"top-nav\"},[i(\"div\",{staticClass:\"nav-content\"},[i(\"div\",{staticClass:\"logo-section\"},[i(\"span\",{staticClass:\"company-name\"},[t._v(\"高义钢铁访客登记系统\")])])])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i(\"h3\",[i(\"i\",{staticClass:\"el-icon-edit-outline\"}),t._v(\" 填写登记信息\")])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i(\"h3\",[i(\"i\",{staticClass:\"el-icon-circle-check\"}),t._v(\" 登记成功\")])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i(\"div\",{staticClass:\"success-icon\"},[i(\"i\",{staticClass:\"el-icon-circle-check\"})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i(\"div\",{staticClass:\"qr-code\"},[i(\"i\",{staticClass:\"el-icon-qrcode\"})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i(\"div\",{staticClass:\"credential-info\"},[i(\"h6\",[t._v(\"使用说明\")]),i(\"ul\",[i(\"li\",[t._v(\"请保存此二维码截图\")]),i(\"li\",[t._v(\"审核通过后可用于园区门禁\")]),i(\"li\",[t._v(\"凭证仅限当次访问使用\")]),i(\"li\",[t._v(\"如有疑问请联系园区前台\")])])])}],s=i(\"c14f\"),n=i(\"1da1\"),o=(i(\"99af\"),i(\"d81d\"),i(\"14d9\"),i(\"a434\"),i(\"b0c0\"),i(\"e9c4\"),i(\"d3b7\"),i(\"ac1f\"),i(\"00b4\"),i(\"4d90\"),i(\"498a\"),i(\"0643\"),i(\"a573\"),i(\"a2d6\")),c={name:\"SelfRegister\",data:function(){return{submitting:!1,registrationCompleted:!1,formData:{visitors:[{name:\"\",phone:\"\",idCard:\"\",company:\"\",isMainContact:!0}],visitInfo:{reasonForVisit:\"\",hostEmployeeName:\"\",departmentVisited:\"\",vehiclePlateNumber:\"\",plannedEntryDatetime:\"\",plannedExitDatetime:\"\"}},visitRules:{\"visitInfo.reasonForVisit\":[{required:!0,message:\"请输入来访事由\",trigger:\"blur\"},{min:2,max:200,message:\"来访事由长度应在2-200个字符之间\",trigger:\"blur\"}],\"visitInfo.hostEmployeeName\":[{required:!0,message:\"请输入被访人姓名\",trigger:\"blur\"},{min:2,max:20,message:\"被访人姓名长度应在2-20个字符之间\",trigger:\"blur\"}],\"visitInfo.departmentVisited\":[{required:!0,message:\"请输入被访部门\",trigger:\"blur\"},{min:2,max:50,message:\"部门名称长度应在2-50个字符之间\",trigger:\"blur\"}],\"visitInfo.plannedEntryDatetime\":[{required:!0,message:\"请选择预计来访时间\",trigger:\"change\"}],\"visitInfo.plannedExitDatetime\":[{required:!0,message:\"请选择预计离开时间\",trigger:\"change\"}],\"visitors.0.name\":[{required:!0,message:\"请输入主联系人姓名\",trigger:\"blur\"},{min:2,max:20,message:\"姓名长度应在2-20个字符之间\",trigger:\"blur\"}],\"visitors.0.phone\":[{required:!0,message:\"请输入主联系人手机号\",trigger:\"blur\"},{pattern:/^1[3-9]\\d{9}$/,message:\"请输入正确的手机号\",trigger:\"blur\"}],\"visitors.0.idCard\":[{required:!0,message:\"请输入主联系人身份证号\",trigger:\"blur\"}]},registrationId:null,entryPickerOptions:{shortcuts:[{text:\"现在\",onClick:function(t){t.$emit(\"pick\",new Date)}},{text:\"1小时后\",onClick:function(t){var e=new Date;e.setTime(e.getTime()+36e5),t.$emit(\"pick\",e)}},{text:\"明天上午9点\",onClick:function(t){var e=new Date;e.setDate(e.getDate()+1),e.setHours(9,0,0,0),t.$emit(\"pick\",e)}}],disabledDate:function(t){var e=Date.now()-36e5,i=Date.now()+2592e6;return t.getTime()<e||t.getTime()>i}},exitPickerOptions:{shortcuts:[{text:\"2小时后\",onClick:function(t){var e=new Date;e.setTime(e.getTime()+72e5),t.$emit(\"pick\",e)}},{text:\"中午12点\",onClick:function(t){var e=new Date;e.setHours(12,0,0,0),e.getTime()<Date.now()&&e.setDate(e.getDate()+1),t.$emit(\"pick\",e)}},{text:\"今天下午6点\",onClick:function(t){var e=new Date;e.setHours(18,0,0,0),e.getTime()<Date.now()&&e.setDate(e.getDate()+1),t.$emit(\"pick\",e)}}],disabledDate:function(t){var e=Date.now()+2592e6;return t.getTime()<Date.now()-864e5||t.getTime()>e}}}},computed:{totalVisitors:function(){return this.formData.visitors.length},mainContact:function(){return this.formData.visitors[0]||{}}},methods:{addVisitor:function(){this.formData.visitors.length>=10?this.$message.warning(\"最多只能添加10名访客\"):(this.formData.visitors.push({name:\"\",phone:\"\",idCard:\"\",company:\"\",isMainContact:!1}),this.$message.success(\"已添加访客\"))},removeVisitor:function(t){0!==t?(this.formData.visitors.splice(t,1),this.$message.success(\"已移除访客\")):this.$message.warning(\"不能删除主联系人\")},formatDateForBackend:function(t){if(!t)return\"\";var e=new Date(t);if(isNaN(e.getTime()))return\"\";var i=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,\"0\"),r=String(e.getDate()).padStart(2,\"0\"),s=String(e.getHours()).padStart(2,\"0\"),n=String(e.getMinutes()).padStart(2,\"0\"),o=String(e.getSeconds()).padStart(2,\"0\");return\"\".concat(i,\"-\").concat(a,\"-\").concat(r,\" \").concat(s,\":\").concat(n,\":\").concat(o)},onArrivalTimeChange:function(t){if(console.log(\"预计来访时间变更:\",t),t&&!this.formData.visitInfo.plannedExitDatetime){var e=new Date(t),i=new Date(e.getTime()+144e5),a=i.getFullYear(),r=String(i.getMonth()+1).padStart(2,\"0\"),s=String(i.getDate()).padStart(2,\"0\"),n=String(i.getHours()).padStart(2,\"0\"),o=String(i.getMinutes()).padStart(2,\"0\"),c=String(i.getSeconds()).padStart(2,\"0\");this.formData.visitInfo.plannedExitDatetime=\"\".concat(a,\"-\").concat(r,\"-\").concat(s,\" \").concat(n,\":\").concat(o,\":\").concat(c)}},onDepartureTimeChange:function(t){if(console.log(\"预计离开时间变更:\",t),t&&this.formData.visitInfo.plannedEntryDatetime){var e=new Date(this.formData.visitInfo.plannedEntryDatetime),i=new Date(t);i<=e&&(this.$message.warning(\"预计离开时间不能早于或等于来访时间\"),this.formData.visitInfo.plannedExitDatetime=\"\")}},submitForm:function(){var t=this;return Object(n[\"a\"])(Object(s[\"a\"])().m((function e(){var i,a,r,n;return Object(s[\"a\"])().w((function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,t.$refs.visitForm.validate();case 1:if(t.validateVisitors()){e.n=2;break}return e.a(2);case 2:if(t.validateTimes()){e.n=3;break}return e.a(2);case 3:return t.submitting=!0,i=t.formData.visitors[0],a={registration:{primaryContactName:i.name.trim(),primaryContactPhone:i.phone.trim(),reasonForVisit:t.formData.visitInfo.reasonForVisit,hostEmployeeName:t.formData.visitInfo.hostEmployeeName,hostEmployeeId:null,departmentVisited:t.formData.visitInfo.departmentVisited,plannedEntryDatetime:t.formatDateForBackend(t.formData.visitInfo.plannedEntryDatetime),plannedExitDatetime:t.formatDateForBackend(t.formData.visitInfo.plannedExitDatetime),vehiclePlateNumber:t.formData.visitInfo.vehiclePlateNumber||\"\",totalCompanions:t.formData.visitors.length-1},attendeesList:t.formData.visitors.map((function(t,e){return{visitorName:t.name.trim(),visitorPhone:t.phone.trim(),visitorIdCard:t.idCard.trim().toUpperCase(),visitorCompany:(t.company||\"\").trim(),isPrimary:0===e?\"1\":\"0\",visitorAvatarPhoto:null}}))},console.log(\"提交数据结构:\",JSON.stringify(a,null,2)),e.n=4,Object(o[\"A\"])(a);case 4:r=e.v,200===r.code?(t.registrationId=r.data||\"VR\"+Date.now(),t.$message.success(\"\".concat(t.totalVisitors,\"名访客登记成功！\")),t.registrationCompleted=!0):t.$message.error(r.msg||\"登记失败，请重试\"),e.n=6;break;case 5:e.p=5,n=e.v,console.error(\"提交表单失败:\",n),t.$message.error(\"登记失败，请检查网络连接\");case 6:return e.p=6,t.submitting=!1,e.f(6);case 7:return e.a(2)}}),e,null,[[0,5,6,7]])})))()},validateVisitors:function(){for(var t=0;t<this.formData.visitors.length;t++){var e=this.formData.visitors[t],i=0===t?\"主联系人\":\"访客\".concat(t+1);if(!e.name||\"\"===e.name.trim())return this.$message.error(\"请输入\".concat(i,\"的姓名\")),!1;if(e.name.trim().length<2||e.name.trim().length>20)return this.$message.error(\"\".concat(i,\"的姓名长度应在2-20个字符之间\")),!1;if(!e.phone||\"\"===e.phone.trim())return this.$message.error(\"请输入\".concat(i,\"的手机号\")),!1;var a=/^1[3-9]\\d{9}$/;if(!a.test(e.phone.trim()))return this.$message.error(\"\".concat(i,\"的手机号格式不正确\")),!1;if(!e.idCard||\"\"===e.idCard.trim())return this.$message.error(\"请输入\".concat(i,\"的身份证号\")),!1;var r=e.idCard.trim().toUpperCase();if(18===r.length){var s=/^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;if(!s.test(r))return this.$message.error(\"\".concat(i,\"的身份证号格式不正确\")),!1}for(var n=0;n<this.formData.visitors.length;n++)if(t!==n&&e.idCard.trim().toUpperCase()===this.formData.visitors[n].idCard.trim().toUpperCase())return this.$message.error(\"\".concat(i,\"与访客\").concat(0===n?\"主联系人\":n+1,\"的身份证号重复\")),!1;for(var o=0;o<this.formData.visitors.length;o++)if(t!==o&&e.phone.trim()===this.formData.visitors[o].phone.trim())return this.$message.error(\"\".concat(i,\"与访客\").concat(0===o?\"主联系人\":o+1,\"的手机号重复，请确认是否为同一人\")),!1;e.name=e.name.trim(),e.phone=e.phone.trim(),e.idCard=e.idCard.trim().toUpperCase(),e.company=(e.company||\"\").trim()}return!0},validateTimes:function(){if(!this.formData.visitInfo.plannedEntryDatetime)return this.$message.error(\"请选择预计来访时间\"),!1;if(!this.formData.visitInfo.plannedExitDatetime)return this.$message.error(\"请选择预计离开时间\"),!1;var t=new Date(this.formData.visitInfo.plannedEntryDatetime),e=new Date(this.formData.visitInfo.plannedExitDatetime),i=new Date,a=new Date(i.getTime()-36e5);if(t<a)return this.$message.error(\"预计来访时间不能早于当前时间1小时前\"),!1;if(e<=t)return this.$message.error(\"预计离开时间必须晚于来访时间\"),!1;var r=e.getTime()-t.getTime(),s=864e5;return!(r>s)||(this.$message.error(\"单次访问时长不能超过24小时\"),!1)},resetForm:function(){var t=this;this.$confirm(\"确定要重置表单吗？当前填写的信息将会丢失。\",\"确认重置\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){t.registrationCompleted=!1,t.formData={visitors:[{name:\"\",phone:\"\",idCard:\"\",company:\"\",isMainContact:!0}],visitInfo:{reasonForVisit:\"\",hostEmployeeName:\"\",departmentVisited:\"\",vehiclePlateNumber:\"\",plannedEntryDatetime:\"\",plannedExitDatetime:\"\"}},t.registrationId=null,t.$message.success(\"表单已重置\")}))},printCredential:function(){this.$message.info(\"打印功能开发中...\")},parseTime:function(t){if(!t)return\"\";var e=new Date(t);if(isNaN(e.getTime()))return\"\";var i=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,\"0\"),r=String(e.getDate()).padStart(2,\"0\"),s=String(e.getHours()).padStart(2,\"0\"),n=String(e.getMinutes()).padStart(2,\"0\");return\"\".concat(i,\"-\").concat(a,\"-\").concat(r,\" \").concat(s,\":\").concat(n)},showHelp:function(){this.$alert('\\n        <div style=\"text-align: left;\">\\n          <h4>使用说明</h4>\\n          <p><strong>1. 填写基本信息</strong></p>\\n          <ul>\\n            <li>请如实填写来访事由、被访人等信息</li>\\n            <li>主联系人信息必须准确，用于后续联系</li>\\n          </ul>\\n          <p><strong>2. 添加同行人员</strong></p>\\n          <ul>\\n            <li>如有同行人员，请点击\"添加访客\"按钮</li>\\n            <li>每位访客都需要填写完整身份信息</li>\\n          </ul>\\n          <p><strong>3. 选择访问时间</strong></p>\\n          <ul>\\n            <li>请合理安排访问时间，避免过长逗留</li>\\n            <li>如需延长访问时间，请联系被访人</li>\\n          </ul>\\n          <p><strong>4. 审核与通行</strong></p>\\n          <ul>\\n            <li>提交后等待审核，通过后可凭二维码进入园区</li>\\n            <li>请按照预约时间准时到访</li>\\n          </ul>\\n        </div>\\n      ',\"使用帮助\",{confirmButtonText:\"知道了\",dangerouslyUseHTMLString:!0})},contactService:function(){var t=this;this.$confirm(\"需要帮助吗？\",\"联系客服\",{confirmButtonText:\"拨打电话\",cancelButtonText:\"取消\",type:\"info\"}).then((function(){t.$message({type:\"info\",message:\"客服电话：400-xxx-xxxx\"})})).catch((function(){}))}}},l=c,m=(i(\"92e1\"),i(\"2877\")),u=Object(m[\"a\"])(l,a,r,!1,null,\"53a04a32\",null);e[\"default\"]=u.exports},\"4d90\":function(t,e,i){\"use strict\";var a=i(\"23e7\"),r=i(\"0ccb\").start,s=i(\"9a0c\");a({target:\"String\",proto:!0,forced:s},{padStart:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},\"92e1\":function(t,e,i){\"use strict\";i(\"22cd\")},\"9a0c\":function(t,e,i){var a=i(\"342f\");t.exports=/Version\\/10(?:\\.\\d+){1,2}(?: [\\w./]+)?(?: Mobile\\/\\w+)? Safari\\//.test(a)},a2d6:function(t,e,i){\"use strict\";i.d(e,\"v\",(function(){return r})),i.d(e,\"p\",(function(){return s})),i.d(e,\"a\",(function(){return n})),i.d(e,\"B\",(function(){return o})),i.d(e,\"y\",(function(){return c})),i.d(e,\"h\",(function(){return l})),i.d(e,\"z\",(function(){return m})),i.d(e,\"q\",(function(){return u})),i.d(e,\"u\",(function(){return d})),i.d(e,\"k\",(function(){return f})),i.d(e,\"l\",(function(){return p})),i.d(e,\"b\",(function(){return v})),i.d(e,\"A\",(function(){return g})),i.d(e,\"n\",(function(){return h})),i.d(e,\"f\",(function(){return b})),i.d(e,\"c\",(function(){return C})),i.d(e,\"x\",(function(){return D})),i.d(e,\"m\",(function(){return y})),i.d(e,\"i\",(function(){return _})),i.d(e,\"o\",(function(){return x})),i.d(e,\"r\",(function(){return I})),i.d(e,\"w\",(function(){return k})),i.d(e,\"t\",(function(){return $})),i.d(e,\"s\",(function(){return E})),i.d(e,\"g\",(function(){return w})),i.d(e,\"d\",(function(){return S})),i.d(e,\"j\",(function(){return O}));var a=i(\"b775\");function r(t){return Object(a[\"a\"])({url:\"/asc/visitor/basics/list\",method:\"get\",params:t})}function s(t){return Object(a[\"a\"])({url:\"/asc/visitor/basics/\"+t,method:\"get\"})}function n(t){return Object(a[\"a\"])({url:\"/asc/visitor/basics\",method:\"post\",data:t})}function o(t){return Object(a[\"a\"])({url:\"/asc/visitor/basics\",method:\"put\",data:t})}function c(t){return Object(a[\"a\"])({url:\"/asc/visitor/basics/avatar/\"+t,method:\"delete\"})}function l(t){return Object(a[\"a\"])({url:\"/common/deleteFile\",method:\"post\",params:{fileName:t}})}function m(t,e,i){return Object(a[\"a\"])({url:\"/asc/visitor/basics/blacklist/\"+t,method:\"put\",params:{isBlacklisted:e,blacklistReason:i}})}function u(){return Object(a[\"a\"])({url:\"/asc/visitor/basics/statistics\",method:\"get\"})}function d(t){return Object(a[\"a\"])({url:\"/asc/visitor/registration/list\",method:\"get\",params:t})}function f(t){return Object(a[\"a\"])({url:\"/asc/visitor/registration/pending\",method:\"get\",params:t})}function p(t){return Object(a[\"a\"])({url:\"/asc/visitor/registration/\"+t,method:\"get\"})}function v(t){return Object(a[\"a\"])({url:\"/asc/visitor/registration\",method:\"post\",data:t})}function g(t){return Object(a[\"a\"])({url:\"/asc/visitor/registration\",method:\"post\",data:t})}function h(){return Object(a[\"a\"])({url:\"/asc/visitor/registration/today\",method:\"get\"})}function b(t){return Object(a[\"a\"])({url:\"/asc/visitor/registration/\"+t,method:\"delete\"})}function C(t){return Object(a[\"a\"])({url:\"/asc/visitor/registration/approve/\"+t,method:\"put\"})}function D(t,e){return Object(a[\"a\"])({url:\"/asc/visitor/registration/reject/\"+t,method:\"put\",data:e})}function y(t){return Object(a[\"a\"])({url:\"/asc/visitor/registration/qrcode/\"+t,method:\"get\"})}function _(t){return Object(a[\"a\"])({url:\"/asc/visitor/visit/current\",method:\"get\",params:t})}function x(t){return Object(a[\"a\"])({url:\"/asc/visitor/visit/\"+t,method:\"get\"})}function I(t){return Object(a[\"a\"])({url:\"/asc/visitor/access/history/\"+t,method:\"get\"})}function k(t,e,i){return Object(a[\"a\"])({url:\"/asc/visitor/visit/exit/\"+t,method:\"put\",params:{exitMethod:e,exitGate:i}})}function $(t){return Object(a[\"a\"])({url:\"/asc/visitor/access/list\",method:\"get\",params:t})}function E(t){return Object(a[\"a\"])({url:\"/asc/visitor/access/exit\",method:\"put\",data:t})}function w(t){return Object(a[\"a\"])({url:\"/asc/visitor/access/\"+t,method:\"delete\"})}function S(){return Object(a[\"a\"])({url:\"/asc/visitor/access/overtime/check\",method:\"post\"})}function O(t){return Object(a[\"a\"])({url:\"/asc/visitor/statistics/daily\",method:\"get\",params:{date:t}})}}}]);", "extractedComments": []}