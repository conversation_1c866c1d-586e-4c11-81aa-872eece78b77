package com.sxgygt.asc.service;

import java.util.List;
import com.sxgygt.asc.domain.VisitRegistrations;
import com.sxgygt.asc.domain.RegistrationAttendees;

/**
 * 访问登记信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IVisitRegistrationsService 
{
    /**
     * 查询访问登记信息
     * 
     * @param registrationId 访问登记信息主键
     * @return 访问登记信息
     */
    public VisitRegistrations selectVisitRegistrationsById(Long registrationId);

    /**
     * 查询访问登记信息列表
     * 
     * @param visitRegistrations 访问登记信息
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectVisitRegistrationsList(VisitRegistrations visitRegistrations);

    /**
     * 查询待审核的登记信息
     * 
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectPendingRegistrations();

    /**
     * 根据被访人查询登记信息
     * 
     * @param hostEmployeeId 被访人用户ID
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectRegistrationsByHostEmployeeId(Long hostEmployeeId);

    /**
     * 查询今日登记信息
     *
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectTodayRegistrations();

    /**
     * 访客查询今日登记记录（根据身份证号或手机号）
     *
     * @param idCardNumber 身份证号
     * @param primaryContactPhone 主联系人手机号
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> queryTodayRegistrationsByVisitor(String idCardNumber, String primaryContactPhone);

    /**
     * 新增访问登记信息（支持多人登记）
     * 
     * @param visitRegistrations 访问登记信息
     * @param attendeesList 参与人员列表
     * @return 结果
     */
    public int insertVisitRegistrations(VisitRegistrations visitRegistrations, List<RegistrationAttendees> attendeesList);

    /**
     * 修改访问登记信息
     * 
     * @param visitRegistrations 访问登记信息
     * @return 结果
     */
    public int updateVisitRegistrations(VisitRegistrations visitRegistrations);

    /**
     * 批量删除访问登记信息
     * 
     * @param registrationIds 需要删除的访问登记信息主键集合
     * @return 结果
     */
    public int deleteVisitRegistrationsByIds(Long[] registrationIds);

    /**
     * 删除访问登记信息信息
     * 
     * @param registrationId 访问登记信息主键
     * @return 结果
     */
    public int deleteVisitRegistrationsById(Long registrationId);

    /**
     * 审核通过访问登记
     * 
     * @param registrationId 登记ID
     * @param approvalStaffId 审核人ID
     * @param approvalStaffName 审核人姓名
     * @return 结果
     */
    public int approveRegistration(Long registrationId, Long approvalStaffId, String approvalStaffName);

    /**
     * 拒绝访问登记
     * 
     * @param registrationId 登记ID
     * @param rejectionReason 拒绝原因
     * @param approvalStaffId 审核人ID
     * @param approvalStaffName 审核人姓名
     * @return 结果
     */
    public int rejectRegistration(Long registrationId, String rejectionReason, Long approvalStaffId, String approvalStaffName);

    /**
     * 取消访问登记
     * 
     * @param registrationId 登记ID
     * @return 结果
     */
    public int cancelRegistration(Long registrationId);

    /**
     * 完成访问登记（所有访客已离场）
     * 
     * @param registrationId 登记ID
     * @return 结果
     */
    public int completeRegistration(Long registrationId);
}
