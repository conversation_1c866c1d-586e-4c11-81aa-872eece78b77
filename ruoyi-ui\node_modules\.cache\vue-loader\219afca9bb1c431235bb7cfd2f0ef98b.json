{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=style&index=0&id=53a04a32&prod&scoped=true&lang=css", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750987744033}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750836933411}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750836980592}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750836940519}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDkuLvlrrnlmaggKi8NCi5wYy1yZWdpc3Rlci1jb250YWluZXIgew0KICBtaW4taGVpZ2h0OiAxMDB2aDsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y1ZjdmYSAwJSwgI2MzY2ZlMiAxMDAlKTsNCiAgZm9udC1mYW1pbHk6ICdIZWx2ZXRpY2EgTmV1ZScsIEFyaWFsLCBzYW5zLXNlcmlmOw0KfQ0KDQovKiDpobbpg6jlr7zoiKogKi8NCi50b3AtbmF2IHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgcGFkZGluZzogMCA0MHB4Ow0KICBwb3NpdGlvbjogc3RpY2t5Ow0KICB0b3A6IDA7DQogIHotaW5kZXg6IDEwMDsNCn0NCg0KLm5hdi1jb250ZW50IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBoZWlnaHQ6IDcwcHg7DQogIG1heC13aWR0aDogMTQwMHB4Ow0KICBtYXJnaW46IDAgYXV0bzsNCn0NCg0KLmxvZ28tc2VjdGlvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTVweDsNCn0NCg0KLm5hdi1sb2dvIHsNCiAgaGVpZ2h0OiA0MHB4Ow0KfQ0KDQouY29tcGFueS1uYW1lIHsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCn0NCg0KLm5hdi1hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAyMHB4Ow0KfQ0KDQovKiDkuLvlhoXlrrnljLrln58gKi8NCi5tYWluLWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBtYXgtd2lkdGg6IDE0MDBweDsNCiAgbWFyZ2luOiAwIGF1dG87DQogIHBhZGRpbmc6IDQwcHg7DQogIGdhcDogNDBweDsNCiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDExMHB4KTsNCn0NCg0KLyog56e75Yqo56uv5biD5bGA5LyY5YyWICovDQoubWFpbi1jb250ZW50Lm1vYmlsZS1sYXlvdXQgew0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBnYXA6IDIwcHg7DQogIG1heC13aWR0aDogMTAwJTsNCn0NCg0KLyog56e75Yqo56uv5qyi6L+O5Yy65Z+fICovDQoubW9iaWxlLXdlbGNvbWUgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLndlbGNvbWUtaGVhZGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoud2VsY29tZS1oZWFkZXIgLndlbGNvbWUtaWNvbiB7DQogIHdpZHRoOiA2MHB4Ow0KICBoZWlnaHQ6IDYwcHg7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIG1hcmdpbjogMCBhdXRvIDE1cHg7DQp9DQoNCi53ZWxjb21lLWhlYWRlciAud2VsY29tZS1pY29uIGkgew0KICBmb250LXNpemU6IDI4cHg7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQoud2VsY29tZS1oZWFkZXIgaDIgew0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBtYXJnaW46IDAgMCAxMHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQoud2VsY29tZS1oZWFkZXIgLndlbGNvbWUtZGVzYyB7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbjogMDsNCn0NCg0KLyog6L+b5bqm5oyH56S65ZmoICovDQoucHJvZ3Jlc3MtaW5kaWNhdG9yIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCn0NCg0KLnByb2dyZXNzLWJhciB7DQogIGhlaWdodDogNnB4Ow0KICBiYWNrZ3JvdW5kOiAjZjBmMGYwOw0KICBib3JkZXItcmFkaXVzOiAzcHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5wcm9ncmVzcy1maWxsIHsNCiAgaGVpZ2h0OiAxMDAlOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlOw0KfQ0KDQoucHJvZ3Jlc3MtdGV4dCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzdmOGM4ZDsNCn0NCg0KLyog56e75Yqo56uv6KGo5Y2V5a655ZmoICovDQoubW9iaWxlLWZvcm0tY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi8qIOenu+WKqOerr+ihqOWNleagh+mimCAqLw0KLm1vYmlsZS1mb3JtLWhlYWRlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGNvbG9yOiAjZmZmOw0KICBwYWRkaW5nOiAyMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5tb2JpbGUtZm9ybS1oZWFkZXIgaDMgew0KICBtYXJnaW46IDAgMCA4cHg7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLm1vYmlsZS1mb3JtLWhlYWRlciAuZm9ybS1zdWJ0aXRsZSB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBvcGFjaXR5OiAwLjk7DQp9DQoNCi8qIOenu+WKqOerr+ihqOWNleWGheWuuSAqLw0KLm1vYmlsZS1mb3JtLWNvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQovKiDnp7vliqjnq6/ooajljZXljaHniYcgKi8NCi5tb2JpbGUtZm9ybS1jYXJkIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoubW9iaWxlLWZvcm0tY2FyZCAuY2FyZC1oZWFkZXIgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQp9DQoNCi5tb2JpbGUtZm9ybS1jYXJkIC5jYXJkLWhlYWRlciBpIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBjb2xvcjogIzY2N2VlYTsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5tb2JpbGUtZm9ybS1jYXJkIC5jYXJkLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgZmxleDogMTsNCn0NCg0KLm1vYmlsZS1mb3JtLWNhcmQgLmFkZC12aXNpdG9yLWJ0biB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KfQ0KDQoubW9iaWxlLWZvcm0tY2FyZCAuY2FyZC1jb250ZW50IHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLyog56e75Yqo56uv6KGo5Y2V6aG5ICovDQoubW9iaWxlLWZvcm0taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5tb2JpbGUtZm9ybS1pdGVtIC5lbC1mb3JtLWl0ZW1fX2xhYmVsIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCiAgcGFkZGluZy1ib3R0b206IDhweDsNCn0NCg0KLyog56e75Yqo56uv6L6T5YWl5qGGICovDQoubW9iaWxlLWlucHV0IC5lbC1pbnB1dF9faW5uZXIgew0KICBoZWlnaHQ6IDQ0cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICBwYWRkaW5nOiAwIDE1cHg7DQp9DQoNCi5tb2JpbGUtaW5wdXQgLmVsLXRleHRhcmVhX19pbm5lciB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICBwYWRkaW5nOiAxMnB4IDE1cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQp9DQoNCi8qIOenu+WKqOerr+aXpeacn+mAieaLqeWZqCAqLw0KLm1vYmlsZS1kYXRlLXBpY2tlciAuZWwtaW5wdXRfX2lubmVyIHsNCiAgaGVpZ2h0OiA0NHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLyog56e75Yqo56uv6K6/5a6i6aG5ICovDQoubW9iaWxlLXZpc2l0b3ItaXRlbSB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcGFkZGluZzogMTVweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KDQoubW9iaWxlLXZpc2l0b3ItaXRlbTpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLm1vYmlsZS12aXNpdG9yLWl0ZW0gLnZpc2l0b3ItaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQoubW9iaWxlLXZpc2l0b3ItaXRlbSAudmlzaXRvci1iYWRnZSB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGNvbG9yOiAjNmM3NTdkOw0KICBwYWRkaW5nOiA0cHggMTJweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoubW9iaWxlLXZpc2l0b3ItaXRlbSAudmlzaXRvci1iYWRnZS5wcmltYXJ5IHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgY29sb3I6ICNmZmY7DQogIGJvcmRlcjogbm9uZTsNCn0NCg0KLm1vYmlsZS12aXNpdG9yLWl0ZW0gLnJlbW92ZS1idG4gew0KICB3aWR0aDogMjhweDsNCiAgaGVpZ2h0OiAyOHB4Ow0KICBwYWRkaW5nOiAwOw0KfQ0KDQovKiDnp7vliqjnq6/mk43kvZzljLrln58gKi8NCi5tb2JpbGUtYWN0aW9ucyB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQouc3VibWl0LWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDEycHg7DQp9DQoNCi5zdWNjZXNzLWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDEycHg7DQp9DQoNCi8qIOenu+WKqOerr+aMiemSriAqLw0KLm1vYmlsZS1zdWJtaXQtYnRuLA0KLm1vYmlsZS1yZXNldC1idG4sDQoubW9iaWxlLWFjdGlvbi1idG4gew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiA0OHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiBub25lOw0KfQ0KDQoubW9iaWxlLXN1Ym1pdC1idG4gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBjb2xvcjogI2ZmZjsNCn0NCg0KLm1vYmlsZS1zdWJtaXQtYnRuOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzVhNmZkOCAwJSwgIzZhNDE5MCAxMDAlKTsNCn0NCg0KLm1vYmlsZS1yZXNldC1idG4gew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBjb2xvcjogIzZjNzU3ZDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2RjZGZlNjsNCn0NCg0KLm1vYmlsZS1yZXNldC1idG46aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBjb2xvcjogIzQ5NTA1NzsNCn0NCg0KLm1vYmlsZS1hY3Rpb24tYnRuIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgY29sb3I6ICM2NjdlZWE7DQogIGJvcmRlcjogMXB4IHNvbGlkICM2NjdlZWE7DQp9DQoNCi5tb2JpbGUtYWN0aW9uLWJ0bjpob3ZlciB7DQogIGJhY2tncm91bmQ6ICM2NjdlZWE7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAvKiDpobbpg6jlr7zoiKrnp7vliqjnq6/kvJjljJYgKi8NCiAgLnRvcC1uYXYgew0KICAgIHBhZGRpbmc6IDAgMjBweDsNCiAgfQ0KDQogIC5uYXYtY29udGVudCB7DQogICAgaGVpZ2h0OiA2MHB4Ow0KICB9DQoNCiAgLmNvbXBhbnktbmFtZSB7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICB9DQoNCiAgLm5hdi1hY3Rpb25zIHsNCiAgICBnYXA6IDEwcHg7DQogIH0NCg0KICAvKiDkuLvlhoXlrrnljLrln5/np7vliqjnq6/kvJjljJYgKi8NCiAgLm1haW4tY29udGVudCB7DQogICAgcGFkZGluZzogMTVweDsNCiAgICBnYXA6IDE1cHg7DQogIH0NCg0KICAubWFpbi1jb250ZW50Lm1vYmlsZS1sYXlvdXQgew0KICAgIHBhZGRpbmc6IDEwcHg7DQogICAgZ2FwOiAxNXB4Ow0KICB9DQoNCiAgLyog56e75Yqo56uv5qyi6L+O5Yy65Z+f5LyY5YyWICovDQogIC5tb2JpbGUtd2VsY29tZSB7DQogICAgcGFkZGluZzogMTVweDsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICB9DQoNCiAgLndlbGNvbWUtaGVhZGVyIC53ZWxjb21lLWljb24gew0KICAgIHdpZHRoOiA1MHB4Ow0KICAgIGhlaWdodDogNTBweDsNCiAgfQ0KDQogIC53ZWxjb21lLWhlYWRlciAud2VsY29tZS1pY29uIGkgew0KICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgfQ0KDQogIC53ZWxjb21lLWhlYWRlciBoMiB7DQogICAgZm9udC1zaXplOiAyMHB4Ow0KICB9DQoNCiAgLyog56e75Yqo56uv6KGo5Y2V5LyY5YyWICovDQogIC5tb2JpbGUtZm9ybS1jb250YWluZXIgew0KICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1oZWFkZXIgew0KICAgIHBhZGRpbmc6IDE1cHg7DQogIH0NCg0KICAubW9iaWxlLWZvcm0taGVhZGVyIGgzIHsNCiAgICBmb250LXNpemU6IDE2cHg7DQogIH0NCg0KICAubW9iaWxlLWZvcm0tY29udGVudCB7DQogICAgcGFkZGluZzogMTVweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1jYXJkIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICB9DQoNCiAgLm1vYmlsZS1mb3JtLWNhcmQgLmNhcmQtaGVhZGVyIHsNCiAgICBwYWRkaW5nOiAxMnB4IDE1cHg7DQogIH0NCg0KICAubW9iaWxlLWZvcm0tY2FyZCAuY2FyZC1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAxNXB4Ow0KICB9DQoNCiAgLyog56e75Yqo56uv6K6/5a6i6aG55LyY5YyWICovDQogIC5tb2JpbGUtdmlzaXRvci1pdGVtIHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogIH0NCg0KICAvKiDnp7vliqjnq6/mk43kvZzljLrln5/kvJjljJYgKi8NCiAgLm1vYmlsZS1hY3Rpb25zIHsNCiAgICBwYWRkaW5nOiAxNXB4Ow0KICB9DQoNCiAgLm1vYmlsZS1zdWJtaXQtYnRuLA0KICAubW9iaWxlLXJlc2V0LWJ0biwNCiAgLm1vYmlsZS1hY3Rpb24tYnRuIHsNCiAgICBoZWlnaHQ6IDQ0cHg7DQogICAgZm9udC1zaXplOiAxNXB4Ow0KICB9DQp9DQoNCi8qIOi2heWwj+Wxj+W5leS8mOWMliAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7DQogIC50b3AtbmF2IHsNCiAgICBwYWRkaW5nOiAwIDE1cHg7DQogIH0NCg0KICAubmF2LWNvbnRlbnQgew0KICAgIGhlaWdodDogNTVweDsNCiAgfQ0KDQogIC5jb21wYW55LW5hbWUgew0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgfQ0KDQogIC5tYWluLWNvbnRlbnQubW9iaWxlLWxheW91dCB7DQogICAgcGFkZGluZzogOHB4Ow0KICB9DQoNCiAgLm1vYmlsZS13ZWxjb21lIHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICB9DQoNCiAgLm1vYmlsZS1mb3JtLWhlYWRlciB7DQogICAgcGFkZGluZzogMTJweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICB9DQoNCiAgLm1vYmlsZS1hY3Rpb25zIHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICB9DQp9DQoNCi8qIOW3puS+p+S/oeaBr+mdouadvyAqLw0KLmluZm8tcGFuZWwgew0KICBmbGV4OiAwIDAgNDAwcHg7DQp9DQoNCi53ZWxjb21lLWNhcmQgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICBwYWRkaW5nOiAzMnB4Ow0KICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgaGVpZ2h0OiBmaXQtY29udGVudDsNCiAgcG9zaXRpb246IHN0aWNreTsNCiAgdG9wOiAxMTBweDsNCn0NCg0KLndlbGNvbWUtaWNvbiB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLndlbGNvbWUtaWNvbiBpIHsNCiAgZm9udC1zaXplOiA2NHB4Ow0KICBjb2xvcjogIzM0OThkYjsNCn0NCg0KLndlbGNvbWUtY2FyZCBoMiB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICMyYzNlNTA7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIGZvbnQtc2l6ZTogMjhweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLndlbGNvbWUtZGVzYyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgbGluZS1oZWlnaHQ6IDEuNjsNCn0NCg0KLyog5rWB56iL5q2l6aqkICovDQoucHJvY2Vzcy1zdGVwcyB7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi5zdGVwLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDE2cHg7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQogIHBhZGRpbmc6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQp9DQoNCi5zdGVwLWl0ZW0uYWN0aXZlIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM0OThkYiwgIzI5ODBiOSk7DQogIGNvbG9yOiB3aGl0ZTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDhweCk7DQp9DQoNCi5zdGVwLWl0ZW06bm90KC5hY3RpdmUpIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgY29sb3I6ICM2Yzc1N2Q7DQp9DQoNCi5zdGVwLWNpcmNsZSB7DQogIHdpZHRoOiAzMnB4Ow0KICBoZWlnaHQ6IDMycHg7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE0cHg7DQogIGZsZXgtc2hyaW5rOiAwOw0KfQ0KDQouc3RlcC1pdGVtLmFjdGl2ZSAuc3RlcC1jaXJjbGUgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLnN0ZXAtaXRlbTpub3QoLmFjdGl2ZSkgLnN0ZXAtY2lyY2xlIHsNCiAgYmFja2dyb3VuZDogI2RlZTJlNjsNCiAgY29sb3I6ICM2Yzc1N2Q7DQp9DQoNCi5zdGVwLXRleHQgaDQgew0KICBtYXJnaW46IDAgMCA0cHggMDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQouc3RlcC10ZXh0IHAgew0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgb3BhY2l0eTogMC44Ow0KfQ0KDQovKiDlronlhajmj5DnpLogKi8NCi5zZWN1cml0eS10aXBzIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMzQ5OGRiOw0KfQ0KDQouc2VjdXJpdHktdGlwcyBoNCB7DQogIG1hcmdpbjogMCAwIDEycHggMDsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5zZWN1cml0eS10aXBzIHVsIHsNCiAgbWFyZ2luOiAwOw0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQogIGNvbG9yOiAjNmM3NTdkOw0KfQ0KDQouc2VjdXJpdHktdGlwcyBsaSB7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS40Ow0KfQ0KDQovKiDlj7PkvqfooajljZXpnaLmnb8gKi8NCi5mb3JtLXBhbmVsIHsNCiAgZmxleDogMTsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTZweDsNCiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi8qIOi/m+W6puaMh+ekuuWZqCAqLw0KLnByb2dyZXNzLWluZGljYXRvciB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDI0cHggMzJweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5wcm9ncmVzcy1iYXIgew0KICBoZWlnaHQ6IDZweDsNCiAgYmFja2dyb3VuZDogI2U5ZWNlZjsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5wcm9ncmVzcy1maWxsIHsNCiAgaGVpZ2h0OiAxMDAlOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNDk4ZGIsICMyOTgwYjkpOw0KICBib3JkZXItcmFkaXVzOiAzcHg7DQogIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTsNCn0NCg0KLnByb2dyZXNzLXRleHQgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjNmM3NTdkOw0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi8qIOihqOWNleWGheWuuSAqLw0KLmZvcm0tY29udGVudCB7DQogIHBhZGRpbmc6IDMycHg7DQogIG1heC1oZWlnaHQ6IGNhbGMoMTAwdmggLSAzMDBweCk7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5mb3JtLWhlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCn0NCg0KLmZvcm0taGVhZGVyIGgzIHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouZm9ybS1oZWFkZXIgcCB7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLyog6aqM6K+B5pa55byP572R5qC8ICovDQoudmVyaWZ5LWdyaWQgew0KICBkaXNwbGF5OiBncmlkOw0KICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpOw0KICBnYXA6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi52ZXJpZnktY2FyZCB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMjRweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQp9DQoNCi52ZXJpZnktY2FyZDpob3ZlciB7DQogIGJvcmRlci1jb2xvcjogIzM0OThkYjsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm94LXNoYWRvdzogMCA0cHggMTZweCByZ2JhKDUyLCAxNTIsIDIxOSwgMC4xNSk7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCn0NCg0KLnZlcmlmeS1jYXJkLnNlbGVjdGVkIHsNCiAgYm9yZGVyLWNvbG9yOiAjMzQ5OGRiOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzQ5OGRiLCAjMjk4MGI5KTsNCiAgY29sb3I6IHdoaXRlOw0KICBib3gtc2hhZG93OiAwIDhweCAyNHB4IHJnYmEoNTIsIDE1MiwgMjE5LCAwLjMpOw0KfQ0KDQoudmVyaWZ5LWljb24gew0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KfQ0KDQoudmVyaWZ5LWljb24gaSB7DQogIGZvbnQtc2l6ZTogNDhweDsNCiAgY29sb3I6ICMzNDk4ZGI7DQp9DQoNCi52ZXJpZnktY2FyZC5zZWxlY3RlZCAudmVyaWZ5LWljb24gaSB7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLnZlcmlmeS1jYXJkIGg0IHsNCiAgbWFyZ2luOiAwIDAgOHB4IDA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLnZlcmlmeS1jYXJkIHAgew0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgb3BhY2l0eTogMC44Ow0KfQ0KDQoudmVyaWZ5LXN0YXR1cyB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiAxMnB4Ow0KICByaWdodDogMTJweDsNCiAgd2lkdGg6IDI0cHg7DQogIGhlaWdodDogMjRweDsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOw0KICBib3JkZXItcmFkaXVzOiA1MCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQovKiDpqozor4Hmk43kvZzljLrln58gKi8NCi52ZXJpZnktb3BlcmF0aW9uIHsNCiAgbWFyZ2luLXRvcDogMzJweDsNCiAgcGFkZGluZzogMjRweDsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCn0NCg0KLm9wZXJhdGlvbi1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5vcGVyYXRpb24taGVhZGVyIGg0IHsNCiAgbWFyZ2luOiAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQovKiDouqvku73ooajljZUgKi8NCi5pZGVudGl0eS1mb3JtIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQp9DQoNCi8qIOi6q+S7veivgeivu+WNoeWZqCAqLw0KLmlkLWNhcmQtcmVhZGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQoucmVhZGVyLXZpc3VhbCB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBwYWRkaW5nOiA0MHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KfQ0KDQoucmVhZGVyLWFuaW1hdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5yb3RhdGluZyB7DQogIGFuaW1hdGlvbjogcm90YXRlIDJzIGxpbmVhciBpbmZpbml0ZTsNCn0NCg0KQGtleWZyYW1lcyByb3RhdGUgew0KICBmcm9tIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH0NCiAgdG8geyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9DQp9DQoNCi5yZWFkZXItYW5pbWF0aW9uIGkgew0KICBmb250LXNpemU6IDY0cHg7DQogIGNvbG9yOiAjMzQ5OGRiOw0KfQ0KDQoucmVhZGVyLXZpc3VhbCBoNCB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5yZWFkZXItdmlzdWFsIHAgew0KICBjb2xvcjogIzdmOGM4ZDsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnJlYWRlci10aXBzIHsNCiAgbWFyZ2luOiAyNHB4IDA7DQp9DQoNCi8qIOS6uuiEuOivhuWIqyAqLw0KLmZhY2UtcmVjb2duaXRpb24gew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5jYW1lcmEtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDQwcHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQp9DQoNCi5jYW1lcmEtZnJhbWUgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHdpZHRoOiAyODBweDsNCiAgaGVpZ2h0OiAyMTBweDsNCiAgbWFyZ2luOiAwIGF1dG8gMjRweDsNCiAgYm9yZGVyOiAzcHggc29saWQgIzM0OThkYjsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5jYW1lcmEtb3ZlcmxheSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTZweDsNCn0NCg0KLmZhY2Utb3V0bGluZSB7DQogIHdpZHRoOiAxMjBweDsNCiAgaGVpZ2h0OiAxNTBweDsNCiAgYm9yZGVyOiAycHggZGFzaGVkICMzNDk4ZGI7DQogIGJvcmRlci1yYWRpdXM6IDYwcHg7DQogIG9wYWNpdHk6IDAuNjsNCn0NCg0KLmNhbWVyYS1pY29uIHsNCiAgZm9udC1zaXplOiAzMnB4Ow0KICBjb2xvcjogIzM0OThkYjsNCn0NCg0KLnNjYW5uaW5nLWxpbmUgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMDsNCiAgbGVmdDogMDsNCiAgcmlnaHQ6IDA7DQogIGhlaWdodDogMnB4Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50LCAjMzQ5OGRiLCB0cmFuc3BhcmVudCk7DQogIGFuaW1hdGlvbjogc2NhbiAycyBlYXNlLWluLW91dCBpbmZpbml0ZTsNCn0NCg0KQGtleWZyYW1lcyBzY2FuIHsNCiAgMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7IH0NCiAgNTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDIwNnB4KTsgfQ0KICAxMDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOyB9DQp9DQoNCi8qIOihqOWNleWIhue7hCAqLw0KLmZvcm0tc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi5zZWN0aW9uLXRpdGxlIHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgcGFkZGluZy1ib3R0b206IDhweDsNCiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5zZWN0aW9uLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCn0NCg0KLyog5ZCM6KGM5Lq65ZGYICovDQoubm8tY29tcGFuaW9ucyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogNDBweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi5uby1jb21wYW5pb25zIGkgew0KICBmb250LXNpemU6IDQ4cHg7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIGNvbG9yOiAjYmRjM2M3Ow0KfQ0KDQouY29tcGFuaW9ucy10YWJsZSB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLyog5oiQ5Yqf6aG16Z2iICovDQouc3VjY2Vzcy1jb250ZW50IHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouc3VjY2Vzcy1pY29uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnN1Y2Nlc3MtaWNvbiBpIHsNCiAgZm9udC1zaXplOiA4MHB4Ow0KICBjb2xvcjogIzI3YWU2MDsNCn0NCg0KLnN1Y2Nlc3MtY29udGVudCBoNCB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDI4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQp9DQoNCi5zdWNjZXNzLW1lc3NhZ2Ugew0KICBjb2xvcjogIzdmOGM4ZDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQoucmVnaXN0ZXItc3VtbWFyeSB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQogIHRleHQtYWxpZ246IGxlZnQ7DQp9DQoNCi5yZWdpc3Rlci1zdW1tYXJ5IGg1IHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouYWNjZXNzLWNyZWRlbnRpYWwgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KfQ0KDQouYWNjZXNzLWNyZWRlbnRpYWwgaDUgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5jcmVkZW50aWFsLWNhcmQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDI0cHg7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoucXItY29kZS1jb250YWluZXIgew0KICBmbGV4OiAwIDAgMTIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnFyLWNvZGUgew0KICB3aWR0aDogMTIwcHg7DQogIGhlaWdodDogMTIwcHg7DQogIGJvcmRlcjogMnB4IHNvbGlkICNlOWVjZWY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLnFyLWNvZGUgaSB7DQogIGZvbnQtc2l6ZTogNDhweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi5xci1jb2RlLWlkIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzZjNzU3ZDsNCiAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTsNCiAgbWFyZ2luOiAwOw0KfQ0KDQouY3JlZGVudGlhbC1pbmZvIHsNCiAgZmxleDogMTsNCn0NCg0KLmNyZWRlbnRpYWwtaW5mbyBoNiB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQp9DQoNCi5jcmVkZW50aWFsLWluZm8gdWwgew0KICBtYXJnaW46IDA7DQogIHBhZGRpbmctbGVmdDogMjBweDsNCiAgY29sb3I6ICM2Yzc1N2Q7DQp9DQoNCi5jcmVkZW50aWFsLWluZm8gbGkgew0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCn0NCg0KLyog5bqV6YOo5pON5L2c5oyJ6ZKuICovDQouZm9ybS1hY3Rpb25zIHsNCiAgcGFkZGluZzogMjRweCAzMnB4Ow0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2U5ZWNlZjsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouZmluYWwtYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTZweDsNCn0NCg0KLyog5ZON5bqU5byP6K6+6K6hICovDQpAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7DQogIC5tYWluLWNvbnRlbnQgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAyNHB4Ow0KICB9DQogIA0KICAuaW5mby1wYW5lbCB7DQogICAgZmxleDogbm9uZTsNCiAgfQ0KICANCiAgLndlbGNvbWUtY2FyZCB7DQogICAgcG9zaXRpb246IHN0YXRpYzsNCiAgfQ0KICANCiAgLnZlcmlmeS1ncmlkIHsNCiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsNCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLm1haW4tY29udGVudCB7DQogICAgcGFkZGluZzogMjBweDsNCiAgfQ0KICANCiAgLm5hdi1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAwIDIwcHg7DQogIH0NCiAgDQogIC5jb21wYW55LW5hbWUgew0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgfQ0KICANCiAgLmZvcm0tY29udGVudCB7DQogICAgcGFkZGluZzogMjBweDsNCiAgfQ0KICANCiAgLmNyZWRlbnRpYWwtY2FyZCB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIH0NCiAgDQogIC5mb3JtLWFjdGlvbnMgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAxNnB4Ow0KICB9DQp9DQoNCi8qIEVsZW1lbnQgVUkg5qC35byP6KaG55uWICovDQouZWwtZm9ybS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLmVsLWlucHV0X19pbm5lciB7DQogIGhlaWdodDogNDRweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQouZWwtaW5wdXRfX2lubmVyOmZvY3VzIHsNCiAgYm9yZGVyLWNvbG9yOiAjMzQ5OGRiOw0KICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSg1MiwgMTUyLCAyMTksIDAuMSk7DQp9DQoNCi5lbC1zZWxlY3Qgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLmVsLWRhdGUtZWRpdG9yIHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5lbC1idXR0b24tLWxhcmdlIHsNCiAgaGVpZ2h0OiA0NHB4Ow0KICBwYWRkaW5nOiAxMnB4IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouZWwtYnV0dG9uLS1wcmltYXJ5IHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM0OThkYiwgIzI5ODBiOSk7DQogIGJvcmRlcjogbm9uZTsNCn0NCg0KLmVsLWJ1dHRvbi0tcHJpbWFyeTpob3ZlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyOTgwYjksICMxZjRlNzkpOw0KfQ0KDQouZWwtZGVzY3JpcHRpb25zIHsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQouZWwtdGFibGUgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi5lbC1hbGVydCB7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg=="}, null]}