# 访客自主登记系统

## 概述

访客自主登记系统是智慧园区管理平台的一个重要组成部分，为访客提供便捷的自助登记服务。该系统支持多种身份验证方式，能够快速完成访客信息收集和审核流程。

## 功能特性

### 1. 多种身份验证方式
- **身份证读卡器验证**：支持二代身份证自动读取
- **人脸识别验证**：使用摄像头进行人脸识别（需要硬件支持）
- **手动输入**：支持手动填写身份信息
- **护照验证**：支持护照等其他证件验证

### 2. 完整的信息收集
- **访客基本信息**：姓名、手机号、证件类型、证件号码、所属公司
- **访问详情**：来访事由、被访人姓名、被访部门、车牌号码
- **时间安排**：预计到访时间、预计离开时间
- **同行人员**：支持添加多位同行人员信息

### 3. 三步登记流程
1. **身份验证**：选择验证方式并完成身份确认
2. **填写信息**：完善访问信息和同行人员
3. **完成登记**：提交申请并获取临时凭证

## 访问方式

### 1. 独立页面访问
```
直接访问：http://your-domain/asc/visitor/self-register
```

### 2. 通过管理系统访问
- 登录智慧园区管理系统
- 进入访客管理模块
- 点击"新增"按钮

### 3. 公共访问（推荐）
- 访问指南页面：`http://your-domain/visitor-guide.html`
- 该页面无需登录，适合向访客提供

## 技术实现

### 前端技术栈
- **Vue.js 2.x**：前端框架
- **Element UI**：UI组件库
- **Vue Router**：路由管理
- **Vuex**：状态管理

### 关键文件
```
ruoyi-ui/src/views/asc/visitor/self-register.vue  # 主页面组件
ruoyi-ui/src/api/asc/visitor.js                   # API接口
ruoyi-ui/src/router/index.js                      # 路由配置
ruoyi-ui/src/permission.js                        # 权限控制
```

### API接口
```javascript
// 提交访客登记
POST /asc/visitor/registration

// 请求数据格式
{
  registration: {
    reasonForVisit: "商务洽谈",
    hostEmployeeName: "张三",
    departmentVisited: "技术部",
    vehiclePlateNumber: "京A12345",
    plannedEntryDatetime: "2025-06-26 09:00:00",
    plannedExitDatetime: "2025-06-26 17:00:00",
    primaryContactName: "李四",
    primaryContactPhone: "13800138000"
  },
  attendeesList: [
    {
      visitorName: "李四",
      visitorPhone: "13800138000",
      visitorIdCard: "110101199001011234",
      visitorCompany: "ABC公司",
      isPrimary: "1"
    }
  ]
}
```

## 配置说明

### 1. 路由配置
在 `router/index.js` 中添加了独立的路由：
```javascript
{
  path: '/asc/visitor/self-register',
  component: () => import('@/views/asc/visitor/self-register'),
  hidden: true,
  meta: { title: '访客自主登记' }
}
```

### 2. 权限配置
在 `permission.js` 中将自主登记页面加入白名单：
```javascript
const whiteList = ['/login', '/register', '/hr/recruit', '/qrcode', '/asc/visitor/self-register']
```

## 使用指南

### 访客使用流程

1. **访问页面**
   - 直接访问自主登记页面
   - 或通过访问指南页面进入

2. **选择验证方式**
   - 根据现场条件选择合适的身份验证方式
   - 推荐使用身份证读卡器（如有设备）

3. **填写信息**
   - 完善个人基本信息
   - 填写详细的访问信息
   - 如有同行人员，请添加相关信息

4. **提交申请**
   - 检查信息无误后提交
   - 获取临时凭证二维码
   - 等待审核通过

### 管理员配置

1. **硬件设备**
   - 配置身份证读卡器（可选）
   - 配置摄像头设备（用于人脸识别，可选）

2. **系统配置**
   - 确保后端API正常运行
   - 配置审核流程和通知机制

## 注意事项

1. **数据安全**
   - 所有个人信息都经过加密传输
   - 遵循隐私保护相关法规

2. **兼容性**
   - 支持现代浏览器（Chrome、Firefox、Safari、Edge）
   - 移动端友好设计

3. **网络要求**
   - 需要稳定的网络连接
   - 建议在园区内部网络环境使用

4. **硬件要求**
   - 身份证读卡器需要专用驱动支持
   - 人脸识别需要摄像头设备

## 后续优化建议

1. **功能增强**
   - 集成二维码生成库，生成真实二维码
   - 添加打印功能支持
   - 支持访客照片上传

2. **用户体验**
   - 添加访问历史查询
   - 支持短信通知功能
   - 优化移动端显示效果

3. **系统集成**
   - 与门禁系统集成
   - 与停车管理系统集成
   - 添加访客轨迹跟踪

## 联系支持

如需技术支持或有问题反馈，请联系：
- 技术支持：园区信息技术部
- 服务时间：周一至周五 8:00-18:00
