{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750989656917}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_visitor", "require", "name", "data", "submitting", "registrationCompleted", "formData", "visitors", "phone", "idCard", "company", "isMainContact", "visitInfo", "reasonForVisit", "hostEmployeeName", "departmentVisited", "vehiclePlateNumber", "plannedEntryDatetime", "plannedExitDatetime", "visitRules", "required", "message", "trigger", "min", "max", "pattern", "registrationId", "entryPickerOptions", "shortcuts", "text", "onClick", "picker", "$emit", "Date", "date", "setTime", "getTime", "setDate", "getDate", "setHours", "disabledDate", "time", "oneHourBefore", "now", "thirtyDaysLater", "exitPickerOptions", "computed", "totalVisitors", "length", "mainContact", "methods", "addVisitor", "$message", "warning", "push", "success", "removeVisitor", "index", "splice", "formatDateForBackend", "dateStr", "isNaN", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "onArrivalTimeChange", "value", "console", "log", "arrivalTime", "departureTime", "onDepartureTimeChange", "submitForm", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "submitData", "response", "_t", "w", "_context", "n", "p", "$refs", "visitForm", "validate", "validateVisitors", "a", "validateTimes", "registration", "primaryContactName", "trim", "primaryContactPhone", "hostEmployeeId", "totalCompanions", "attendeesList", "map", "visitor", "visitorName", "visitorPhone", "visitorIdCard", "toUpperCase", "visitorCompany", "isPrimary", "visitorAvatarPhoto", "JSON", "stringify", "submitVisitorRegistration", "v", "code", "error", "msg", "f", "i", "visitorTitle", "phonePattern", "test", "idPattern", "j", "entryTime", "exitTime", "visitDuration", "maxDuration", "resetForm", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "printCredential", "info", "parseTime", "showHelp", "$alert", "dangerouslyUseHTMLString", "contactService", "_this3", "catch"], "sources": ["src/views/asc/visitor/self-register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <!-- <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" /> -->\r\n          <span class=\"company-name\">高义钢铁访客登记系统</span>\r\n        </div>\r\n        <!-- <div class=\"nav-actions\">\r\n          <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button>\r\n        </div> -->\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 - 移动端优化 -->\r\n    <div class=\"main-content mobile-layout\">\r\n      <!-- 移动端欢迎区域 -->\r\n      <!-- <div class=\"mobile-welcome\" v-if=\"!registrationCompleted\"> -->\r\n        <!-- <div class=\"welcome-header\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>访客登记</h2>\r\n          <p class=\"welcome-desc\">请填写以下信息完成登记</p>\r\n        </div> -->\r\n\r\n        <!-- 简化的进度指示 -->\r\n        <!-- <div class=\"progress-indicator\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: registrationCompleted ? '100%' : '60%' }\"></div>\r\n          </div>\r\n          <div class=\"progress-text\">\r\n            {{ registrationCompleted ? '登记完成' : '正在填写信息...' }}\r\n          </div>\r\n        </div> -->\r\n      <!-- </div> -->\r\n\r\n      <!-- 表单容器 - 全屏移动端布局 -->\r\n      <div class=\"mobile-form-container\">\r\n        <!-- 表单标题 -->\r\n        <div class=\"mobile-form-header\" v-if=\"!registrationCompleted\">\r\n          <h3><i class=\"el-icon-edit-outline\"></i> 填写登记信息</h3>\r\n          <p class=\"form-subtitle\">共{{ totalVisitors }}人，请确保信息准确</p>\r\n        </div>\r\n\r\n        <!-- 成功页面标题 -->\r\n        <div class=\"mobile-form-header\" v-else>\r\n          <h3><i class=\"el-icon-circle-check\"></i> 登记成功</h3>\r\n          <p class=\"form-subtitle\">您的访客登记已提交成功</p>\r\n        </div>\r\n\r\n        <!-- 移动端表单内容 -->\r\n        <div class=\"mobile-form-content\" v-if=\"!registrationCompleted\">\r\n          <el-form ref=\"visitForm\" :model=\"formData\" :rules=\"visitRules\"\r\n                   label-width=\"100px\" class=\"mobile-visit-form\">\r\n\r\n            <!-- 访问信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"card-title\">访问信息</span>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <el-form-item label=\"来访事由\" prop=\"visitInfo.reasonForVisit\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                            type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit\r\n                            class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"被访人\" prop=\"visitInfo.hostEmployeeName\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                            maxlength=\"20\" class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"被访部门\" prop=\"visitInfo.departmentVisited\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                            maxlength=\"50\" class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"车牌号\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.vehiclePlateNumber\" placeholder=\"如有车辆请填写（可选）\"\r\n                            class=\"mobile-input\" />\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 时间信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span class=\"card-title\">访问时间</span>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <el-form-item label=\"到访时间\" prop=\"visitInfo.plannedEntryDatetime\" class=\"mobile-form-item\">\r\n                  <el-date-picker\r\n                    v-model=\"formData.visitInfo.plannedEntryDatetime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"选择到访时间\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    :picker-options=\"entryPickerOptions\"\r\n                    @change=\"onArrivalTimeChange\"\r\n                    class=\"mobile-date-picker\"\r\n                  />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"离开时间\" prop=\"visitInfo.plannedExitDatetime\" class=\"mobile-form-item\">\r\n                  <el-date-picker\r\n                    v-model=\"formData.visitInfo.plannedExitDatetime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"选择离开时间\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    :picker-options=\"exitPickerOptions\"\r\n                    @change=\"onDepartureTimeChange\"\r\n                    class=\"mobile-date-picker\"\r\n                  />\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 访客信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span class=\"card-title\">访客信息（共{{ totalVisitors }}人）</span>\r\n                <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addVisitor\"\r\n                           :disabled=\"formData.visitors.length >= 10\" class=\"add-visitor-btn\">\r\n                  添加\r\n                </el-button>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <div v-for=\"(visitor, index) in formData.visitors\" :key=\"index\" class=\"mobile-visitor-item\">\r\n                  <div class=\"visitor-header\">\r\n                    <div class=\"visitor-badge\" :class=\"{ primary: index === 0 }\">\r\n                      {{ index === 0 ? '主联系人' : `访客${index + 1}` }}\r\n                    </div>\r\n                    <el-button v-if=\"index > 0\" size=\"mini\" type=\"danger\" icon=\"el-icon-delete\"\r\n                               @click=\"removeVisitor(index)\" circle class=\"remove-btn\"></el-button>\r\n                  </div>\r\n\r\n                  <div class=\"visitor-form\">\r\n                    <el-form-item label=\"姓名\" :prop=\"`visitors.${index}.name`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.name\" placeholder=\"请输入姓名\" maxlength=\"20\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"手机号\" :prop=\"`visitors.${index}.phone`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.phone\" placeholder=\"请输入手机号\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"身份证号\" :prop=\"`visitors.${index}.idCard`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.idCard\" placeholder=\"请输入身份证号\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"公司名称\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.company\" placeholder=\"请输入公司名称（可选）\"\r\n                                maxlength=\"100\" class=\"mobile-input\" />\r\n                    </el-form-item>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 登记成功页面 -->\r\n        <div class=\"success-content\" v-if=\"registrationCompleted\">\r\n          <div class=\"success-icon\">\r\n            <i class=\"el-icon-circle-check\"></i>\r\n          </div>\r\n\r\n          <h4>登记成功！</h4>\r\n          <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n\r\n          <!-- 登记信息摘要 -->\r\n          <div class=\"register-summary\">\r\n            <h5>登记信息摘要</h5>\r\n            <el-descriptions :column=\"2\" border>\r\n              <el-descriptions-item label=\"主联系人\">\r\n                {{ mainContact.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"联系电话\">\r\n                {{ mainContact.phone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访人\">\r\n                {{ formData.visitInfo.hostEmployeeName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访部门\">\r\n                {{ formData.visitInfo.departmentVisited }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                {{ formData.visitInfo.reasonForVisit }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计到访时间\">\r\n                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计离开时间\">\r\n                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"访客总数\">\r\n                {{ totalVisitors }} 人\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n\r\n          <!-- 访问凭证 -->\r\n          <!-- <div class=\"access-credential\" v-if=\"registrationId\">\r\n            <h5>访问凭证</h5>\r\n            <div class=\"credential-card\">\r\n              <div class=\"qr-code-container\">\r\n                <div class=\"qr-code\">\r\n                  <i class=\"el-icon-qrcode\"></i>\r\n                </div>\r\n                <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n              </div>\r\n              <div class=\"credential-info\">\r\n                <h6>使用说明</h6>\r\n                <ul>\r\n                  <li>请保存此二维码截图</li>\r\n                  <li>审核通过后可用于园区门禁</li>\r\n                  <li>凭证仅限当次访问使用</li>\r\n                  <li>如有疑问请联系园区前台</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div> -->\r\n        </div>\r\n\r\n        <!-- 移动端底部操作区 -->\r\n        <div class=\"mobile-actions\">\r\n          <!-- 表单提交按钮 -->\r\n          <div v-if=\"!registrationCompleted\" class=\"submit-actions\">\r\n            <el-button type=\"primary\" @click=\"submitForm\" size=\"large\" :loading=\"submitting\"\r\n                       class=\"mobile-submit-btn\">\r\n              <i class=\"el-icon-check\" v-if=\"!submitting\"></i>\r\n              <i class=\"el-icon-loading\" v-if=\"submitting\"></i>\r\n              {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}\r\n            </el-button>\r\n\r\n            <el-button @click=\"resetForm\" size=\"large\" class=\"mobile-reset-btn\">\r\n              <i class=\"el-icon-refresh-left\"></i>\r\n              重置表单\r\n            </el-button>\r\n          </div>\r\n\r\n          <!-- 成功后操作按钮 -->\r\n          <div v-if=\"registrationCompleted\" class=\"success-actions\">\r\n            <el-button type=\"primary\" @click=\"printCredential\" size=\"large\" class=\"mobile-action-btn\">\r\n              <i class=\"el-icon-printer\"></i>\r\n              打印凭证\r\n            </el-button>\r\n            <el-button @click=\"resetForm\" size=\"large\" class=\"mobile-action-btn\">\r\n              <i class=\"el-icon-refresh\"></i>\r\n              重新登记\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      submitting: false,\r\n      registrationCompleted: false,\r\n\r\n      // 表单数据 - 与微信端保持一致的结构\r\n      formData: {\r\n        // 访客列表（第一位为主联系人）\r\n        visitors: [\r\n          {\r\n            name: '',\r\n            phone: '',\r\n            idCard: '',\r\n            company: '',\r\n            isMainContact: true\r\n          }\r\n        ],\r\n        // 来访信息\r\n        visitInfo: {\r\n          reasonForVisit: '',\r\n          hostEmployeeName: '',\r\n          departmentVisited: '',\r\n          vehiclePlateNumber: '',\r\n          plannedEntryDatetime: '',\r\n          plannedExitDatetime: ''\r\n        }\r\n      },\r\n\r\n      // 表单验证规则 - 与微信端保持一致\r\n      visitRules: {\r\n        'visitInfo.reasonForVisit': [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.hostEmployeeName': [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.departmentVisited': [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.plannedEntryDatetime': [\r\n          { required: true, message: '请选择预计来访时间', trigger: 'change' }\r\n        ],\r\n        'visitInfo.plannedExitDatetime': [\r\n          { required: true, message: '请选择预计离开时间', trigger: 'change' }\r\n        ],\r\n        // 动态访客验证规则\r\n        'visitors.0.name': [\r\n          { required: true, message: '请输入主联系人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.phone': [\r\n          { required: true, message: '请输入主联系人手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.idCard': [\r\n          { required: true, message: '请输入主联系人身份证号', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n\r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '中午12点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(12, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 总访客人数\r\n    totalVisitors() {\r\n      return this.formData.visitors.length;\r\n    },\r\n\r\n    // 主联系人信息\r\n    mainContact() {\r\n      return this.formData.visitors[0] || {};\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 添加访客\r\n    addVisitor() {\r\n      if (this.formData.visitors.length >= 10) {\r\n        this.$message.warning('最多只能添加10名访客');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.push({\r\n        name: '',\r\n        phone: '',\r\n        idCard: '',\r\n        company: '',\r\n        isMainContact: false\r\n      });\r\n\r\n      this.$message.success('已添加访客');\r\n    },\r\n\r\n    // 移除访客\r\n    removeVisitor(index) {\r\n      if (index === 0) {\r\n        this.$message.warning('不能删除主联系人');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.splice(index, 1);\r\n      this.$message.success('已移除访客');\r\n    },\r\n\r\n    // 格式化日期为后端期望的格式\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n\r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.formData.visitInfo.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n\r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n\r\n        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.formData.visitInfo.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.formData.visitInfo.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 提交表单 */\r\n    async submitForm() {\r\n      try {\r\n        // 验证表单\r\n        await this.$refs.visitForm.validate();\r\n\r\n        // 验证访客信息\r\n        if (!this.validateVisitors()) {\r\n          return;\r\n        }\r\n\r\n        // 验证时间\r\n        if (!this.validateTimes()) {\r\n          return;\r\n        }\r\n\r\n        this.submitting = true;\r\n\r\n        // 获取主联系人信息\r\n        const mainContact = this.formData.visitors[0];\r\n\r\n        const submitData = {\r\n          // VisitRegistrations 对象\r\n          registration: {\r\n            primaryContactName: mainContact.name.trim(),\r\n            primaryContactPhone: mainContact.phone.trim(),\r\n            reasonForVisit: this.formData.visitInfo.reasonForVisit,\r\n            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,\r\n            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n            departmentVisited: this.formData.visitInfo.departmentVisited,\r\n            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),\r\n            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),\r\n            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',\r\n            totalCompanions: this.formData.visitors.length - 1\r\n          },\r\n          // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n          attendeesList: this.formData.visitors.map((visitor, index) => ({\r\n            visitorName: visitor.name.trim(),\r\n            visitorPhone: visitor.phone.trim(),\r\n            visitorIdCard: visitor.idCard.trim().toUpperCase(),\r\n            visitorCompany: (visitor.company || '').trim(),\r\n            isPrimary: index === 0 ? \"1\" : \"0\", // 第一个访客必须是主联系人\r\n            visitorAvatarPhoto: null // 暂时不支持头像上传\r\n          }))\r\n        };\r\n\r\n        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n\r\n        // 调用API提交数据\r\n        const response = await submitVisitorRegistration(submitData);\r\n\r\n        if (response.code === 200) {\r\n          this.registrationId = response.data || 'VR' + Date.now();\r\n          this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n          this.registrationCompleted = true;\r\n        } else {\r\n          this.$message.error(response.msg || '登记失败，请重试');\r\n        }\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error);\r\n        this.$message.error('登记失败，请检查网络连接');\r\n      } finally {\r\n        this.submitting = false;\r\n      }\r\n    },\r\n\r\n    // 验证访客信息\r\n    validateVisitors() {\r\n      for (let i = 0; i < this.formData.visitors.length; i++) {\r\n        const visitor = this.formData.visitors[i];\r\n        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n\r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号格式（与后端保持一致）\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idCard || visitor.idCard.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n\r\n        const idCard = visitor.idCard.trim().toUpperCase();\r\n\r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idCard = visitor.idCard.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 验证时间\r\n    validateTimes() {\r\n      if (!this.formData.visitInfo.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.formData.visitInfo.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 检查来访时间不能早于当前时间1小时前\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (entryTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能早于当前时间1小时前');\r\n        return false;\r\n      }\r\n\r\n      // 检查离开时间必须晚于来访时间\r\n      if (exitTime <= entryTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      // 检查访问时长不能超过24小时\r\n      const visitDuration = exitTime.getTime() - entryTime.getTime();\r\n      const maxDuration = 24 * 60 * 60 * 1000; // 24小时\r\n      if (visitDuration > maxDuration) {\r\n        this.$message.error('单次访问时长不能超过24小时');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 重置所有数据\r\n        this.registrationCompleted = false;\r\n\r\n        this.formData = {\r\n          visitors: [\r\n            {\r\n              name: '',\r\n              phone: '',\r\n              idCard: '',\r\n              company: '',\r\n              isMainContact: true\r\n            }\r\n          ],\r\n          visitInfo: {\r\n            reasonForVisit: '',\r\n            hostEmployeeName: '',\r\n            departmentVisited: '',\r\n            vehiclePlateNumber: '',\r\n            plannedEntryDatetime: '',\r\n            plannedExitDatetime: ''\r\n          }\r\n        };\r\n\r\n        this.registrationId = null;\r\n\r\n        this.$message.success('表单已重置');\r\n      });\r\n    },\r\n\r\n    // 打印凭证\r\n    printCredential() {\r\n      this.$message.info('打印功能开发中...');\r\n    },\r\n\r\n    // 格式化时间显示\r\n    parseTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    },\r\n\r\n    // 显示帮助信息\r\n    showHelp() {\r\n      this.$alert(`\r\n        <div style=\"text-align: left;\">\r\n          <h4>使用说明</h4>\r\n          <p><strong>1. 填写基本信息</strong></p>\r\n          <ul>\r\n            <li>请如实填写来访事由、被访人等信息</li>\r\n            <li>主联系人信息必须准确，用于后续联系</li>\r\n          </ul>\r\n          <p><strong>2. 添加同行人员</strong></p>\r\n          <ul>\r\n            <li>如有同行人员，请点击\"添加访客\"按钮</li>\r\n            <li>每位访客都需要填写完整身份信息</li>\r\n          </ul>\r\n          <p><strong>3. 选择访问时间</strong></p>\r\n          <ul>\r\n            <li>请合理安排访问时间，避免过长逗留</li>\r\n            <li>如需延长访问时间，请联系被访人</li>\r\n          </ul>\r\n          <p><strong>4. 审核与通行</strong></p>\r\n          <ul>\r\n            <li>提交后等待审核，通过后可凭二维码进入园区</li>\r\n            <li>请按照预约时间准时到访</li>\r\n          </ul>\r\n        </div>\r\n      `, '使用帮助', {\r\n        confirmButtonText: '知道了',\r\n        dangerouslyUseHTMLString: true\r\n      });\r\n    },\r\n\r\n    // 联系客服\r\n    contactService() {\r\n      this.$confirm('需要帮助吗？', '联系客服', {\r\n        confirmButtonText: '拨打电话',\r\n        cancelButtonText: '取消',\r\n        type: 'info'\r\n      }).then(() => {\r\n        // 这里可以实现拨打客服电话的功能\r\n        this.$message({\r\n          type: 'info',\r\n          message: '客服电话：400-xxx-xxxx'\r\n        });\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 移动端布局优化 */\r\n.main-content.mobile-layout {\r\n  flex-direction: column;\r\n  padding: 20px;\r\n  gap: 20px;\r\n  max-width: 100%;\r\n}\r\n\r\n/* 移动端欢迎区域 */\r\n.mobile-welcome {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.welcome-header {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.welcome-header .welcome-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 15px;\r\n}\r\n\r\n.welcome-header .welcome-icon i {\r\n  font-size: 28px;\r\n  color: #fff;\r\n}\r\n\r\n.welcome-header h2 {\r\n  font-size: 24px;\r\n  color: #2c3e50;\r\n  margin: 0 0 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-header .welcome-desc {\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  margin-top: 20px;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #f0f0f0;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 移动端表单容器 */\r\n.mobile-form-container {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 移动端表单标题 */\r\n.mobile-form-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.mobile-form-header h3 {\r\n  margin: 0 0 8px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.mobile-form-header .form-subtitle {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 移动端表单内容 */\r\n.mobile-form-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 移动端表单卡片 */\r\n.mobile-form-card {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  overflow: hidden;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.mobile-form-card .card-header {\r\n  background: #fff;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.mobile-form-card .card-header i {\r\n  font-size: 16px;\r\n  color: #667eea;\r\n  margin-right: 8px;\r\n}\r\n\r\n.mobile-form-card .card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.mobile-form-card .add-visitor-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.mobile-form-card .card-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 移动端表单项 */\r\n.mobile-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.mobile-form-item .el-form-item__label {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  line-height: 1.4;\r\n  padding-bottom: 8px;\r\n}\r\n\r\n/* 移动端输入框 */\r\n.mobile-input .el-input__inner {\r\n  height: 44px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 0 15px;\r\n}\r\n\r\n.mobile-input .el-textarea__inner {\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 12px 15px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 移动端日期选择器 */\r\n.mobile-date-picker .el-input__inner {\r\n  height: 44px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 移动端访客项 */\r\n.mobile-visitor-item {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n  position: relative;\r\n}\r\n\r\n.mobile-visitor-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mobile-visitor-item .visitor-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.mobile-visitor-item .visitor-badge {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n  padding: 4px 12px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.mobile-visitor-item .visitor-badge.primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  border: none;\r\n}\r\n\r\n.mobile-visitor-item .remove-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n/* 移动端操作区域 */\r\n.mobile-actions {\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n.submit-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.success-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n/* 移动端按钮 */\r\n.mobile-submit-btn,\r\n.mobile-reset-btn,\r\n.mobile-action-btn {\r\n  width: 100%;\r\n  height: 48px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  border-radius: 8px;\r\n  border: none;\r\n}\r\n\r\n.mobile-submit-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n}\r\n\r\n.mobile-submit-btn:hover {\r\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\r\n}\r\n\r\n.mobile-reset-btn {\r\n  background: #fff;\r\n  color: #6c757d;\r\n  border: 1px solid #dcdfe6;\r\n}\r\n\r\n.mobile-reset-btn:hover {\r\n  background: #f8f9fa;\r\n  color: #495057;\r\n}\r\n\r\n.mobile-action-btn {\r\n  background: #fff;\r\n  color: #667eea;\r\n  border: 1px solid #667eea;\r\n}\r\n\r\n.mobile-action-btn:hover {\r\n  background: #667eea;\r\n  color: #fff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  /* 顶部导航移动端优化 */\r\n  .top-nav {\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .nav-content {\r\n    height: 60px;\r\n  }\r\n\r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-actions {\r\n    gap: 10px;\r\n  }\r\n\r\n  /* 主内容区域移动端优化 */\r\n  .main-content {\r\n    padding: 15px;\r\n    gap: 15px;\r\n  }\r\n\r\n  .main-content.mobile-layout {\r\n    padding: 10px;\r\n    gap: 15px;\r\n  }\r\n\r\n  /* 移动端欢迎区域优化 */\r\n  .mobile-welcome {\r\n    padding: 15px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .welcome-header .welcome-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .welcome-header .welcome-icon i {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .welcome-header h2 {\r\n    font-size: 20px;\r\n  }\r\n\r\n  /* 移动端表单优化 */\r\n  .mobile-form-container {\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .mobile-form-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-form-header h3 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .mobile-form-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-form-card {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .mobile-form-card .card-header {\r\n    padding: 12px 15px;\r\n  }\r\n\r\n  .mobile-form-card .card-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  /* 移动端访客项优化 */\r\n  .mobile-visitor-item {\r\n    padding: 12px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  /* 移动端操作区域优化 */\r\n  .mobile-actions {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-submit-btn,\r\n  .mobile-reset-btn,\r\n  .mobile-action-btn {\r\n    height: 44px;\r\n    font-size: 15px;\r\n  }\r\n}\r\n\r\n/* 超小屏幕优化 */\r\n@media (max-width: 480px) {\r\n  .top-nav {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .nav-content {\r\n    height: 55px;\r\n  }\r\n\r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .main-content.mobile-layout {\r\n    padding: 8px;\r\n  }\r\n\r\n  .mobile-welcome {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-form-header {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-form-content {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-actions {\r\n    padding: 12px;\r\n  }\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA+QA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,qBAAA;MAEA;MACAC,QAAA;QACA;QACAC,QAAA,GACA;UACAL,IAAA;UACAM,KAAA;UACAC,MAAA;UACAC,OAAA;UACAC,aAAA;QACA,EACA;QACA;QACAC,SAAA;UACAC,cAAA;UACAC,gBAAA;UACAC,iBAAA;UACAC,kBAAA;UACAC,oBAAA;UACAC,mBAAA;QACA;MACA;MAEA;MACAC,UAAA;QACA,6BACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,+BACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,gCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,mCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,kCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA;QACA,oBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,qBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,OAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,sBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAI,cAAA;MAEA;MACAC,kBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACAA,MAAA,CAAAC,KAAA,aAAAC,IAAA;UACA;QACA;UACAJ,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACAJ,IAAA,CAAAK,QAAA;YACAR,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;QACAM,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAC,aAAA,GAAAT,IAAA,CAAAU,GAAA;UACA,IAAAC,eAAA,GAAAX,IAAA,CAAAU,GAAA;UACA,OAAAF,IAAA,CAAAL,OAAA,KAAAM,aAAA,IAAAD,IAAA,CAAAL,OAAA,KAAAQ,eAAA;QACA;MACA;MAEAC,iBAAA;QACAjB,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAK,QAAA;YACA,IAAAL,IAAA,CAAAE,OAAA,KAAAH,IAAA,CAAAU,GAAA;cACAT,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACA;YACAP,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAK,QAAA;YACA,IAAAL,IAAA,CAAAE,OAAA,KAAAH,IAAA,CAAAU,GAAA;cACAT,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACA;YACAP,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;QACAM,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAG,eAAA,GAAAX,IAAA,CAAAU,GAAA;UACA,OAAAF,IAAA,CAAAL,OAAA,KAAAH,IAAA,CAAAU,GAAA,eAAAF,IAAA,CAAAL,OAAA,KAAAQ,eAAA;QACA;MACA;IACA;EACA;EAEAE,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAzC,QAAA,CAAAC,QAAA,CAAAyC,MAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAA3C,QAAA,CAAAC,QAAA;IACA;EACA;EAEA2C,OAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAA7C,QAAA,CAAAC,QAAA,CAAAyC,MAAA;QACA,KAAAI,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA/C,QAAA,CAAAC,QAAA,CAAA+C,IAAA;QACApD,IAAA;QACAM,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,aAAA;MACA;MAEA,KAAAyC,QAAA,CAAAG,OAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MACA,IAAAA,KAAA;QACA,KAAAL,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA/C,QAAA,CAAAC,QAAA,CAAAmD,MAAA,CAAAD,KAAA;MACA,KAAAL,QAAA,CAAAG,OAAA;IACA;IAEA;IACAI,oBAAA,WAAAA,qBAAAC,OAAA;MACA,KAAAA,OAAA;MAEA,IAAA1B,IAAA,OAAAD,IAAA,CAAA2B,OAAA;MACA,IAAAC,KAAA,CAAA3B,IAAA,CAAAE,OAAA;MAEA,IAAA0B,IAAA,GAAA5B,IAAA,CAAA6B,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAA/B,IAAA,CAAAgC,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAA/B,IAAA,CAAAI,OAAA,IAAA6B,QAAA;MACA,IAAAE,KAAA,GAAAJ,MAAA,CAAA/B,IAAA,CAAAoC,QAAA,IAAAH,QAAA;MACA,IAAAI,OAAA,GAAAN,MAAA,CAAA/B,IAAA,CAAAsC,UAAA,IAAAL,QAAA;MACA,IAAAM,OAAA,GAAAR,MAAA,CAAA/B,IAAA,CAAAwC,UAAA,IAAAP,QAAA;MAEA,UAAAQ,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IAEA,eACAG,mBAAA,WAAAA,oBAAAC,KAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,KAAA;MACA;MACA,IAAAA,KAAA,UAAAvE,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA,IAAA8D,WAAA,OAAA/C,IAAA,CAAA4C,KAAA;QACA,IAAAI,aAAA,OAAAhD,IAAA,CAAA+C,WAAA,CAAA5C,OAAA;QAEA,IAAA0B,IAAA,GAAAmB,aAAA,CAAAlB,WAAA;QACA,IAAAC,KAAA,GAAAC,MAAA,CAAAgB,aAAA,CAAAf,QAAA,QAAAC,QAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAAgB,aAAA,CAAA3C,OAAA,IAAA6B,QAAA;QACA,IAAAE,KAAA,GAAAJ,MAAA,CAAAgB,aAAA,CAAAX,QAAA,IAAAH,QAAA;QACA,IAAAI,OAAA,GAAAN,MAAA,CAAAgB,aAAA,CAAAT,UAAA,IAAAL,QAAA;QACA,IAAAM,OAAA,GAAAR,MAAA,CAAAgB,aAAA,CAAAP,UAAA,IAAAP,QAAA;QAEA,KAAA7D,QAAA,CAAAM,SAAA,CAAAM,mBAAA,MAAAyD,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;MACA;IACA;IAEA,eACAS,qBAAA,WAAAA,sBAAAL,KAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,KAAA;MACA;MACA,IAAAA,KAAA,SAAAvE,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,IAAA+D,WAAA,OAAA/C,IAAA,MAAA3B,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,IAAAgE,aAAA,OAAAhD,IAAA,CAAA4C,KAAA;QAEA,IAAAI,aAAA,IAAAD,WAAA;UACA,KAAA5B,QAAA,CAAAC,OAAA;UACA,KAAA/C,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA;MACA;IACA;IAEA,WACAiE,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAxC,WAAA,EAAAyC,UAAA,EAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGAX,KAAA,CAAAa,KAAA,CAAAC,SAAA,CAAAC,QAAA;YAAA;cAAA,IAGAf,KAAA,CAAAgB,gBAAA;gBAAAN,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAO,CAAA;YAAA;cAAA,IAKAjB,KAAA,CAAAkB,aAAA;gBAAAR,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAO,CAAA;YAAA;cAIAjB,KAAA,CAAAhF,UAAA;;cAEA;cACA6C,WAAA,GAAAmC,KAAA,CAAA9E,QAAA,CAAAC,QAAA;cAEAmF,UAAA;gBACA;gBACAa,YAAA;kBACAC,kBAAA,EAAAvD,WAAA,CAAA/C,IAAA,CAAAuG,IAAA;kBACAC,mBAAA,EAAAzD,WAAA,CAAAzC,KAAA,CAAAiG,IAAA;kBACA5F,cAAA,EAAAuE,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAC,cAAA;kBACAC,gBAAA,EAAAsE,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAE,gBAAA;kBACA6F,cAAA;kBAAA;kBACA5F,iBAAA,EAAAqE,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAG,iBAAA;kBACAE,oBAAA,EAAAmE,KAAA,CAAAzB,oBAAA,CAAAyB,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAK,oBAAA;kBACAC,mBAAA,EAAAkE,KAAA,CAAAzB,oBAAA,CAAAyB,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAM,mBAAA;kBACAF,kBAAA,EAAAoE,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAI,kBAAA;kBACA4F,eAAA,EAAAxB,KAAA,CAAA9E,QAAA,CAAAC,QAAA,CAAAyC,MAAA;gBACA;gBACA;gBACA6D,aAAA,EAAAzB,KAAA,CAAA9E,QAAA,CAAAC,QAAA,CAAAuG,GAAA,WAAAC,OAAA,EAAAtD,KAAA;kBAAA;oBACAuD,WAAA,EAAAD,OAAA,CAAA7G,IAAA,CAAAuG,IAAA;oBACAQ,YAAA,EAAAF,OAAA,CAAAvG,KAAA,CAAAiG,IAAA;oBACAS,aAAA,EAAAH,OAAA,CAAAtG,MAAA,CAAAgG,IAAA,GAAAU,WAAA;oBACAC,cAAA,GAAAL,OAAA,CAAArG,OAAA,QAAA+F,IAAA;oBACAY,SAAA,EAAA5D,KAAA;oBAAA;oBACA6D,kBAAA;kBACA;gBAAA;cACA;cAEAxC,OAAA,CAAAC,GAAA,YAAAwC,IAAA,CAAAC,SAAA,CAAA9B,UAAA;;cAEA;cAAAI,QAAA,CAAAC,CAAA;cAAA,OACA,IAAA0B,kCAAA,EAAA/B,UAAA;YAAA;cAAAC,QAAA,GAAAG,QAAA,CAAA4B,CAAA;cAEA,IAAA/B,QAAA,CAAAgC,IAAA;gBACAvC,KAAA,CAAA1D,cAAA,GAAAiE,QAAA,CAAAxF,IAAA,WAAA8B,IAAA,CAAAU,GAAA;gBACAyC,KAAA,CAAAhC,QAAA,CAAAG,OAAA,IAAAoB,MAAA,CAAAS,KAAA,CAAArC,aAAA;gBACAqC,KAAA,CAAA/E,qBAAA;cACA;gBACA+E,KAAA,CAAAhC,QAAA,CAAAwE,KAAA,CAAAjC,QAAA,CAAAkC,GAAA;cACA;cAAA/B,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAA4B,CAAA;cAEA5C,OAAA,CAAA8C,KAAA,YAAAhC,EAAA;cACAR,KAAA,CAAAhC,QAAA,CAAAwE,KAAA;YAAA;cAAA9B,QAAA,CAAAE,CAAA;cAEAZ,KAAA,CAAAhF,UAAA;cAAA,OAAA0F,QAAA,CAAAgC,CAAA;YAAA;cAAA,OAAAhC,QAAA,CAAAO,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAW,gBAAA,WAAAA,iBAAA;MACA,SAAA2B,CAAA,MAAAA,CAAA,QAAAzH,QAAA,CAAAC,QAAA,CAAAyC,MAAA,EAAA+E,CAAA;QACA,IAAAhB,OAAA,QAAAzG,QAAA,CAAAC,QAAA,CAAAwH,CAAA;QACA,IAAAC,YAAA,GAAAD,CAAA,iCAAApD,MAAA,CAAAoD,CAAA;;QAEA;QACA,KAAAhB,OAAA,CAAA7G,IAAA,IAAA6G,OAAA,CAAA7G,IAAA,CAAAuG,IAAA;UACA,KAAArD,QAAA,CAAAwE,KAAA,sBAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;QAEA,IAAAjB,OAAA,CAAA7G,IAAA,CAAAuG,IAAA,GAAAzD,MAAA,QAAA+D,OAAA,CAAA7G,IAAA,CAAAuG,IAAA,GAAAzD,MAAA;UACA,KAAAI,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;;QAEA;QACA,KAAAjB,OAAA,CAAAvG,KAAA,IAAAuG,OAAA,CAAAvG,KAAA,CAAAiG,IAAA;UACA,KAAArD,QAAA,CAAAwE,KAAA,sBAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;;QAEA;QACA,IAAAC,YAAA;QACA,KAAAA,YAAA,CAAAC,IAAA,CAAAnB,OAAA,CAAAvG,KAAA,CAAAiG,IAAA;UACA,KAAArD,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;;QAEA;QACA,KAAAjB,OAAA,CAAAtG,MAAA,IAAAsG,OAAA,CAAAtG,MAAA,CAAAgG,IAAA;UACA,KAAArD,QAAA,CAAAwE,KAAA,sBAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;QAEA,IAAAvH,MAAA,GAAAsG,OAAA,CAAAtG,MAAA,CAAAgG,IAAA,GAAAU,WAAA;;QAEA;QACA,IAAA1G,MAAA,CAAAuC,MAAA;UACA,IAAAmF,SAAA;UACA,KAAAA,SAAA,CAAAD,IAAA,CAAAzH,MAAA;YACA,KAAA2C,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA;YACA;UACA;QACA;;QAEA;QACA,SAAAI,CAAA,MAAAA,CAAA,QAAA9H,QAAA,CAAAC,QAAA,CAAAyC,MAAA,EAAAoF,CAAA;UACA,IAAAL,CAAA,KAAAK,CAAA,IAAArB,OAAA,CAAAtG,MAAA,CAAAgG,IAAA,GAAAU,WAAA,YAAA7G,QAAA,CAAAC,QAAA,CAAA6H,CAAA,EAAA3H,MAAA,CAAAgG,IAAA,GAAAU,WAAA;YACA,KAAA/D,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA,wBAAArD,MAAA,CAAAyD,CAAA,kBAAAA,CAAA;YACA;UACA;QACA;;QAEA;QACA,SAAAA,EAAA,MAAAA,EAAA,QAAA9H,QAAA,CAAAC,QAAA,CAAAyC,MAAA,EAAAoF,EAAA;UACA,IAAAL,CAAA,KAAAK,EAAA,IAAArB,OAAA,CAAAvG,KAAA,CAAAiG,IAAA,YAAAnG,QAAA,CAAAC,QAAA,CAAA6H,EAAA,EAAA5H,KAAA,CAAAiG,IAAA;YACA,KAAArD,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA,wBAAArD,MAAA,CAAAyD,EAAA,kBAAAA,EAAA;YACA;UACA;QACA;;QAEA;QACArB,OAAA,CAAA7G,IAAA,GAAA6G,OAAA,CAAA7G,IAAA,CAAAuG,IAAA;QACAM,OAAA,CAAAvG,KAAA,GAAAuG,OAAA,CAAAvG,KAAA,CAAAiG,IAAA;QACAM,OAAA,CAAAtG,MAAA,GAAAsG,OAAA,CAAAtG,MAAA,CAAAgG,IAAA,GAAAU,WAAA;QACAJ,OAAA,CAAArG,OAAA,IAAAqG,OAAA,CAAArG,OAAA,QAAA+F,IAAA;MACA;MAEA;IACA;IAEA;IACAH,aAAA,WAAAA,cAAA;MACA,UAAAhG,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,KAAAmC,QAAA,CAAAwE,KAAA;QACA;MACA;MAEA,UAAAtH,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA,KAAAkC,QAAA,CAAAwE,KAAA;QACA;MACA;MAEA,IAAAS,SAAA,OAAApG,IAAA,MAAA3B,QAAA,CAAAM,SAAA,CAAAK,oBAAA;MACA,IAAAqH,QAAA,OAAArG,IAAA,MAAA3B,QAAA,CAAAM,SAAA,CAAAM,mBAAA;MACA,IAAAyB,GAAA,OAAAV,IAAA;;MAEA;MACA,IAAAS,aAAA,OAAAT,IAAA,CAAAU,GAAA,CAAAP,OAAA;MACA,IAAAiG,SAAA,GAAA3F,aAAA;QACA,KAAAU,QAAA,CAAAwE,KAAA;QACA;MACA;;MAEA;MACA,IAAAU,QAAA,IAAAD,SAAA;QACA,KAAAjF,QAAA,CAAAwE,KAAA;QACA;MACA;;MAEA;MACA,IAAAW,aAAA,GAAAD,QAAA,CAAAlG,OAAA,KAAAiG,SAAA,CAAAjG,OAAA;MACA,IAAAoG,WAAA;MACA,IAAAD,aAAA,GAAAC,WAAA;QACA,KAAApF,QAAA,CAAAwE,KAAA;QACA;MACA;MAEA;IACA;IAEA;IACAa,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;QACAL,MAAA,CAAArI,qBAAA;QAEAqI,MAAA,CAAApI,QAAA;UACAC,QAAA,GACA;YACAL,IAAA;YACAM,KAAA;YACAC,MAAA;YACAC,OAAA;YACAC,aAAA;UACA,EACA;UACAC,SAAA;YACAC,cAAA;YACAC,gBAAA;YACAC,iBAAA;YACAC,kBAAA;YACAC,oBAAA;YACAC,mBAAA;UACA;QACA;QAEAwH,MAAA,CAAAhH,cAAA;QAEAgH,MAAA,CAAAtF,QAAA,CAAAG,OAAA;MACA;IACA;IAEA;IACAyF,eAAA,WAAAA,gBAAA;MACA,KAAA5F,QAAA,CAAA6F,IAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAAzG,IAAA;MACA,KAAAA,IAAA;MACA,IAAAP,IAAA,OAAAD,IAAA,CAAAQ,IAAA;MACA,IAAAoB,KAAA,CAAA3B,IAAA,CAAAE,OAAA;MAEA,IAAA0B,IAAA,GAAA5B,IAAA,CAAA6B,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAA/B,IAAA,CAAAgC,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAA/B,IAAA,CAAAI,OAAA,IAAA6B,QAAA;MACA,IAAAE,KAAA,GAAAJ,MAAA,CAAA/B,IAAA,CAAAoC,QAAA,IAAAH,QAAA;MACA,IAAAI,OAAA,GAAAN,MAAA,CAAA/B,IAAA,CAAAsC,UAAA,IAAAL,QAAA;MAEA,UAAAQ,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA;IACA;IAEA;IACA4E,QAAA,WAAAA,SAAA;MACA,KAAAC,MAAA,+8CAwBA;QACAR,iBAAA;QACAS,wBAAA;MACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAZ,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;QACAQ,MAAA,CAAAnG,QAAA;UACA0F,IAAA;UACAzH,OAAA;QACA;MACA,GAAAmI,KAAA;QACA;MAAA,CACA;IACA;EACA;AACA", "ignoreList": []}]}