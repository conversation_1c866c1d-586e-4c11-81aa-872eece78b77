{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750990421754}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_visitor", "require", "name", "data", "_this", "submitting", "registrationCompleted", "queryDialog", "visible", "queryForm", "queryType", "queryValue", "queryRules", "required", "message", "trigger", "validator", "rule", "value", "callback", "Error", "idPattern", "test", "phonePattern", "queryLoading", "queryExecuted", "queryResults", "formData", "visitors", "phone", "idCard", "company", "isMainContact", "visitInfo", "reasonForVisit", "hostEmployeeName", "departmentVisited", "vehiclePlateNumber", "plannedEntryDatetime", "plannedExitDatetime", "visitRules", "min", "max", "pattern", "registrationId", "entryPickerOptions", "shortcuts", "text", "onClick", "picker", "$emit", "Date", "date", "setTime", "getTime", "setDate", "getDate", "setHours", "disabledDate", "time", "oneHourBefore", "now", "thirtyDaysLater", "exitPickerOptions", "computed", "totalVisitors", "length", "mainContact", "methods", "addVisitor", "$message", "warning", "push", "success", "removeVisitor", "index", "splice", "formatDateForBackend", "dateStr", "isNaN", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "onArrivalTimeChange", "console", "log", "arrivalTime", "departureTime", "onDepartureTimeChange", "submitForm", "_this2", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "submitData", "response", "_t", "w", "_context", "n", "p", "$refs", "visitForm", "validate", "validateVisitors", "a", "validateTimes", "registration", "primaryContactName", "trim", "primaryContactPhone", "hostEmployeeId", "totalCompanions", "attendeesList", "map", "visitor", "visitorName", "visitorPhone", "visitorIdCard", "toUpperCase", "visitorCompany", "isPrimary", "visitorAvatarPhoto", "JSON", "stringify", "submitVisitorRegistration", "v", "code", "error", "msg", "f", "i", "visitorTitle", "j", "entryTime", "exitTime", "visitDuration", "maxDuration", "resetForm", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "printCredential", "info", "parseTime", "showHelp", "$alert", "dangerouslyUseHTMLString", "contactService", "_this4", "catch", "showQueryDialog", "reset<PERSON><PERSON>y", "handleQueryTypeChange", "clearValidate", "handleQuery", "_this5", "_callee2", "today", "queryParams", "_t2", "_context2", "toISOString", "split", "pageNum", "pageSize", "params", "beginTime", "endTime", "idCardNumber", "listRegistrations", "rows", "resetFields", "getStatusText", "status", "statusMap", "getStatusClass", "classMap", "printResults"], "sources": ["src/views/asc/visitor/self-register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <!-- <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" /> -->\r\n          <span class=\"company-name\">高义钢铁访客登记系统</span>\r\n        </div>\r\n        <div class=\"nav-actions\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"showQueryDialog\">查询登记</el-button>\r\n          <!-- <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 - 移动端优化 -->\r\n    <div class=\"main-content mobile-layout\">\r\n      <!-- 移动端欢迎区域 -->\r\n      <!-- <div class=\"mobile-welcome\" v-if=\"!registrationCompleted\"> -->\r\n        <!-- <div class=\"welcome-header\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>访客登记</h2>\r\n          <p class=\"welcome-desc\">请填写以下信息完成登记</p>\r\n        </div> -->\r\n\r\n        <!-- 简化的进度指示 -->\r\n        <!-- <div class=\"progress-indicator\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: registrationCompleted ? '100%' : '60%' }\"></div>\r\n          </div>\r\n          <div class=\"progress-text\">\r\n            {{ registrationCompleted ? '登记完成' : '正在填写信息...' }}\r\n          </div>\r\n        </div> -->\r\n      <!-- </div> -->\r\n\r\n      <!-- 表单容器 - 全屏移动端布局 -->\r\n      <div class=\"mobile-form-container\">\r\n        <!-- 表单标题 -->\r\n        <div class=\"mobile-form-header\" v-if=\"!registrationCompleted\">\r\n          <h3><i class=\"el-icon-edit-outline\"></i> 填写登记信息</h3>\r\n          <p class=\"form-subtitle\">共{{ totalVisitors }}人，请确保信息准确</p>\r\n        </div>\r\n\r\n        <!-- 成功页面标题 -->\r\n        <!-- <div class=\"mobile-form-header\" v-else>\r\n          <h3><i class=\"el-icon-circle-check\"></i> 登记成功</h3>\r\n          <p class=\"form-subtitle\">您的访客登记已提交成功</p>\r\n        </div> -->\r\n\r\n        <!-- 移动端表单内容 -->\r\n        <div class=\"mobile-form-content\" v-if=\"!registrationCompleted\">\r\n          <el-form ref=\"visitForm\" :model=\"formData\" :rules=\"visitRules\"\r\n                   label-width=\"100px\" class=\"mobile-visit-form\">\r\n\r\n            <!-- 访问信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"card-title\">访问信息</span>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <el-form-item label=\"来访事由\" prop=\"visitInfo.reasonForVisit\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                            type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit\r\n                            class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"被访人\" prop=\"visitInfo.hostEmployeeName\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                            maxlength=\"20\" class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"被访部门\" prop=\"visitInfo.departmentVisited\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                            maxlength=\"50\" class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"车牌号\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.vehiclePlateNumber\" placeholder=\"如有车辆请填写（可选）\"\r\n                            class=\"mobile-input\" />\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 时间信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span class=\"card-title\">访问时间</span>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <el-form-item label=\"到访时间\" prop=\"visitInfo.plannedEntryDatetime\" class=\"mobile-form-item\">\r\n                  <el-date-picker\r\n                    v-model=\"formData.visitInfo.plannedEntryDatetime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"选择到访时间\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    :picker-options=\"entryPickerOptions\"\r\n                    @change=\"onArrivalTimeChange\"\r\n                    class=\"mobile-date-picker\"\r\n                  />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"离开时间\" prop=\"visitInfo.plannedExitDatetime\" class=\"mobile-form-item\">\r\n                  <el-date-picker\r\n                    v-model=\"formData.visitInfo.plannedExitDatetime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"选择离开时间\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    :picker-options=\"exitPickerOptions\"\r\n                    @change=\"onDepartureTimeChange\"\r\n                    class=\"mobile-date-picker\"\r\n                  />\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 访客信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span class=\"card-title\">访客信息（共{{ totalVisitors }}人）</span>\r\n                <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addVisitor\"\r\n                           :disabled=\"formData.visitors.length >= 10\" class=\"add-visitor-btn\">\r\n                  添加\r\n                </el-button>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <div v-for=\"(visitor, index) in formData.visitors\" :key=\"index\" class=\"mobile-visitor-item\">\r\n                  <div class=\"visitor-header\">\r\n                    <div class=\"visitor-badge\" :class=\"{ primary: index === 0 }\">\r\n                      {{ index === 0 ? '主联系人' : `访客${index + 1}` }}\r\n                    </div>\r\n                    <el-button v-if=\"index > 0\" size=\"mini\" type=\"danger\" icon=\"el-icon-delete\"\r\n                               @click=\"removeVisitor(index)\" circle class=\"remove-btn\"></el-button>\r\n                  </div>\r\n\r\n                  <div class=\"visitor-form\">\r\n                    <el-form-item label=\"姓名\" :prop=\"`visitors.${index}.name`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.name\" placeholder=\"请输入姓名\" maxlength=\"20\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"手机号\" :prop=\"`visitors.${index}.phone`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.phone\" placeholder=\"请输入手机号\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"身份证号\" :prop=\"`visitors.${index}.idCard`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.idCard\" placeholder=\"请输入身份证号\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"公司名称\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.company\" placeholder=\"请输入公司名称（可选）\"\r\n                                maxlength=\"100\" class=\"mobile-input\" />\r\n                    </el-form-item>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 登记成功页面 -->\r\n        <div class=\"success-content\" v-if=\"registrationCompleted\">\r\n          <div class=\"success-icon\">\r\n            <i class=\"el-icon-circle-check\"></i>\r\n          </div>\r\n\r\n          <h4>登记成功！</h4>\r\n          <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n\r\n          <!-- 登记信息摘要 -->\r\n          <div class=\"register-summary\">\r\n            <h5>登记信息摘要</h5>\r\n            <el-descriptions :column=\"2\" border>\r\n              <el-descriptions-item label=\"主联系人\">\r\n                {{ mainContact.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"联系电话\">\r\n                {{ mainContact.phone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访人\">\r\n                {{ formData.visitInfo.hostEmployeeName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访部门\">\r\n                {{ formData.visitInfo.departmentVisited }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                {{ formData.visitInfo.reasonForVisit }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计到访时间\">\r\n                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计离开时间\">\r\n                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"访客总数\">\r\n                {{ totalVisitors }} 人\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n\r\n          <!-- 访问凭证 -->\r\n          <!-- <div class=\"access-credential\" v-if=\"registrationId\">\r\n            <h5>访问凭证</h5>\r\n            <div class=\"credential-card\">\r\n              <div class=\"qr-code-container\">\r\n                <div class=\"qr-code\">\r\n                  <i class=\"el-icon-qrcode\"></i>\r\n                </div>\r\n                <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n              </div>\r\n              <div class=\"credential-info\">\r\n                <h6>使用说明</h6>\r\n                <ul>\r\n                  <li>请保存此二维码截图</li>\r\n                  <li>审核通过后可用于园区门禁</li>\r\n                  <li>凭证仅限当次访问使用</li>\r\n                  <li>如有疑问请联系园区前台</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div> -->\r\n        </div>\r\n\r\n        <!-- 移动端底部操作区 -->\r\n        <div class=\"mobile-actions\">\r\n          <!-- 表单提交按钮 -->\r\n          <div v-if=\"!registrationCompleted\" class=\"submit-actions\">\r\n            <el-button type=\"primary\" @click=\"submitForm\" size=\"large\" :loading=\"submitting\"\r\n                       class=\"mobile-submit-btn\">\r\n              <i class=\"el-icon-check\" v-if=\"!submitting\"></i>\r\n              <i class=\"el-icon-loading\" v-if=\"submitting\"></i>\r\n              {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}\r\n            </el-button>\r\n\r\n            <el-button @click=\"resetForm\" size=\"large\" class=\"mobile-reset-btn\">\r\n              <i class=\"el-icon-refresh-left\"></i>\r\n              重置表单\r\n            </el-button>\r\n          </div>\r\n\r\n          <!-- 成功后操作按钮 -->\r\n          <div v-if=\"registrationCompleted\" class=\"success-actions\">\r\n            <!-- <el-button type=\"primary\" @click=\"printCredential\" size=\"large\" class=\"mobile-action-btn\">\r\n              <i class=\"el-icon-printer\"></i>\r\n              打印凭证\r\n            </el-button> -->\r\n            <!-- <el-button @click=\"resetForm\" size=\"large\" class=\"mobile-action-btn\">\r\n              <i class=\"el-icon-refresh\"></i>\r\n              重新登记\r\n            </el-button> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 查询登记对话框 -->\r\n    <el-dialog\r\n      title=\"查询今日登记记录\"\r\n      :visible.sync=\"queryDialog.visible\"\r\n      width=\"90%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"query-dialog\"\r\n    >\r\n      <div class=\"query-container\">\r\n        <!-- 查询表单 -->\r\n        <div class=\"query-form-section\">\r\n          <h4><i class=\"el-icon-search\"></i> 请输入查询信息</h4>\r\n          <el-form ref=\"queryForm\" :model=\"queryForm\" :rules=\"queryRules\" label-width=\"100px\">\r\n            <el-form-item label=\"查询方式\" prop=\"queryType\">\r\n              <el-radio-group v-model=\"queryForm.queryType\" @change=\"handleQueryTypeChange\">\r\n                <el-radio label=\"idCard\">身份证号</el-radio>\r\n                <el-radio label=\"phone\">手机号</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"queryForm.queryType === 'idCard' ? '身份证号' : '手机号'\" prop=\"queryValue\">\r\n              <el-input\r\n                v-model=\"queryForm.queryValue\"\r\n                :placeholder=\"queryForm.queryType === 'idCard' ? '请输入身份证号' : '请输入手机号'\"\r\n                maxlength=\"18\"\r\n                clearable\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleQuery\" :loading=\"queryLoading\">\r\n                <i class=\"el-icon-search\"></i> 查询\r\n              </el-button>\r\n              <el-button @click=\"resetQuery\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 查询结果 -->\r\n        <div class=\"query-results-section\" v-if=\"queryResults.length > 0\">\r\n          <h4><i class=\"el-icon-document\"></i> 查询结果（共{{ queryResults.length }}条）</h4>\r\n          <div class=\"results-list\">\r\n            <div v-for=\"(record, index) in queryResults\" :key=\"index\" class=\"result-item\">\r\n              <div class=\"result-header\">\r\n                <div class=\"visitor-info\">\r\n                  <span class=\"visitor-name\">{{ record.primaryContactName }}</span>\r\n                  <span class=\"visitor-phone\">{{ record.primaryContactPhone }}</span>\r\n                </div>\r\n                <div class=\"status-badge\" :class=\"getStatusClass(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"result-details\">\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">来访事由：</span>\r\n                  <span class=\"value\">{{ record.reasonForVisit }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">被访人：</span>\r\n                  <span class=\"value\">{{ record.hostEmployeeName }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">被访部门：</span>\r\n                  <span class=\"value\">{{ record.departmentVisited }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">预计到访：</span>\r\n                  <span class=\"value\">{{ parseTime(record.plannedEntryDatetime) }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">预计离开：</span>\r\n                  <span class=\"value\">{{ parseTime(record.plannedExitDatetime) }}</span>\r\n                </div>\r\n                <div class=\"detail-row\" v-if=\"record.totalCompanions > 0\">\r\n                  <span class=\"label\">同行人数：</span>\r\n                  <span class=\"value\">{{ record.totalCompanions }}人</span>\r\n                </div>\r\n                <div class=\"detail-row\" v-if=\"record.submittedDatetime\">\r\n                  <span class=\"label\">提交时间：</span>\r\n                  <span class=\"value\">{{ parseTime(record.submittedDatetime) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 无结果提示 -->\r\n        <div class=\"no-results\" v-if=\"queryExecuted && queryResults.length === 0\">\r\n          <i class=\"el-icon-document-remove\"></i>\r\n          <h4>未找到登记记录</h4>\r\n          <p>今日暂无相关登记记录，请确认输入信息是否正确</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"queryDialog.visible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"printResults\" v-if=\"queryResults.length > 0\">\r\n          <i class=\"el-icon-printer\"></i> 打印凭证\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration, listRegistrations } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      submitting: false,\r\n      registrationCompleted: false,\r\n\r\n      // 查询对话框相关\r\n      queryDialog: {\r\n        visible: false\r\n      },\r\n      queryForm: {\r\n        queryType: 'idCard', // 查询类型：idCard 或 phone\r\n        queryValue: '' // 查询值\r\n      },\r\n      queryRules: {\r\n        queryType: [\r\n          { required: true, message: '请选择查询方式', trigger: 'change' }\r\n        ],\r\n        queryValue: [\r\n          { required: true, message: '请输入查询信息', trigger: 'blur' },\r\n          {\r\n            validator: (rule, value, callback) => {\r\n              if (!value) {\r\n                callback(new Error('请输入查询信息'));\r\n                return;\r\n              }\r\n\r\n              if (this.queryForm.queryType === 'idCard') {\r\n                // 身份证号验证\r\n                const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n                if (!idPattern.test(value)) {\r\n                  callback(new Error('请输入正确的身份证号'));\r\n                  return;\r\n                }\r\n              } else if (this.queryForm.queryType === 'phone') {\r\n                // 手机号验证\r\n                const phonePattern = /^1[3-9]\\d{9}$/;\r\n                if (!phonePattern.test(value)) {\r\n                  callback(new Error('请输入正确的手机号'));\r\n                  return;\r\n                }\r\n              }\r\n\r\n              callback();\r\n            },\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      queryLoading: false,\r\n      queryExecuted: false, // 是否已执行过查询\r\n      queryResults: [], // 查询结果\r\n\r\n      // 表单数据 - 与微信端保持一致的结构\r\n      formData: {\r\n        // 访客列表（第一位为主联系人）\r\n        visitors: [\r\n          {\r\n            name: '',\r\n            phone: '',\r\n            idCard: '',\r\n            company: '',\r\n            isMainContact: true\r\n          }\r\n        ],\r\n        // 来访信息\r\n        visitInfo: {\r\n          reasonForVisit: '',\r\n          hostEmployeeName: '',\r\n          departmentVisited: '',\r\n          vehiclePlateNumber: '',\r\n          plannedEntryDatetime: '',\r\n          plannedExitDatetime: ''\r\n        }\r\n      },\r\n\r\n      // 表单验证规则 - 与微信端保持一致\r\n      visitRules: {\r\n        'visitInfo.reasonForVisit': [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.hostEmployeeName': [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.departmentVisited': [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.plannedEntryDatetime': [\r\n          { required: true, message: '请选择预计来访时间', trigger: 'change' }\r\n        ],\r\n        'visitInfo.plannedExitDatetime': [\r\n          { required: true, message: '请选择预计离开时间', trigger: 'change' }\r\n        ],\r\n        // 动态访客验证规则\r\n        'visitors.0.name': [\r\n          { required: true, message: '请输入主联系人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.phone': [\r\n          { required: true, message: '请输入主联系人手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.idCard': [\r\n          { required: true, message: '请输入主联系人身份证号', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n\r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '中午12点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(12, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 总访客人数\r\n    totalVisitors() {\r\n      return this.formData.visitors.length;\r\n    },\r\n\r\n    // 主联系人信息\r\n    mainContact() {\r\n      return this.formData.visitors[0] || {};\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 添加访客\r\n    addVisitor() {\r\n      if (this.formData.visitors.length >= 10) {\r\n        this.$message.warning('最多只能添加10名访客');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.push({\r\n        name: '',\r\n        phone: '',\r\n        idCard: '',\r\n        company: '',\r\n        isMainContact: false\r\n      });\r\n\r\n      this.$message.success('已添加访客');\r\n    },\r\n\r\n    // 移除访客\r\n    removeVisitor(index) {\r\n      if (index === 0) {\r\n        this.$message.warning('不能删除主联系人');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.splice(index, 1);\r\n      this.$message.success('已移除访客');\r\n    },\r\n\r\n    // 格式化日期为后端期望的格式\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n\r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.formData.visitInfo.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n\r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n\r\n        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.formData.visitInfo.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.formData.visitInfo.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 提交表单 */\r\n    async submitForm() {\r\n      try {\r\n        // 验证表单\r\n        await this.$refs.visitForm.validate();\r\n\r\n        // 验证访客信息\r\n        if (!this.validateVisitors()) {\r\n          return;\r\n        }\r\n\r\n        // 验证时间\r\n        if (!this.validateTimes()) {\r\n          return;\r\n        }\r\n\r\n        this.submitting = true;\r\n\r\n        // 获取主联系人信息\r\n        const mainContact = this.formData.visitors[0];\r\n\r\n        const submitData = {\r\n          // VisitRegistrations 对象\r\n          registration: {\r\n            primaryContactName: mainContact.name.trim(),\r\n            primaryContactPhone: mainContact.phone.trim(),\r\n            reasonForVisit: this.formData.visitInfo.reasonForVisit,\r\n            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,\r\n            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n            departmentVisited: this.formData.visitInfo.departmentVisited,\r\n            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),\r\n            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),\r\n            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',\r\n            totalCompanions: this.formData.visitors.length - 1\r\n          },\r\n          // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n          attendeesList: this.formData.visitors.map((visitor, index) => ({\r\n            visitorName: visitor.name.trim(),\r\n            visitorPhone: visitor.phone.trim(),\r\n            visitorIdCard: visitor.idCard.trim().toUpperCase(),\r\n            visitorCompany: (visitor.company || '').trim(),\r\n            isPrimary: index === 0 ? \"1\" : \"0\", // 第一个访客必须是主联系人\r\n            visitorAvatarPhoto: null // 暂时不支持头像上传\r\n          }))\r\n        };\r\n\r\n        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n\r\n        // 调用API提交数据\r\n        const response = await submitVisitorRegistration(submitData);\r\n\r\n        if (response.code === 200) {\r\n          this.registrationId = response.data || 'VR' + Date.now();\r\n          this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n          this.registrationCompleted = true;\r\n        } else {\r\n          this.$message.error(response.msg || '登记失败，请重试');\r\n        }\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error);\r\n        this.$message.error('登记失败，请检查网络连接');\r\n      } finally {\r\n        this.submitting = false;\r\n      }\r\n    },\r\n\r\n    // 验证访客信息\r\n    validateVisitors() {\r\n      for (let i = 0; i < this.formData.visitors.length; i++) {\r\n        const visitor = this.formData.visitors[i];\r\n        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n\r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号格式（与后端保持一致）\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idCard || visitor.idCard.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n\r\n        const idCard = visitor.idCard.trim().toUpperCase();\r\n\r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idCard = visitor.idCard.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 验证时间\r\n    validateTimes() {\r\n      if (!this.formData.visitInfo.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.formData.visitInfo.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 检查来访时间不能早于当前时间1小时前\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (entryTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能早于当前时间1小时前');\r\n        return false;\r\n      }\r\n\r\n      // 检查离开时间必须晚于来访时间\r\n      if (exitTime <= entryTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      // 检查访问时长不能超过24小时\r\n      const visitDuration = exitTime.getTime() - entryTime.getTime();\r\n      const maxDuration = 24 * 60 * 60 * 1000; // 24小时\r\n      if (visitDuration > maxDuration) {\r\n        this.$message.error('单次访问时长不能超过24小时');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 重置所有数据\r\n        this.registrationCompleted = false;\r\n\r\n        this.formData = {\r\n          visitors: [\r\n            {\r\n              name: '',\r\n              phone: '',\r\n              idCard: '',\r\n              company: '',\r\n              isMainContact: true\r\n            }\r\n          ],\r\n          visitInfo: {\r\n            reasonForVisit: '',\r\n            hostEmployeeName: '',\r\n            departmentVisited: '',\r\n            vehiclePlateNumber: '',\r\n            plannedEntryDatetime: '',\r\n            plannedExitDatetime: ''\r\n          }\r\n        };\r\n\r\n        this.registrationId = null;\r\n\r\n        this.$message.success('表单已重置');\r\n      });\r\n    },\r\n\r\n    // 打印凭证\r\n    printCredential() {\r\n      this.$message.info('打印功能开发中...');\r\n    },\r\n\r\n    // 格式化时间显示\r\n    parseTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    },\r\n\r\n    // 显示帮助信息\r\n    showHelp() {\r\n      this.$alert(`\r\n        <div style=\"text-align: left;\">\r\n          <h4>使用说明</h4>\r\n          <p><strong>1. 填写基本信息</strong></p>\r\n          <ul>\r\n            <li>请如实填写来访事由、被访人等信息</li>\r\n            <li>主联系人信息必须准确，用于后续联系</li>\r\n          </ul>\r\n          <p><strong>2. 添加同行人员</strong></p>\r\n          <ul>\r\n            <li>如有同行人员，请点击\"添加访客\"按钮</li>\r\n            <li>每位访客都需要填写完整身份信息</li>\r\n          </ul>\r\n          <p><strong>3. 选择访问时间</strong></p>\r\n          <ul>\r\n            <li>请合理安排访问时间，避免过长逗留</li>\r\n            <li>如需延长访问时间，请联系被访人</li>\r\n          </ul>\r\n          <p><strong>4. 审核与通行</strong></p>\r\n          <ul>\r\n            <li>提交后等待审核，通过后可凭二维码进入园区</li>\r\n            <li>请按照预约时间准时到访</li>\r\n          </ul>\r\n        </div>\r\n      `, '使用帮助', {\r\n        confirmButtonText: '知道了',\r\n        dangerouslyUseHTMLString: true\r\n      });\r\n    },\r\n\r\n    // 联系客服\r\n    contactService() {\r\n      this.$confirm('需要帮助吗？', '联系客服', {\r\n        confirmButtonText: '拨打电话',\r\n        cancelButtonText: '取消',\r\n        type: 'info'\r\n      }).then(() => {\r\n        // 这里可以实现拨打客服电话的功能\r\n        this.$message({\r\n          type: 'info',\r\n          message: '客服电话：400-xxx-xxxx'\r\n        });\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    },\r\n\r\n    // 显示查询对话框\r\n    showQueryDialog() {\r\n      this.queryDialog.visible = true;\r\n      this.resetQuery();\r\n    },\r\n\r\n    // 查询类型变更\r\n    handleQueryTypeChange() {\r\n      this.queryForm.queryValue = '';\r\n      this.queryResults = [];\r\n      this.queryExecuted = false;\r\n      this.$refs.queryForm.clearValidate('queryValue');\r\n    },\r\n\r\n    // 执行查询\r\n    async handleQuery() {\r\n      try {\r\n        await this.$refs.queryForm.validate();\r\n\r\n        this.queryLoading = true;\r\n        this.queryExecuted = false;\r\n\r\n        // 构建查询参数\r\n        const today = new Date().toISOString().split('T')[0];\r\n        const queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 50,\r\n          params: {\r\n            beginTime: today,\r\n            endTime: today\r\n          }\r\n        };\r\n\r\n        // 根据查询类型添加查询条件\r\n        if (this.queryForm.queryType === 'idCard') {\r\n          queryParams.idCardNumber = this.queryForm.queryValue.trim().toUpperCase();\r\n        } else if (this.queryForm.queryType === 'phone') {\r\n          queryParams.primaryContactPhone = this.queryForm.queryValue.trim();\r\n        }\r\n\r\n        console.log('查询参数:', queryParams);\r\n\r\n        // 调用查询接口\r\n        const response = await listRegistrations(queryParams);\r\n\r\n        if (response.code === 200) {\r\n          this.queryResults = response.rows || [];\r\n          this.queryExecuted = true;\r\n\r\n          if (this.queryResults.length > 0) {\r\n            this.$message.success(`找到 ${this.queryResults.length} 条登记记录`);\r\n          } else {\r\n            this.$message.info('未找到相关登记记录');\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg || '查询失败');\r\n          this.queryResults = [];\r\n          this.queryExecuted = true;\r\n        }\r\n      } catch (error) {\r\n        console.error('查询失败:', error);\r\n        this.$message.error('查询失败，请检查网络连接');\r\n        this.queryResults = [];\r\n        this.queryExecuted = true;\r\n      } finally {\r\n        this.queryLoading = false;\r\n      }\r\n    },\r\n\r\n    // 重置查询\r\n    resetQuery() {\r\n      this.queryForm = {\r\n        queryType: 'idCard',\r\n        queryValue: ''\r\n      };\r\n      this.queryResults = [];\r\n      this.queryExecuted = false;\r\n      this.$refs.queryForm && this.$refs.queryForm.resetFields();\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'pending': '待审核',\r\n        'approved': '已通过',\r\n        'rejected': '已拒绝',\r\n        'cancelled': '已取消',\r\n        'completed': '已完成'\r\n      };\r\n      return statusMap[status] || status;\r\n    },\r\n\r\n    // 获取状态样式类\r\n    getStatusClass(status) {\r\n      const classMap = {\r\n        'pending': 'status-pending',\r\n        'approved': 'status-approved',\r\n        'rejected': 'status-rejected',\r\n        'cancelled': 'status-cancelled',\r\n        'completed': 'status-completed'\r\n      };\r\n      return classMap[status] || 'status-default';\r\n    },\r\n\r\n    // 打印结果\r\n    printResults() {\r\n      this.$message.info('打印功能开发中...');\r\n      // 这里可以实现打印功能\r\n      // window.print();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.nav-actions .el-button {\r\n  border-radius: 20px;\r\n  padding: 8px 20px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 移动端布局优化 */\r\n.main-content.mobile-layout {\r\n  flex-direction: column;\r\n  padding: 20px;\r\n  gap: 20px;\r\n  max-width: 100%;\r\n}\r\n\r\n/* 移动端欢迎区域 */\r\n.mobile-welcome {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.welcome-header {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.welcome-header .welcome-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 15px;\r\n}\r\n\r\n.welcome-header .welcome-icon i {\r\n  font-size: 28px;\r\n  color: #fff;\r\n}\r\n\r\n.welcome-header h2 {\r\n  font-size: 24px;\r\n  color: #2c3e50;\r\n  margin: 0 0 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-header .welcome-desc {\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  margin-top: 20px;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #f0f0f0;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 移动端表单容器 */\r\n.mobile-form-container {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 移动端表单标题 */\r\n.mobile-form-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.mobile-form-header h3 {\r\n  margin: 0 0 8px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.mobile-form-header .form-subtitle {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 移动端表单内容 */\r\n.mobile-form-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 移动端表单卡片 */\r\n.mobile-form-card {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  overflow: hidden;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.mobile-form-card .card-header {\r\n  background: #fff;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.mobile-form-card .card-header i {\r\n  font-size: 16px;\r\n  color: #667eea;\r\n  margin-right: 8px;\r\n}\r\n\r\n.mobile-form-card .card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.mobile-form-card .add-visitor-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.mobile-form-card .card-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 移动端表单项 */\r\n.mobile-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.mobile-form-item .el-form-item__label {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  line-height: 1.4;\r\n  padding-bottom: 8px;\r\n}\r\n\r\n/* 移动端输入框 */\r\n.mobile-input .el-input__inner {\r\n  height: 44px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 0 15px;\r\n}\r\n\r\n.mobile-input .el-textarea__inner {\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 12px 15px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 移动端日期选择器 */\r\n.mobile-date-picker .el-input__inner {\r\n  height: 44px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 移动端访客项 */\r\n.mobile-visitor-item {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n  position: relative;\r\n}\r\n\r\n.mobile-visitor-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mobile-visitor-item .visitor-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.mobile-visitor-item .visitor-badge {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n  padding: 4px 12px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.mobile-visitor-item .visitor-badge.primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  border: none;\r\n}\r\n\r\n.mobile-visitor-item .remove-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n/* 移动端操作区域 */\r\n.mobile-actions {\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n.submit-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.success-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n/* 移动端按钮 */\r\n.mobile-submit-btn,\r\n.mobile-reset-btn,\r\n.mobile-action-btn {\r\n  width: 100%;\r\n  height: 48px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  border-radius: 8px;\r\n  border: none;\r\n}\r\n\r\n.mobile-submit-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n}\r\n\r\n.mobile-submit-btn:hover {\r\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\r\n}\r\n\r\n.mobile-reset-btn {\r\n  background: #fff;\r\n  color: #6c757d;\r\n  border: 1px solid #dcdfe6;\r\n}\r\n\r\n.mobile-reset-btn:hover {\r\n  background: #f8f9fa;\r\n  color: #495057;\r\n}\r\n\r\n.mobile-action-btn {\r\n  background: #fff;\r\n  color: #667eea;\r\n  border: 1px solid #667eea;\r\n}\r\n\r\n.mobile-action-btn:hover {\r\n  background: #667eea;\r\n  color: #fff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  /* 顶部导航移动端优化 */\r\n  .top-nav {\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .nav-content {\r\n    height: 60px;\r\n  }\r\n\r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-actions {\r\n    gap: 10px;\r\n  }\r\n\r\n  /* 主内容区域移动端优化 */\r\n  .main-content {\r\n    padding: 15px;\r\n    gap: 15px;\r\n  }\r\n\r\n  .main-content.mobile-layout {\r\n    padding: 10px;\r\n    gap: 15px;\r\n  }\r\n\r\n  /* 移动端欢迎区域优化 */\r\n  .mobile-welcome {\r\n    padding: 15px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .welcome-header .welcome-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .welcome-header .welcome-icon i {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .welcome-header h2 {\r\n    font-size: 20px;\r\n  }\r\n\r\n  /* 移动端表单优化 */\r\n  .mobile-form-container {\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .mobile-form-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-form-header h3 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .mobile-form-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-form-card {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .mobile-form-card .card-header {\r\n    padding: 12px 15px;\r\n  }\r\n\r\n  .mobile-form-card .card-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  /* 移动端访客项优化 */\r\n  .mobile-visitor-item {\r\n    padding: 12px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  /* 移动端操作区域优化 */\r\n  .mobile-actions {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-submit-btn,\r\n  .mobile-reset-btn,\r\n  .mobile-action-btn {\r\n    height: 44px;\r\n    font-size: 15px;\r\n  }\r\n}\r\n\r\n/* 超小屏幕优化 */\r\n@media (max-width: 480px) {\r\n  .top-nav {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .nav-content {\r\n    height: 55px;\r\n  }\r\n\r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .main-content.mobile-layout {\r\n    padding: 8px;\r\n  }\r\n\r\n  .mobile-welcome {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-form-header {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-form-content {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-actions {\r\n    padding: 12px;\r\n  }\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 查询对话框样式 */\r\n.query-dialog .el-dialog {\r\n  border-radius: 12px;\r\n}\r\n\r\n.query-dialog .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  padding: 20px 24px;\r\n  border-radius: 12px 12px 0 0;\r\n}\r\n\r\n.query-dialog .el-dialog__title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.query-dialog .el-dialog__headerbtn .el-dialog__close {\r\n  color: #fff;\r\n  font-size: 20px;\r\n}\r\n\r\n.query-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.query-container {\r\n  padding: 24px;\r\n}\r\n\r\n.query-form-section {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.query-form-section h4 {\r\n  margin: 0 0 20px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.query-form-section h4 i {\r\n  color: #667eea;\r\n}\r\n\r\n.query-results-section {\r\n  margin-top: 24px;\r\n}\r\n\r\n.query-results-section h4 {\r\n  margin: 0 0 16px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.query-results-section h4 i {\r\n  color: #667eea;\r\n}\r\n\r\n.results-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.result-item {\r\n  background: #fff;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.result-item:hover {\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.visitor-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.visitor-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.visitor-phone {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n}\r\n\r\n.status-badge {\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  min-width: 60px;\r\n}\r\n\r\n.status-pending {\r\n  background: #fff3cd;\r\n  color: #856404;\r\n  border: 1px solid #ffeaa7;\r\n}\r\n\r\n.status-approved {\r\n  background: #d4edda;\r\n  color: #155724;\r\n  border: 1px solid #c3e6cb;\r\n}\r\n\r\n.status-rejected {\r\n  background: #f8d7da;\r\n  color: #721c24;\r\n  border: 1px solid #f5c6cb;\r\n}\r\n\r\n.status-cancelled {\r\n  background: #e2e3e5;\r\n  color: #383d41;\r\n  border: 1px solid #d6d8db;\r\n}\r\n\r\n.status-completed {\r\n  background: #d1ecf1;\r\n  color: #0c5460;\r\n  border: 1px solid #bee5eb;\r\n}\r\n\r\n.status-default {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n  border: 1px solid #dee2e6;\r\n}\r\n\r\n.result-details {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.detail-row {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.detail-row .label {\r\n  font-weight: 600;\r\n  color: #495057;\r\n  min-width: 80px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.detail-row .value {\r\n  color: #6c757d;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n\r\n.no-results {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.no-results i {\r\n  font-size: 64px;\r\n  color: #dee2e6;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.no-results h4 {\r\n  margin: 0 0 8px 0;\r\n  color: #495057;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.no-results p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.dialog-footer {\r\n  padding: 16px 24px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .query-dialog {\r\n    width: 95% !important;\r\n  }\r\n\r\n  .query-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .query-form-section {\r\n    padding: 16px;\r\n  }\r\n\r\n  .result-details {\r\n    grid-template-columns: 1fr;\r\n    gap: 8px;\r\n  }\r\n\r\n  .result-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n\r\n  .visitor-info {\r\n    width: 100%;\r\n  }\r\n\r\n  .status-badge {\r\n    align-self: flex-start;\r\n  }\r\n\r\n  .dialog-footer {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .nav-actions {\r\n    gap: 10px;\r\n  }\r\n\r\n  .nav-actions .el-button {\r\n    padding: 6px 16px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .nav-actions .el-button {\r\n    padding: 4px 12px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .visitor-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .detail-row {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .detail-row .label {\r\n    min-width: auto;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAwXA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,UAAA;MACAC,qBAAA;MAEA;MACAC,WAAA;QACAC,OAAA;MACA;MACAC,SAAA;QACAC,SAAA;QAAA;QACAC,UAAA;MACA;MACAC,UAAA;QACAF,SAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,UAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAC,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,KAAAD,KAAA;cACAC,QAAA,KAAAC,KAAA;cACA;YACA;YAEA,IAAAhB,KAAA,CAAAK,SAAA,CAAAC,SAAA;cACA;cACA,IAAAW,SAAA;cACA,KAAAA,SAAA,CAAAC,IAAA,CAAAJ,KAAA;gBACAC,QAAA,KAAAC,KAAA;gBACA;cACA;YACA,WAAAhB,KAAA,CAAAK,SAAA,CAAAC,SAAA;cACA;cACA,IAAAa,YAAA;cACA,KAAAA,YAAA,CAAAD,IAAA,CAAAJ,KAAA;gBACAC,QAAA,KAAAC,KAAA;gBACA;cACA;YACA;YAEAD,QAAA;UACA;UACAJ,OAAA;QACA;MAEA;MACAS,YAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;;MAEA;MACAC,QAAA;QACA;QACAC,QAAA,GACA;UACA1B,IAAA;UACA2B,KAAA;UACAC,MAAA;UACAC,OAAA;UACAC,aAAA;QACA,EACA;QACA;QACAC,SAAA;UACAC,cAAA;UACAC,gBAAA;UACAC,iBAAA;UACAC,kBAAA;UACAC,oBAAA;UACAC,mBAAA;QACA;MACA;MAEA;MACAC,UAAA;QACA,6BACA;UAAA3B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA0B,GAAA;UAAAC,GAAA;UAAA5B,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,+BACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA0B,GAAA;UAAAC,GAAA;UAAA5B,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,gCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA0B,GAAA;UAAAC,GAAA;UAAA5B,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,mCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,kCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA;QACA,oBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA0B,GAAA;UAAAC,GAAA;UAAA5B,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,qBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAA4B,OAAA;UAAA7B,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,sBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACA6B,cAAA;MAEA;MACAC,kBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACAA,MAAA,CAAAC,KAAA,aAAAC,IAAA;UACA;QACA;UACAJ,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACAJ,IAAA,CAAAK,QAAA;YACAR,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;QACAM,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAC,aAAA,GAAAT,IAAA,CAAAU,GAAA;UACA,IAAAC,eAAA,GAAAX,IAAA,CAAAU,GAAA;UACA,OAAAF,IAAA,CAAAL,OAAA,KAAAM,aAAA,IAAAD,IAAA,CAAAL,OAAA,KAAAQ,eAAA;QACA;MACA;MAEAC,iBAAA;QACAjB,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAK,QAAA;YACA,IAAAL,IAAA,CAAAE,OAAA,KAAAH,IAAA,CAAAU,GAAA;cACAT,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACA;YACAP,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAK,QAAA;YACA,IAAAL,IAAA,CAAAE,OAAA,KAAAH,IAAA,CAAAU,GAAA;cACAT,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACA;YACAP,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;QACAM,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAG,eAAA,GAAAX,IAAA,CAAAU,GAAA;UACA,OAAAF,IAAA,CAAAL,OAAA,KAAAH,IAAA,CAAAU,GAAA,eAAAF,IAAA,CAAAL,OAAA,KAAAQ,eAAA;QACA;MACA;IACA;EACA;EAEAE,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAtC,QAAA,CAAAC,QAAA,CAAAsC,MAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAxC,QAAA,CAAAC,QAAA;IACA;EACA;EAEAwC,OAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAA1C,QAAA,CAAAC,QAAA,CAAAsC,MAAA;QACA,KAAAI,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA5C,QAAA,CAAAC,QAAA,CAAA4C,IAAA;QACAtE,IAAA;QACA2B,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,aAAA;MACA;MAEA,KAAAsC,QAAA,CAAAG,OAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MACA,IAAAA,KAAA;QACA,KAAAL,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA5C,QAAA,CAAAC,QAAA,CAAAgD,MAAA,CAAAD,KAAA;MACA,KAAAL,QAAA,CAAAG,OAAA;IACA;IAEA;IACAI,oBAAA,WAAAA,qBAAAC,OAAA;MACA,KAAAA,OAAA;MAEA,IAAA1B,IAAA,OAAAD,IAAA,CAAA2B,OAAA;MACA,IAAAC,KAAA,CAAA3B,IAAA,CAAAE,OAAA;MAEA,IAAA0B,IAAA,GAAA5B,IAAA,CAAA6B,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAA/B,IAAA,CAAAgC,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAA/B,IAAA,CAAAI,OAAA,IAAA6B,QAAA;MACA,IAAAE,KAAA,GAAAJ,MAAA,CAAA/B,IAAA,CAAAoC,QAAA,IAAAH,QAAA;MACA,IAAAI,OAAA,GAAAN,MAAA,CAAA/B,IAAA,CAAAsC,UAAA,IAAAL,QAAA;MACA,IAAAM,OAAA,GAAAR,MAAA,CAAA/B,IAAA,CAAAwC,UAAA,IAAAP,QAAA;MAEA,UAAAQ,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IAEA,eACAG,mBAAA,WAAAA,oBAAA5E,KAAA;MACA6E,OAAA,CAAAC,GAAA,cAAA9E,KAAA;MACA;MACA,IAAAA,KAAA,UAAAS,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA,IAAA0D,WAAA,OAAA9C,IAAA,CAAAjC,KAAA;QACA,IAAAgF,aAAA,OAAA/C,IAAA,CAAA8C,WAAA,CAAA3C,OAAA;QAEA,IAAA0B,IAAA,GAAAkB,aAAA,CAAAjB,WAAA;QACA,IAAAC,KAAA,GAAAC,MAAA,CAAAe,aAAA,CAAAd,QAAA,QAAAC,QAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAAe,aAAA,CAAA1C,OAAA,IAAA6B,QAAA;QACA,IAAAE,KAAA,GAAAJ,MAAA,CAAAe,aAAA,CAAAV,QAAA,IAAAH,QAAA;QACA,IAAAI,OAAA,GAAAN,MAAA,CAAAe,aAAA,CAAAR,UAAA,IAAAL,QAAA;QACA,IAAAM,OAAA,GAAAR,MAAA,CAAAe,aAAA,CAAAN,UAAA,IAAAP,QAAA;QAEA,KAAA1D,QAAA,CAAAM,SAAA,CAAAM,mBAAA,MAAAsD,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;MACA;IACA;IAEA,eACAQ,qBAAA,WAAAA,sBAAAjF,KAAA;MACA6E,OAAA,CAAAC,GAAA,cAAA9E,KAAA;MACA;MACA,IAAAA,KAAA,SAAAS,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,IAAA2D,WAAA,OAAA9C,IAAA,MAAAxB,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,IAAA4D,aAAA,OAAA/C,IAAA,CAAAjC,KAAA;QAEA,IAAAgF,aAAA,IAAAD,WAAA;UACA,KAAA3B,QAAA,CAAAC,OAAA;UACA,KAAA5C,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA;MACA;IACA;IAEA,WACA6D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAvC,WAAA,EAAAwC,UAAA,EAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGAX,MAAA,CAAAa,KAAA,CAAAC,SAAA,CAAAC,QAAA;YAAA;cAAA,IAGAf,MAAA,CAAAgB,gBAAA;gBAAAN,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAO,CAAA;YAAA;cAAA,IAKAjB,MAAA,CAAAkB,aAAA;gBAAAR,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAO,CAAA;YAAA;cAIAjB,MAAA,CAAAhG,UAAA;;cAEA;cACA8D,WAAA,GAAAkC,MAAA,CAAA1E,QAAA,CAAAC,QAAA;cAEA+E,UAAA;gBACA;gBACAa,YAAA;kBACAC,kBAAA,EAAAtD,WAAA,CAAAjE,IAAA,CAAAwH,IAAA;kBACAC,mBAAA,EAAAxD,WAAA,CAAAtC,KAAA,CAAA6F,IAAA;kBACAxF,cAAA,EAAAmE,MAAA,CAAA1E,QAAA,CAAAM,SAAA,CAAAC,cAAA;kBACAC,gBAAA,EAAAkE,MAAA,CAAA1E,QAAA,CAAAM,SAAA,CAAAE,gBAAA;kBACAyF,cAAA;kBAAA;kBACAxF,iBAAA,EAAAiE,MAAA,CAAA1E,QAAA,CAAAM,SAAA,CAAAG,iBAAA;kBACAE,oBAAA,EAAA+D,MAAA,CAAAxB,oBAAA,CAAAwB,MAAA,CAAA1E,QAAA,CAAAM,SAAA,CAAAK,oBAAA;kBACAC,mBAAA,EAAA8D,MAAA,CAAAxB,oBAAA,CAAAwB,MAAA,CAAA1E,QAAA,CAAAM,SAAA,CAAAM,mBAAA;kBACAF,kBAAA,EAAAgE,MAAA,CAAA1E,QAAA,CAAAM,SAAA,CAAAI,kBAAA;kBACAwF,eAAA,EAAAxB,MAAA,CAAA1E,QAAA,CAAAC,QAAA,CAAAsC,MAAA;gBACA;gBACA;gBACA4D,aAAA,EAAAzB,MAAA,CAAA1E,QAAA,CAAAC,QAAA,CAAAmG,GAAA,WAAAC,OAAA,EAAArD,KAAA;kBAAA;oBACAsD,WAAA,EAAAD,OAAA,CAAA9H,IAAA,CAAAwH,IAAA;oBACAQ,YAAA,EAAAF,OAAA,CAAAnG,KAAA,CAAA6F,IAAA;oBACAS,aAAA,EAAAH,OAAA,CAAAlG,MAAA,CAAA4F,IAAA,GAAAU,WAAA;oBACAC,cAAA,GAAAL,OAAA,CAAAjG,OAAA,QAAA2F,IAAA;oBACAY,SAAA,EAAA3D,KAAA;oBAAA;oBACA4D,kBAAA;kBACA;gBAAA;cACA;cAEAxC,OAAA,CAAAC,GAAA,YAAAwC,IAAA,CAAAC,SAAA,CAAA9B,UAAA;;cAEA;cAAAI,QAAA,CAAAC,CAAA;cAAA,OACA,IAAA0B,kCAAA,EAAA/B,UAAA;YAAA;cAAAC,QAAA,GAAAG,QAAA,CAAA4B,CAAA;cAEA,IAAA/B,QAAA,CAAAgC,IAAA;gBACAvC,MAAA,CAAAzD,cAAA,GAAAgE,QAAA,CAAAzG,IAAA,WAAAgD,IAAA,CAAAU,GAAA;gBACAwC,MAAA,CAAA/B,QAAA,CAAAG,OAAA,IAAAoB,MAAA,CAAAQ,MAAA,CAAApC,aAAA;gBACAoC,MAAA,CAAA/F,qBAAA;cACA;gBACA+F,MAAA,CAAA/B,QAAA,CAAAuE,KAAA,CAAAjC,QAAA,CAAAkC,GAAA;cACA;cAAA/B,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAA4B,CAAA;cAEA5C,OAAA,CAAA8C,KAAA,YAAAhC,EAAA;cACAR,MAAA,CAAA/B,QAAA,CAAAuE,KAAA;YAAA;cAAA9B,QAAA,CAAAE,CAAA;cAEAZ,MAAA,CAAAhG,UAAA;cAAA,OAAA0G,QAAA,CAAAgC,CAAA;YAAA;cAAA,OAAAhC,QAAA,CAAAO,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAW,gBAAA,WAAAA,iBAAA;MACA,SAAA2B,CAAA,MAAAA,CAAA,QAAArH,QAAA,CAAAC,QAAA,CAAAsC,MAAA,EAAA8E,CAAA;QACA,IAAAhB,OAAA,QAAArG,QAAA,CAAAC,QAAA,CAAAoH,CAAA;QACA,IAAAC,YAAA,GAAAD,CAAA,iCAAAnD,MAAA,CAAAmD,CAAA;;QAEA;QACA,KAAAhB,OAAA,CAAA9H,IAAA,IAAA8H,OAAA,CAAA9H,IAAA,CAAAwH,IAAA;UACA,KAAApD,QAAA,CAAAuE,KAAA,sBAAAhD,MAAA,CAAAoD,YAAA;UACA;QACA;QAEA,IAAAjB,OAAA,CAAA9H,IAAA,CAAAwH,IAAA,GAAAxD,MAAA,QAAA8D,OAAA,CAAA9H,IAAA,CAAAwH,IAAA,GAAAxD,MAAA;UACA,KAAAI,QAAA,CAAAuE,KAAA,IAAAhD,MAAA,CAAAoD,YAAA;UACA;QACA;;QAEA;QACA,KAAAjB,OAAA,CAAAnG,KAAA,IAAAmG,OAAA,CAAAnG,KAAA,CAAA6F,IAAA;UACA,KAAApD,QAAA,CAAAuE,KAAA,sBAAAhD,MAAA,CAAAoD,YAAA;UACA;QACA;;QAEA;QACA,IAAA1H,YAAA;QACA,KAAAA,YAAA,CAAAD,IAAA,CAAA0G,OAAA,CAAAnG,KAAA,CAAA6F,IAAA;UACA,KAAApD,QAAA,CAAAuE,KAAA,IAAAhD,MAAA,CAAAoD,YAAA;UACA;QACA;;QAEA;QACA,KAAAjB,OAAA,CAAAlG,MAAA,IAAAkG,OAAA,CAAAlG,MAAA,CAAA4F,IAAA;UACA,KAAApD,QAAA,CAAAuE,KAAA,sBAAAhD,MAAA,CAAAoD,YAAA;UACA;QACA;QAEA,IAAAnH,MAAA,GAAAkG,OAAA,CAAAlG,MAAA,CAAA4F,IAAA,GAAAU,WAAA;;QAEA;QACA,IAAAtG,MAAA,CAAAoC,MAAA;UACA,IAAA7C,SAAA;UACA,KAAAA,SAAA,CAAAC,IAAA,CAAAQ,MAAA;YACA,KAAAwC,QAAA,CAAAuE,KAAA,IAAAhD,MAAA,CAAAoD,YAAA;YACA;UACA;QACA;;QAEA;QACA,SAAAC,CAAA,MAAAA,CAAA,QAAAvH,QAAA,CAAAC,QAAA,CAAAsC,MAAA,EAAAgF,CAAA;UACA,IAAAF,CAAA,KAAAE,CAAA,IAAAlB,OAAA,CAAAlG,MAAA,CAAA4F,IAAA,GAAAU,WAAA,YAAAzG,QAAA,CAAAC,QAAA,CAAAsH,CAAA,EAAApH,MAAA,CAAA4F,IAAA,GAAAU,WAAA;YACA,KAAA9D,QAAA,CAAAuE,KAAA,IAAAhD,MAAA,CAAAoD,YAAA,wBAAApD,MAAA,CAAAqD,CAAA,kBAAAA,CAAA;YACA;UACA;QACA;;QAEA;QACA,SAAAA,EAAA,MAAAA,EAAA,QAAAvH,QAAA,CAAAC,QAAA,CAAAsC,MAAA,EAAAgF,EAAA;UACA,IAAAF,CAAA,KAAAE,EAAA,IAAAlB,OAAA,CAAAnG,KAAA,CAAA6F,IAAA,YAAA/F,QAAA,CAAAC,QAAA,CAAAsH,EAAA,EAAArH,KAAA,CAAA6F,IAAA;YACA,KAAApD,QAAA,CAAAuE,KAAA,IAAAhD,MAAA,CAAAoD,YAAA,wBAAApD,MAAA,CAAAqD,EAAA,kBAAAA,EAAA;YACA;UACA;QACA;;QAEA;QACAlB,OAAA,CAAA9H,IAAA,GAAA8H,OAAA,CAAA9H,IAAA,CAAAwH,IAAA;QACAM,OAAA,CAAAnG,KAAA,GAAAmG,OAAA,CAAAnG,KAAA,CAAA6F,IAAA;QACAM,OAAA,CAAAlG,MAAA,GAAAkG,OAAA,CAAAlG,MAAA,CAAA4F,IAAA,GAAAU,WAAA;QACAJ,OAAA,CAAAjG,OAAA,IAAAiG,OAAA,CAAAjG,OAAA,QAAA2F,IAAA;MACA;MAEA;IACA;IAEA;IACAH,aAAA,WAAAA,cAAA;MACA,UAAA5F,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,KAAAgC,QAAA,CAAAuE,KAAA;QACA;MACA;MAEA,UAAAlH,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA,KAAA+B,QAAA,CAAAuE,KAAA;QACA;MACA;MAEA,IAAAM,SAAA,OAAAhG,IAAA,MAAAxB,QAAA,CAAAM,SAAA,CAAAK,oBAAA;MACA,IAAA8G,QAAA,OAAAjG,IAAA,MAAAxB,QAAA,CAAAM,SAAA,CAAAM,mBAAA;MACA,IAAAsB,GAAA,OAAAV,IAAA;;MAEA;MACA,IAAAS,aAAA,OAAAT,IAAA,CAAAU,GAAA,CAAAP,OAAA;MACA,IAAA6F,SAAA,GAAAvF,aAAA;QACA,KAAAU,QAAA,CAAAuE,KAAA;QACA;MACA;;MAEA;MACA,IAAAO,QAAA,IAAAD,SAAA;QACA,KAAA7E,QAAA,CAAAuE,KAAA;QACA;MACA;;MAEA;MACA,IAAAQ,aAAA,GAAAD,QAAA,CAAA9F,OAAA,KAAA6F,SAAA,CAAA7F,OAAA;MACA,IAAAgG,WAAA;MACA,IAAAD,aAAA,GAAAC,WAAA;QACA,KAAAhF,QAAA,CAAAuE,KAAA;QACA;MACA;MAEA;IACA;IAEA;IACAU,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;QACAL,MAAA,CAAAlJ,qBAAA;QAEAkJ,MAAA,CAAA7H,QAAA;UACAC,QAAA,GACA;YACA1B,IAAA;YACA2B,KAAA;YACAC,MAAA;YACAC,OAAA;YACAC,aAAA;UACA,EACA;UACAC,SAAA;YACAC,cAAA;YACAC,gBAAA;YACAC,iBAAA;YACAC,kBAAA;YACAC,oBAAA;YACAC,mBAAA;UACA;QACA;QAEAiH,MAAA,CAAA5G,cAAA;QAEA4G,MAAA,CAAAlF,QAAA,CAAAG,OAAA;MACA;IACA;IAEA;IACAqF,eAAA,WAAAA,gBAAA;MACA,KAAAxF,QAAA,CAAAyF,IAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAArG,IAAA;MACA,KAAAA,IAAA;MACA,IAAAP,IAAA,OAAAD,IAAA,CAAAQ,IAAA;MACA,IAAAoB,KAAA,CAAA3B,IAAA,CAAAE,OAAA;MAEA,IAAA0B,IAAA,GAAA5B,IAAA,CAAA6B,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAA/B,IAAA,CAAAgC,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAA/B,IAAA,CAAAI,OAAA,IAAA6B,QAAA;MACA,IAAAE,KAAA,GAAAJ,MAAA,CAAA/B,IAAA,CAAAoC,QAAA,IAAAH,QAAA;MACA,IAAAI,OAAA,GAAAN,MAAA,CAAA/B,IAAA,CAAAsC,UAAA,IAAAL,QAAA;MAEA,UAAAQ,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA;IACA;IAEA;IACAwE,QAAA,WAAAA,SAAA;MACA,KAAAC,MAAA,+8CAwBA;QACAR,iBAAA;QACAS,wBAAA;MACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAZ,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;QACAQ,MAAA,CAAA/F,QAAA;UACAsF,IAAA;UACA9I,OAAA;QACA;MACA,GAAAwJ,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAhK,WAAA,CAAAC,OAAA;MACA,KAAAgK,UAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAhK,SAAA,CAAAE,UAAA;MACA,KAAAe,YAAA;MACA,KAAAD,aAAA;MACA,KAAAyF,KAAA,CAAAzG,SAAA,CAAAiK,aAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtE,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAoE,SAAA;QAAA,IAAAC,KAAA,EAAAC,WAAA,EAAAnE,QAAA,EAAAoE,GAAA;QAAA,WAAAxE,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAmE,SAAA;UAAA,kBAAAA,SAAA,CAAAjE,CAAA;YAAA;cAAAiE,SAAA,CAAAhE,CAAA;cAAAgE,SAAA,CAAAjE,CAAA;cAAA,OAEA4D,MAAA,CAAA1D,KAAA,CAAAzG,SAAA,CAAA2G,QAAA;YAAA;cAEAwD,MAAA,CAAApJ,YAAA;cACAoJ,MAAA,CAAAnJ,aAAA;;cAEA;cACAqJ,KAAA,OAAA3H,IAAA,GAAA+H,WAAA,GAAAC,KAAA;cACAJ,WAAA;gBACAK,OAAA;gBACAC,QAAA;gBACAC,MAAA;kBACAC,SAAA,EAAAT,KAAA;kBACAU,OAAA,EAAAV;gBACA;cACA,GAEA;cACA,IAAAF,MAAA,CAAAnK,SAAA,CAAAC,SAAA;gBACAqK,WAAA,CAAAU,YAAA,GAAAb,MAAA,CAAAnK,SAAA,CAAAE,UAAA,CAAA+G,IAAA,GAAAU,WAAA;cACA,WAAAwC,MAAA,CAAAnK,SAAA,CAAAC,SAAA;gBACAqK,WAAA,CAAApD,mBAAA,GAAAiD,MAAA,CAAAnK,SAAA,CAAAE,UAAA,CAAA+G,IAAA;cACA;cAEA3B,OAAA,CAAAC,GAAA,UAAA+E,WAAA;;cAEA;cAAAE,SAAA,CAAAjE,CAAA;cAAA,OACA,IAAA0E,0BAAA,EAAAX,WAAA;YAAA;cAAAnE,QAAA,GAAAqE,SAAA,CAAAtC,CAAA;cAEA,IAAA/B,QAAA,CAAAgC,IAAA;gBACAgC,MAAA,CAAAlJ,YAAA,GAAAkF,QAAA,CAAA+E,IAAA;gBACAf,MAAA,CAAAnJ,aAAA;gBAEA,IAAAmJ,MAAA,CAAAlJ,YAAA,CAAAwC,MAAA;kBACA0G,MAAA,CAAAtG,QAAA,CAAAG,OAAA,iBAAAoB,MAAA,CAAA+E,MAAA,CAAAlJ,YAAA,CAAAwC,MAAA;gBACA;kBACA0G,MAAA,CAAAtG,QAAA,CAAAyF,IAAA;gBACA;cACA;gBACAa,MAAA,CAAAtG,QAAA,CAAAuE,KAAA,CAAAjC,QAAA,CAAAkC,GAAA;gBACA8B,MAAA,CAAAlJ,YAAA;gBACAkJ,MAAA,CAAAnJ,aAAA;cACA;cAAAwJ,SAAA,CAAAjE,CAAA;cAAA;YAAA;cAAAiE,SAAA,CAAAhE,CAAA;cAAA+D,GAAA,GAAAC,SAAA,CAAAtC,CAAA;cAEA5C,OAAA,CAAA8C,KAAA,UAAAmC,GAAA;cACAJ,MAAA,CAAAtG,QAAA,CAAAuE,KAAA;cACA+B,MAAA,CAAAlJ,YAAA;cACAkJ,MAAA,CAAAnJ,aAAA;YAAA;cAAAwJ,SAAA,CAAAhE,CAAA;cAEA2D,MAAA,CAAApJ,YAAA;cAAA,OAAAyJ,SAAA,CAAAlC,CAAA;YAAA;cAAA,OAAAkC,SAAA,CAAA3D,CAAA;UAAA;QAAA,GAAAuD,QAAA;MAAA;IAEA;IAEA;IACAL,UAAA,WAAAA,WAAA;MACA,KAAA/J,SAAA;QACAC,SAAA;QACAC,UAAA;MACA;MACA,KAAAe,YAAA;MACA,KAAAD,aAAA;MACA,KAAAyF,KAAA,CAAAzG,SAAA,SAAAyG,KAAA,CAAAzG,SAAA,CAAAmL,WAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA,KAAAA,MAAA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAAF,MAAA;MACA,IAAAG,QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAH,MAAA;IACA;IAEA;IACAI,YAAA,WAAAA,aAAA;MACA,KAAA5H,QAAA,CAAAyF,IAAA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}