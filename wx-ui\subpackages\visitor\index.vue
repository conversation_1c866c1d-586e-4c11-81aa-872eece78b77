<template>
  <view class="visitor-container">
    <!-- 欢迎横幅 -->
    <view class="welcome-banner">
      <view class="banner-content">
        <view class="banner-title">访客管理</view>
        <view class="banner-subtitle">访客登记与管理系统</view>
      </view>
    </view>

    <!-- 功能选择 -->
    <view class="modules-container">
      <view class="modules-grid">
        <!-- 访客登记 -->
        <view class="module-item" @click="goToRegistration">
          <view class="module-icon">
            <uni-icons type="personadd" size="32" color="#67C23A"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">访客登记</text>
            <text class="module-desc">新访客信息登记</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 访客列表 -->
        <view class="module-item" @click="goToList">
          <view class="module-icon">
            <uni-icons type="list" size="32" color="#409EFF"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">登记列表</text>
            <text class="module-desc">查看和管理访客记录</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 今日访客 -->
        <view class="module-item" @click="goToTodayList">
          <view class="module-icon">
            <uni-icons type="calendar" size="32" color="#E6A23C"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">今日访客</text>
            <text class="module-desc">今日访客统计</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 访客统计 -->
        <view class="module-item" @click="goToStatistics">
          <view class="module-icon">
            <uni-icons type="bars" size="32" color="#F56C6C"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">访客统计</text>
            <text class="module-desc">访客数据统计分析</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 审核管理 -->
        <view class="module-item" @click="goToApproval" v-if="hasApprovalPermission">
          <view class="module-icon">
            <uni-icons type="checkmarkempty" size="32" color="#909399"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">审核管理</text>
            <text class="module-desc">访客登记审核</text>
            <text class="pending-count" v-if="pendingCount > 0">{{ pendingCount }}待审核</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速统计 -->
    <view class="stats-container">
      <uni-card title="今日统计" :is-shadow="false">
        <view class="stats-grid">
          <view class="stat-item">
            <view class="stat-number">{{ todayStats.total }}</view>
            <view class="stat-label">今日访客</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ todayStats.checkedIn }}</view>
            <view class="stat-label">已签入</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ todayStats.checkedOut }}</view>
            <view class="stat-label">已签出</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ todayStats.inBuilding }}</view>
            <view class="stat-label">在厂内</view>
          </view>
        </view>
      </uni-card>
    </view>
  </view>
</template>

<script>
import { getTodayRegistrations, getCurrentVisitors, getDailyStatistics, getPendingRegistrations } from '@/api/system/visitor'

export default {
  data() {
    return {
      todayStats: {
        total: 0,
        checkedIn: 0,
        checkedOut: 0,
        inBuilding: 0
      },
      hasApprovalPermission: false, // 是否有审核管理权限
      pendingCount: 0, // 待审核访客数量
      lastRefreshTime: null, // 最后刷新时间
      isLoading: false, // 防止重复加载统计数据
      isPendingLoading: false // 防止重复加载待审核数据
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '访客管理'
    });
    this.lastRefreshTime = Date.now(); // 设置初始加载时间
    this.loadTodayStats();
    this.checkApprovalPermission();
  },
  onShow() {
    // 页面显示时只刷新统计数据，不重复加载权限
    // 避免重复请求，只在从其他页面返回时刷新
    this.refreshData();
  },
  methods: {
    // 跳转到访客登记页面
    goToRegistration() {
      uni.navigateTo({
        url: '/subpackages/visitor/registration',
        fail: (err) => {
          console.error('跳转到访客登记页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 跳转到访客列表页面
    goToList() {
      uni.navigateTo({
        url: '/subpackages/visitor/list',
        fail: (err) => {
          console.error('跳转到访客列表页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 跳转到今日访客页面
    goToTodayList() {
      uni.navigateTo({
        url: `/subpackages/visitor/today`,
        fail: (err) => {
          console.error('跳转到今日访客页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 跳转到访客统计页面
    goToStatistics() {
      uni.navigateTo({
        url: '/subpackages/visitor/statistics',
        fail: (err) => {
          console.error('跳转到访客统计页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 跳转到审核管理页面
    goToApproval() {
      uni.navigateTo({
        url: '/subpackages/visitor/approval',
        fail: (err) => {
          console.error('跳转到审核管理页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 加载今日统计数据
    async loadTodayStats() {
      // 防止重复加载
      if (this.isLoading) {
        console.log('数据正在加载中，跳过重复请求');
        return;
      }
      
      this.isLoading = true;
      try {
        const today = new Date().toISOString().split('T')[0];

        // 获取今日统计数据
        try {
          const statsResponse = await getDailyStatistics(today);
          if (statsResponse.code === 200 && statsResponse.data) {
            this.todayStats = {
              total: statsResponse.data.totalRegistrations || 0,
              checkedIn: statsResponse.data.checkedInCount || 0,
              checkedOut: statsResponse.data.checkedOutCount || 0,
              inBuilding: statsResponse.data.currentInBuilding || 0
            };
            return; // 统计API成功，直接返回
          }
        } catch (statsError) {
          console.log('统计API调用失败，尝试其他方式获取数据:', statsError);
        }
        
        // 如果统计接口失败，尝试获取今日登记数据
        try {
          const todayResponse = await getTodayRegistrations();
          if (todayResponse.code === 200 && todayResponse.data) {
            const registrations = todayResponse.data;
            this.todayStats.total = registrations.length;

            // 获取当前在楼访客
            try {
              const currentResponse = await getCurrentVisitors();
              if (currentResponse.code === 200 && currentResponse.data) {
                this.todayStats.inBuilding = currentResponse.data.length;
              }
            } catch (currentError) {
              console.log('获取当前在楼访客失败:', currentError);
            }
          } else {
            // 今日登记接口也失败，设置默认值
            console.log('今日登记接口返回数据异常:', todayResponse);
          }
        } catch (todayError) {
          console.log('今日登记API调用失败:', todayError);
          // 所有接口都失败，保持默认的0值
        }
      } catch (error) {
        console.error('加载今日统计失败:', error);
        // 静默处理错误，不影响页面显示
      } finally {
        this.isLoading = false;
      }
    },

    // 检查用户权限，设置是否显示审核管理模块
    async checkApprovalPermission() {
      try {
        // 这里应该根据实际的权限系统来判断
        // 临时设置为true，实际应该调用权限验证接口
        this.hasApprovalPermission = true;
        
        if (this.hasApprovalPermission) {
          // 不阻塞主流程，异步加载待审核数量
          this.loadPendingCount().catch(err => {
            console.error('加载待审核数量失败:', err);
          });
        }
      } catch (error) {
        console.error('检查审核权限失败:', error);
        // 权限检查失败时，默认不显示审核功能
        this.hasApprovalPermission = false;
      }
    },

    // 加载待审核数量
    async loadPendingCount() {
      // 防止重复加载
      if (this.isPendingLoading) {
        console.log('待审核数据正在加载中，跳过重复请求');
        return;
      }
      
      this.isPendingLoading = true;
      try {
        // 设置超时时间为5秒
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 5000);
        });
        
        const response = await Promise.race([
          getPendingRegistrations(),
          timeoutPromise
        ]);
        
        if (response && response.code === 200) {
          this.pendingCount = response.data ? response.data.length : 0;
        } else {
          this.pendingCount = 0;
        }
      } catch (error) {
        console.error('加载待审核数量失败:', error);
        this.pendingCount = 0;
      } finally {
        this.isPendingLoading = false;
      }
    },

    // 智能刷新数据 - 避免重复请求
    async refreshData() {
      try {
        // 检查是否需要刷新（避免频繁刷新）
        const now = Date.now();
        const timeSinceLastRefresh = this.lastRefreshTime ? now - this.lastRefreshTime : Infinity;
        const minRefreshInterval = 3000; // 最小刷新间隔3秒
        
        if (timeSinceLastRefresh < minRefreshInterval) {
          console.log(`跳过数据刷新，距离上次刷新仅${Math.round(timeSinceLastRefresh/1000)}秒`);
          return;
        }
        
        console.log('执行数据刷新...');
        this.lastRefreshTime = now;
        
        // 刷新统计数据
        await this.loadTodayStats();
        
        // 如果有权限，刷新待审核数量
        if (this.hasApprovalPermission) {
          await this.loadPendingCount();
        }
      } catch (error) {
        console.error('刷新数据失败:', error);
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.visitor-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px 20px;
  color: white;
  
  .banner-content {
    text-align: center;
    
    .banner-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .banner-subtitle {
      font-size: 14px;
      opacity: 0.9;
    }
  }
}

.modules-container {
  padding: 20px;
  
  .modules-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    
    .module-item {
      background: white;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }
      
      .module-icon {
        width: 50px;
        height: 50px;
        border-radius: 25px;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
      }
      
      .module-info {
        flex: 1;
        
        .module-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 4px;
        }
        
        .module-desc {
          font-size: 12px;
          color: #666;
          display: block;
        }
        
        .pending-count {
          font-size: 12px;
          color: #e74c3c;
          display: block;
          margin-top: 4px;
        }
      }
      
      .module-arrow {
        opacity: 0.5;
      }
    }
  }
}

.stats-container {
  padding: 0 20px 30px;
  
  :deep(.uni-card) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .uni-card__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 12px 12px 0 0;
      
      .uni-card__header-title {
        color: white;
        font-weight: bold;
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    padding: 10px 0;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #666;
      }
    }
  }
}
</style>
