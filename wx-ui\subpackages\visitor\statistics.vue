<template>
  <view class="statistics-container">
    <!-- 时间选择 -->
    <view class="time-selector">
      <uni-data-checkbox 
        v-model="timeRange"
        :localdata="timeRangeOptions"
        mode="button"
        @change="onTimeRangeChange"
      />
    </view>

    <!-- 统计卡片 -->
    <view class="stats-cards">
      <view class="stat-card">
        <view class="stat-number">{{ statistics.totalVisitors }}</view>
        <view class="stat-label">总访客数</view>
        <view class="stat-icon">
          <uni-icons type="person" size="24" color="#667eea"></uni-icons>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-number">{{ statistics.totalRegistrations }}</view>
        <view class="stat-label">总登记数</view>
        <view class="stat-icon">
          <uni-icons type="list" size="24" color="#67C23A"></uni-icons>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-number">{{ statistics.currentInBuilding }}</view>
        <view class="stat-label">在楼访客</view>
        <view class="stat-icon">
          <uni-icons type="home" size="24" color="#E6A23C"></uni-icons>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-number">{{ statistics.avgVisitDuration }}</view>
        <view class="stat-label">平均访问时长</view>
        <view class="stat-icon">
          <uni-icons type="clock" size="24" color="#F56C6C"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 访问目的统计 -->
    <uni-card title="访问目的分布" :is-shadow="false" margin="15px 0">
      <view class="purpose-stats">
        <view v-for="(item, index) in purposeStats" :key="index" class="purpose-item">
          <view class="purpose-info">
            <text class="purpose-name">{{ item.purpose }}</text>
            <text class="purpose-count">{{ item.count }}人次</text>
          </view>
          <view class="purpose-bar">
            <view class="purpose-progress" :style="{ width: item.percentage + '%' }"></view>
          </view>
        </view>
      </view>
    </uni-card>

    <!-- 部门访问统计 -->
    <uni-card title="部门访问排行" :is-shadow="false" margin="15px 0">
      <view class="department-stats">
        <view v-for="(item, index) in departmentStats" :key="index" class="department-item">
          <view class="rank-number">{{ index + 1 }}</view>
          <view class="department-info">
            <text class="department-name">{{ item.department }}</text>
            <text class="department-count">{{ item.count }}人次</text>
          </view>
        </view>
      </view>
    </uni-card>

    <!-- 时间分布统计 -->
    <uni-card title="访问时间分布" :is-shadow="false" margin="15px 0">
      <view class="time-stats">
        <view v-for="(item, index) in timeStats" :key="index" class="time-item">
          <text class="time-label">{{ item.timeRange }}</text>
          <view class="time-bar">
            <view class="time-progress" :style="{ width: item.percentage + '%' }"></view>
          </view>
          <text class="time-count">{{ item.count }}</text>
        </view>
      </view>
    </uni-card>
  </view>
</template>

<script>
import { getTodayRegistrations, listRegistrationInfo } from '@/api/system/visitor'

export default {
  data() {
    return {
      timeRange: 'today',
      timeRangeOptions: [
        { value: 'today', text: '今日' }
      ],
      statistics: {
        totalVisitors: 0,
        totalRegistrations: 0,
        currentInBuilding: 0,
        avgVisitDuration: '0小时'
      },
      purposeStats: [],
      departmentStats: [],
      timeStats: []
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '访客统计'
    });
    this.loadStatistics();
  },
  methods: {
    // 时间范围变更
    onTimeRangeChange() {
      this.loadStatistics();
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        uni.showLoading({ title: '加载中...' });
        
        // 根据时间范围获取不同的统计数据
        await Promise.all([
          this.loadBasicStats(),
          this.loadPurposeStats(),
          this.loadDepartmentStats(),
          this.loadTimeStats()
        ]);
        
      } catch (error) {
        console.error('加载统计数据失败:', error);
        this.$modal.showError('加载统计数据失败');
      } finally {
        uni.hideLoading();
      }
    },

    // 加载基础统计
    async loadBasicStats() {
      try {
        // 只支持今日统计，使用现有的今日登记接口
        if (this.timeRange === 'today') {
          const response = await getTodayRegistrations();
          
          if (response && response.code === 200 && response.data) {
            const todayData = response.data;
            
            // 计算基础统计数据
            this.statistics = {
              totalVisitors: todayData.length || 0,
              totalRegistrations: todayData.length || 0,
              currentInBuilding: todayData.filter(item => item.checkInTime && !item.checkOutTime).length || 0,
              avgVisitDuration: this.calculateAvgDuration(todayData)
            };
            return;
          }
        }
        
        // 如果没有数据或不支持的时间范围，设置默认值
        this.statistics = {
          totalVisitors: 0,
          totalRegistrations: 0,
          currentInBuilding: 0,
          avgVisitDuration: '0小时'
        };
      } catch (error) {
        console.error('加载基础统计失败:', error);
        // 设置默认值
        this.statistics = {
          totalVisitors: 0,
          totalRegistrations: 0,
          currentInBuilding: 0,
          avgVisitDuration: '0小时'
        };
      }
    },

    // 计算平均访问时长
    calculateAvgDuration(registrations) {
      if (!registrations || registrations.length === 0) {
        return '0小时';
      }
      
      const completedVisits = registrations.filter(item => 
        item.checkInTime && item.checkOutTime
      );
      
      if (completedVisits.length === 0) {
        return '0小时';
      }
      
      const totalDuration = completedVisits.reduce((sum, item) => {
        const checkIn = new Date(item.checkInTime);
        const checkOut = new Date(item.checkOutTime);
        const duration = (checkOut - checkIn) / (1000 * 60 * 60); // 转换为小时
        return sum + duration;
      }, 0);
      
      const avgHours = Math.round(totalDuration / completedVisits.length * 10) / 10;
      return `${avgHours}小时`;
    },

    // 加载访问目的统计
    async loadPurposeStats() {
      try {
        // 获取当天日期
        const today = new Date().toISOString().split('T')[0];
        
        // 调用访客列表接口，按来访事由统计（只获取当天已通过审核的数据）
        const response = await listRegistrationInfo({
          pageNum: 1,
          pageSize: 1000, // 获取足够的数据进行统计
          status: 'approved', // 只统计已通过审核的访客
          params: {
            beginTime: today,
            endTime: today
          }
        });
        
        if (response && response.code === 200 && response.rows) {
          // 统计访问目的
          const purposeMap = {};
          response.rows.forEach(item => {
            const reason = item.reasonForVisit || '其他';
            purposeMap[reason] = (purposeMap[reason] || 0) + 1;
          });
          
          // 转换为数组并计算百分比
          const totalCount = Object.values(purposeMap).reduce((sum, count) => sum + count, 0);
          this.purposeStats = Object.entries(purposeMap)
            .map(([purpose, count]) => ({
              purpose,
              count,
              percentage: totalCount > 0 ? Math.round((count / totalCount) * 100) : 0
            }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10); // 只显示前10个
        } else {
          // 使用默认数据
          this.purposeStats = [
            { purpose: '商务洽谈', count: 0, percentage: 0 },
            { purpose: '技术交流', count: 0, percentage: 0 },
            { purpose: '参观访问', count: 0, percentage: 0 }
          ];
        }
      } catch (error) {
        console.error('加载访问目的统计失败:', error);
        this.purposeStats = [
          { purpose: '商务洽谈', count: 0, percentage: 0 },
          { purpose: '技术交流', count: 0, percentage: 0 },
          { purpose: '参观访问', count: 0, percentage: 0 }
        ];
      }
    },

    // 加载部门统计
    async loadDepartmentStats() {
      try {
        // 获取当天日期
        const today = new Date().toISOString().split('T')[0];
        
        // 调用访客列表接口，按被访部门统计（只获取当天已通过审核的数据）
        const response = await listRegistrationInfo({
          pageNum: 1,
          pageSize: 1000, // 获取足够的数据进行统计
          status: 'approved', // 只统计已通过审核的访客
          params: {
            beginTime: today,
            endTime: today
          }
        });
        
        if (response && response.code === 200 && response.rows) {
          // 统计被访部门
          const departmentMap = {};
          response.rows.forEach(item => {
            const dept = item.departmentVisited || '未知部门';
            departmentMap[dept] = (departmentMap[dept] || 0) + 1;
          });
          
          // 转换为数组并排序
          this.departmentStats = Object.entries(departmentMap)
            .map(([department, count]) => ({
              department,
              count
            }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10); // 只显示前10个
        } else {
          // 使用默认数据
          this.departmentStats = [
            { department: '技术部', count: 0 },
            { department: '销售部', count: 0 },
            { department: '市场部', count: 0 }
          ];
        }
      } catch (error) {
        console.error('加载部门统计失败:', error);
        this.departmentStats = [
          { department: '技术部', count: 0 },
          { department: '销售部', count: 0 },
          { department: '市场部', count: 0 }
        ];
      }
    },

    // 加载时间分布统计
    async loadTimeStats() {
      try {
        // 获取当天日期
        const today = new Date().toISOString().split('T')[0];
        
        // 调用访客列表接口，按预计来访时间统计（只获取当天已通过审核的数据）
        const response = await listRegistrationInfo({
          pageNum: 1,
          pageSize: 1000, // 获取足够的数据进行统计
          status: 'approved', // 只统计已通过审核的访客
          params: {
            beginTime: today,
            endTime: today
          }
        });
        
        if (response && response.code === 200 && response.rows) {
          // 统计时间分布
          const timeSlots = {
            '08:00-10:00': 0,
            '10:00-12:00': 0,
            '12:00-14:00': 0,
            '14:00-16:00': 0,
            '16:00-18:00': 0,
            '18:00-20:00': 0
          };
          
          response.rows.forEach(item => {
            if (item.plannedEntryDatetime) {
              const hour = new Date(item.plannedEntryDatetime).getHours();
              if (hour >= 8 && hour < 10) timeSlots['08:00-10:00']++;
              else if (hour >= 10 && hour < 12) timeSlots['10:00-12:00']++;
              else if (hour >= 12 && hour < 14) timeSlots['12:00-14:00']++;
              else if (hour >= 14 && hour < 16) timeSlots['14:00-16:00']++;
              else if (hour >= 16 && hour < 18) timeSlots['16:00-18:00']++;
              else if (hour >= 18 && hour < 20) timeSlots['18:00-20:00']++;
            }
          });
          
          // 转换为数组并计算百分比
          const totalCount = Object.values(timeSlots).reduce((sum, count) => sum + count, 0);
          this.timeStats = Object.entries(timeSlots).map(([timeRange, count]) => ({
            timeRange,
            count,
            percentage: totalCount > 0 ? Math.round((count / totalCount) * 100) : 0
          }));
        } else {
          // 使用默认数据
          this.timeStats = [
            { timeRange: '08:00-10:00', count: 0, percentage: 0 },
            { timeRange: '10:00-12:00', count: 0, percentage: 0 },
            { timeRange: '12:00-14:00', count: 0, percentage: 0 },
            { timeRange: '14:00-16:00', count: 0, percentage: 0 },
            { timeRange: '16:00-18:00', count: 0, percentage: 0 },
            { timeRange: '18:00-20:00', count: 0, percentage: 0 }
          ];
        }
      } catch (error) {
        console.error('加载时间分布统计失败:', error);
        this.timeStats = [
          { timeRange: '08:00-10:00', count: 0, percentage: 0 },
          { timeRange: '10:00-12:00', count: 0, percentage: 0 },
          { timeRange: '12:00-14:00', count: 0, percentage: 0 },
          { timeRange: '14:00-16:00', count: 0, percentage: 0 },
          { timeRange: '16:00-18:00', count: 0, percentage: 0 },
          { timeRange: '18:00-20:00', count: 0, percentage: 0 }
        ];
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.statistics-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.time-selector {
  margin-bottom: 20px;
  
  :deep(.uni-data-checkbox) {
    .checklist-group {
      display: flex;
      gap: 10px;
      
      .checklist-box {
        border-radius: 15px;
        padding: 8px 16px;
        font-size: 12px;
        
        &.is--checked {
          background-color: #667eea;
          border-color: #667eea;
          color: white;
        }
      }
    }
  }
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  
  .stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #666;
    }
    
    .stat-icon {
      position: absolute;
      top: 15px;
      right: 15px;
      opacity: 0.3;
    }
  }
}

.purpose-stats {
  .purpose-item {
    margin-bottom: 15px;
    
    .purpose-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      
      .purpose-name {
        font-size: 14px;
        color: #333;
      }
      
      .purpose-count {
        font-size: 12px;
        color: #666;
      }
    }
    
    .purpose-bar {
      height: 6px;
      background-color: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;
      
      .purpose-progress {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: width 0.3s ease;
      }
    }
  }
}

.department-stats {
  .department-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .rank-number {
      width: 30px;
      height: 30px;
      border-radius: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      margin-right: 15px;
    }
    
    .department-info {
      flex: 1;
      
      .department-name {
        display: block;
        font-size: 14px;
        color: #333;
        margin-bottom: 2px;
      }
      
      .department-count {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

.time-stats {
  .time-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    .time-label {
      width: 100px;
      font-size: 12px;
      color: #666;
    }
    
    .time-bar {
      flex: 1;
      height: 6px;
      background-color: #f0f0f0;
      border-radius: 3px;
      margin: 0 10px;
      overflow: hidden;
      
      .time-progress {
        height: 100%;
        background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
        transition: width 0.3s ease;
      }
    }
    
    .time-count {
      width: 30px;
      text-align: right;
      font-size: 12px;
      color: #333;
    }
  }
}

// 卡片样式优化
:deep(.uni-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .uni-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    
    .uni-card__header-title {
      color: white;
      font-weight: bold;
    }
  }
  
  .uni-card__content {
    padding: 20px;
  }
}
</style>
