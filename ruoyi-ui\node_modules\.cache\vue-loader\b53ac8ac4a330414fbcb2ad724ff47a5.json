{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=style&index=0&id=31326bdb&scoped=true&lang=css", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750990421754}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750836933411}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750836980592}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750836940519}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qIOS4u+WuueWZqCAqLw0KLnBjLXJlZ2lzdGVyLWNvbnRhaW5lciB7DQogIG1pbi1oZWlnaHQ6IDEwMHZoOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjVmN2ZhIDAlLCAjYzNjZmUyIDEwMCUpOw0KICBmb250LWZhbWlseTogJ0hlbHZldGljYSBOZXVlJywgQXJpYWwsIHNhbnMtc2VyaWY7DQp9DQoNCi8qIOmhtumDqOWvvOiIqiAqLw0KLnRvcC1uYXYgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBwYWRkaW5nOiAwIDQwcHg7DQogIHBvc2l0aW9uOiBzdGlja3k7DQogIHRvcDogMDsNCiAgei1pbmRleDogMTAwOw0KfQ0KDQoubmF2LWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGhlaWdodDogNzBweDsNCiAgbWF4LXdpZHRoOiAxNDAwcHg7DQogIG1hcmdpbjogMCBhdXRvOw0KfQ0KDQoubG9nby1zZWN0aW9uIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNXB4Ow0KfQ0KDQoubmF2LWxvZ28gew0KICBoZWlnaHQ6IDQwcHg7DQp9DQoNCi5jb21wYW55LW5hbWUgew0KICBmb250LXNpemU6IDIwcHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KfQ0KDQoubmF2LWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDIwcHg7DQp9DQoNCi5uYXYtYWN0aW9ucyAuZWwtYnV0dG9uIHsNCiAgYm9yZGVyLXJhZGl1czogMjBweDsNCiAgcGFkZGluZzogOHB4IDIwcHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi8qIOS4u+WGheWuueWMuuWfnyAqLw0KLm1haW4tY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1heC13aWR0aDogMTQwMHB4Ow0KICBtYXJnaW46IDAgYXV0bzsNCiAgcGFkZGluZzogNDBweDsNCiAgZ2FwOiA0MHB4Ow0KICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTEwcHgpOw0KfQ0KDQovKiDnp7vliqjnq6/luIPlsYDkvJjljJYgKi8NCi5tYWluLWNvbnRlbnQubW9iaWxlLWxheW91dCB7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIHBhZGRpbmc6IDIwcHg7DQogIGdhcDogMjBweDsNCiAgbWF4LXdpZHRoOiAxMDAlOw0KfQ0KDQovKiDnp7vliqjnq6/mrKLov47ljLrln58gKi8NCi5tb2JpbGUtd2VsY29tZSB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoud2VsY29tZS1oZWFkZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi53ZWxjb21lLWhlYWRlciAud2VsY29tZS1pY29uIHsNCiAgd2lkdGg6IDYwcHg7DQogIGhlaWdodDogNjBweDsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgbWFyZ2luOiAwIGF1dG8gMTVweDsNCn0NCg0KLndlbGNvbWUtaGVhZGVyIC53ZWxjb21lLWljb24gaSB7DQogIGZvbnQtc2l6ZTogMjhweDsNCiAgY29sb3I6ICNmZmY7DQp9DQoNCi53ZWxjb21lLWhlYWRlciBoMiB7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgY29sb3I6ICMyYzNlNTA7DQogIG1hcmdpbjogMCAwIDEwcHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi53ZWxjb21lLWhlYWRlciAud2VsY29tZS1kZXNjIHsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbWFyZ2luOiAwOw0KfQ0KDQovKiDov5vluqbmjIfnpLrlmaggKi8NCi5wcm9ncmVzcy1pbmRpY2F0b3Igew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KfQ0KDQoucHJvZ3Jlc3MtYmFyIHsNCiAgaGVpZ2h0OiA2cHg7DQogIGJhY2tncm91bmQ6ICNmMGYwZjA7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLnByb2dyZXNzLWZpbGwgew0KICBoZWlnaHQ6IDEwMCU7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzIGVhc2U7DQp9DQoNCi5wcm9ncmVzcy10ZXh0IHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjN2Y4YzhkOw0KfQ0KDQovKiDnp7vliqjnq6/ooajljZXlrrnlmaggKi8NCi5tb2JpbGUtZm9ybS1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg0KLyog56e75Yqo56uv6KGo5Y2V5qCH6aKYICovDQoubW9iaWxlLWZvcm0taGVhZGVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgY29sb3I6ICNmZmY7DQogIHBhZGRpbmc6IDIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLm1vYmlsZS1mb3JtLWhlYWRlciBoMyB7DQogIG1hcmdpbjogMCAwIDhweDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQoubW9iaWxlLWZvcm0taGVhZGVyIC5mb3JtLXN1YnRpdGxlIHsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE0cHg7DQogIG9wYWNpdHk6IDAuOTsNCn0NCg0KLyog56e75Yqo56uv6KGo5Y2V5YaF5a65ICovDQoubW9iaWxlLWZvcm0tY29udGVudCB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi8qIOenu+WKqOerr+ihqOWNleWNoeeJhyAqLw0KLm1vYmlsZS1mb3JtLWNhcmQgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5tb2JpbGUtZm9ybS1jYXJkIC5jYXJkLWhlYWRlciB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIHBhZGRpbmc6IDE1cHggMjBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCn0NCg0KLm1vYmlsZS1mb3JtLWNhcmQgLmNhcmQtaGVhZGVyIGkgew0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiAjNjY3ZWVhOw0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCn0NCg0KLm1vYmlsZS1mb3JtLWNhcmQgLmNhcmQtdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmbGV4OiAxOw0KfQ0KDQoubW9iaWxlLWZvcm0tY2FyZCAuYWRkLXZpc2l0b3ItYnRuIHsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQp9DQoNCi5tb2JpbGUtZm9ybS1jYXJkIC5jYXJkLWNvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQovKiDnp7vliqjnq6/ooajljZXpobkgKi8NCi5tb2JpbGUtZm9ybS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLm1vYmlsZS1mb3JtLWl0ZW0gLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBsaW5lLWhlaWdodDogMS40Ow0KICBwYWRkaW5nLWJvdHRvbTogOHB4Ow0KfQ0KDQovKiDnp7vliqjnq6/ovpPlhaXmoYYgKi8NCi5tb2JpbGUtaW5wdXQgLmVsLWlucHV0X19pbm5lciB7DQogIGhlaWdodDogNDRweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogIHBhZGRpbmc6IDAgMTVweDsNCn0NCg0KLm1vYmlsZS1pbnB1dCAuZWwtdGV4dGFyZWFfX2lubmVyIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogIHBhZGRpbmc6IDEycHggMTVweDsNCiAgbGluZS1oZWlnaHQ6IDEuNTsNCn0NCg0KLyog56e75Yqo56uv5pel5pyf6YCJ5oup5ZmoICovDQoubW9iaWxlLWRhdGUtcGlja2VyIC5lbC1pbnB1dF9faW5uZXIgew0KICBoZWlnaHQ6IDQ0cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQovKiDnp7vliqjnq6/orr/lrqLpobkgKi8NCi5tb2JpbGUtdmlzaXRvci1pdGVtIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi5tb2JpbGUtdmlzaXRvci1pdGVtOmxhc3QtY2hpbGQgew0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQoubW9iaWxlLXZpc2l0b3ItaXRlbSAudmlzaXRvci1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQp9DQoNCi5tb2JpbGUtdmlzaXRvci1pdGVtIC52aXNpdG9yLWJhZGdlIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgY29sb3I6ICM2Yzc1N2Q7DQogIHBhZGRpbmc6IDRweCAxMnB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5tb2JpbGUtdmlzaXRvci1pdGVtIC52aXNpdG9yLWJhZGdlLnByaW1hcnkgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBjb2xvcjogI2ZmZjsNCiAgYm9yZGVyOiBub25lOw0KfQ0KDQoubW9iaWxlLXZpc2l0b3ItaXRlbSAucmVtb3ZlLWJ0biB7DQogIHdpZHRoOiAyOHB4Ow0KICBoZWlnaHQ6IDI4cHg7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi8qIOenu+WKqOerr+aTjeS9nOWMuuWfnyAqLw0KLm1vYmlsZS1hY3Rpb25zIHsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5zdWJtaXQtYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGdhcDogMTJweDsNCn0NCg0KLnN1Y2Nlc3MtYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGdhcDogMTJweDsNCn0NCg0KLyog56e75Yqo56uv5oyJ6ZKuICovDQoubW9iaWxlLXN1Ym1pdC1idG4sDQoubW9iaWxlLXJlc2V0LWJ0biwNCi5tb2JpbGUtYWN0aW9uLWJ0biB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDQ4cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IG5vbmU7DQp9DQoNCi5tb2JpbGUtc3VibWl0LWJ0biB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQoubW9iaWxlLXN1Ym1pdC1idG46aG92ZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNWE2ZmQ4IDAlLCAjNmE0MTkwIDEwMCUpOw0KfQ0KDQoubW9iaWxlLXJlc2V0LWJ0biB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGNvbG9yOiAjNmM3NTdkOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KfQ0KDQoubW9iaWxlLXJlc2V0LWJ0bjpob3ZlciB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGNvbG9yOiAjNDk1MDU3Ow0KfQ0KDQoubW9iaWxlLWFjdGlvbi1idG4gew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBjb2xvcjogIzY2N2VlYTsNCiAgYm9yZGVyOiAxcHggc29saWQgIzY2N2VlYTsNCn0NCg0KLm1vYmlsZS1hY3Rpb24tYnRuOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogIzY2N2VlYTsNCiAgY29sb3I6ICNmZmY7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC8qIOmhtumDqOWvvOiIquenu+WKqOerr+S8mOWMliAqLw0KICAudG9wLW5hdiB7DQogICAgcGFkZGluZzogMCAyMHB4Ow0KICB9DQoNCiAgLm5hdi1jb250ZW50IHsNCiAgICBoZWlnaHQ6IDYwcHg7DQogIH0NCg0KICAuY29tcGFueS1uYW1lIHsNCiAgICBmb250LXNpemU6IDE2cHg7DQogIH0NCg0KICAubmF2LWFjdGlvbnMgew0KICAgIGdhcDogMTBweDsNCiAgfQ0KDQogIC8qIOS4u+WGheWuueWMuuWfn+enu+WKqOerr+S8mOWMliAqLw0KICAubWFpbi1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAxNXB4Ow0KICAgIGdhcDogMTVweDsNCiAgfQ0KDQogIC5tYWluLWNvbnRlbnQubW9iaWxlLWxheW91dCB7DQogICAgcGFkZGluZzogMTBweDsNCiAgICBnYXA6IDE1cHg7DQogIH0NCg0KICAvKiDnp7vliqjnq6/mrKLov47ljLrln5/kvJjljJYgKi8NCiAgLm1vYmlsZS13ZWxjb21lIHsNCiAgICBwYWRkaW5nOiAxNXB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIH0NCg0KICAud2VsY29tZS1oZWFkZXIgLndlbGNvbWUtaWNvbiB7DQogICAgd2lkdGg6IDUwcHg7DQogICAgaGVpZ2h0OiA1MHB4Ow0KICB9DQoNCiAgLndlbGNvbWUtaGVhZGVyIC53ZWxjb21lLWljb24gaSB7DQogICAgZm9udC1zaXplOiAyNHB4Ow0KICB9DQoNCiAgLndlbGNvbWUtaGVhZGVyIGgyIHsNCiAgICBmb250LXNpemU6IDIwcHg7DQogIH0NCg0KICAvKiDnp7vliqjnq6/ooajljZXkvJjljJYgKi8NCiAgLm1vYmlsZS1mb3JtLWNvbnRhaW5lciB7DQogICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICB9DQoNCiAgLm1vYmlsZS1mb3JtLWhlYWRlciB7DQogICAgcGFkZGluZzogMTVweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1oZWFkZXIgaDMgew0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAxNXB4Ow0KICB9DQoNCiAgLm1vYmlsZS1mb3JtLWNhcmQgew0KICAgIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIH0NCg0KICAubW9iaWxlLWZvcm0tY2FyZCAuY2FyZC1oZWFkZXIgew0KICAgIHBhZGRpbmc6IDEycHggMTVweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1jYXJkIC5jYXJkLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDE1cHg7DQogIH0NCg0KICAvKiDnp7vliqjnq6/orr/lrqLpobnkvJjljJYgKi8NCiAgLm1vYmlsZS12aXNpdG9yLWl0ZW0gew0KICAgIHBhZGRpbmc6IDEycHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTJweDsNCiAgfQ0KDQogIC8qIOenu+WKqOerr+aTjeS9nOWMuuWfn+S8mOWMliAqLw0KICAubW9iaWxlLWFjdGlvbnMgew0KICAgIHBhZGRpbmc6IDE1cHg7DQogIH0NCg0KICAubW9iaWxlLXN1Ym1pdC1idG4sDQogIC5tb2JpbGUtcmVzZXQtYnRuLA0KICAubW9iaWxlLWFjdGlvbi1idG4gew0KICAgIGhlaWdodDogNDRweDsNCiAgICBmb250LXNpemU6IDE1cHg7DQogIH0NCn0NCg0KLyog6LaF5bCP5bGP5bmV5LyY5YyWICovDQpAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHsNCiAgLnRvcC1uYXYgew0KICAgIHBhZGRpbmc6IDAgMTVweDsNCiAgfQ0KDQogIC5uYXYtY29udGVudCB7DQogICAgaGVpZ2h0OiA1NXB4Ow0KICB9DQoNCiAgLmNvbXBhbnktbmFtZSB7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICB9DQoNCiAgLm1haW4tY29udGVudC5tb2JpbGUtbGF5b3V0IHsNCiAgICBwYWRkaW5nOiA4cHg7DQogIH0NCg0KICAubW9iaWxlLXdlbGNvbWUgew0KICAgIHBhZGRpbmc6IDEycHg7DQogIH0NCg0KICAubW9iaWxlLWZvcm0taGVhZGVyIHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICB9DQoNCiAgLm1vYmlsZS1mb3JtLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDEycHg7DQogIH0NCg0KICAubW9iaWxlLWFjdGlvbnMgew0KICAgIHBhZGRpbmc6IDEycHg7DQogIH0NCn0NCg0KLyog5bem5L6n5L+h5oGv6Z2i5p2/ICovDQouaW5mby1wYW5lbCB7DQogIGZsZXg6IDAgMCA0MDBweDsNCn0NCg0KLndlbGNvbWUtY2FyZCB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDE2cHg7DQogIHBhZGRpbmc6IDMycHg7DQogIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBoZWlnaHQ6IGZpdC1jb250ZW50Ow0KICBwb3NpdGlvbjogc3RpY2t5Ow0KICB0b3A6IDExMHB4Ow0KfQ0KDQoud2VsY29tZS1pY29uIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQoud2VsY29tZS1pY29uIGkgew0KICBmb250LXNpemU6IDY0cHg7DQogIGNvbG9yOiAjMzQ5OGRiOw0KfQ0KDQoud2VsY29tZS1jYXJkIGgyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgZm9udC1zaXplOiAyOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQoud2VsY29tZS1kZXNjIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogIzdmOGM4ZDsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBsaW5lLWhlaWdodDogMS42Ow0KfQ0KDQovKiDmtYHnqIvmraXpqqQgKi8NCi5wcm9jZXNzLXN0ZXBzIHsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCn0NCg0KLnN0ZXAtaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTZweDsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCiAgcGFkZGluZzogMTZweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KLnN0ZXAtaXRlbS5hY3RpdmUgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzQ5OGRiLCAjMjk4MGI5KTsNCiAgY29sb3I6IHdoaXRlOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoOHB4KTsNCn0NCg0KLnN0ZXAtaXRlbTpub3QoLmFjdGl2ZSkgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBjb2xvcjogIzZjNzU3ZDsNCn0NCg0KLnN0ZXAtY2lyY2xlIHsNCiAgd2lkdGg6IDMycHg7DQogIGhlaWdodDogMzJweDsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi5zdGVwLWl0ZW0uYWN0aXZlIC5zdGVwLWNpcmNsZSB7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsNCiAgY29sb3I6IHdoaXRlOw0KfQ0KDQouc3RlcC1pdGVtOm5vdCguYWN0aXZlKSAuc3RlcC1jaXJjbGUgew0KICBiYWNrZ3JvdW5kOiAjZGVlMmU2Ow0KICBjb2xvcjogIzZjNzU3ZDsNCn0NCg0KLnN0ZXAtdGV4dCBoNCB7DQogIG1hcmdpbjogMCAwIDRweCAwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5zdGVwLXRleHQgcCB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBvcGFjaXR5OiAwLjg7DQp9DQoNCi8qIOWuieWFqOaPkOekuiAqLw0KLnNlY3VyaXR5LXRpcHMgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBib3JkZXItbGVmdDogNHB4IHNvbGlkICMzNDk4ZGI7DQp9DQoNCi5zZWN1cml0eS10aXBzIGg0IHsNCiAgbWFyZ2luOiAwIDAgMTJweCAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLnNlY3VyaXR5LXRpcHMgdWwgew0KICBtYXJnaW46IDA7DQogIHBhZGRpbmctbGVmdDogMjBweDsNCiAgY29sb3I6ICM2Yzc1N2Q7DQp9DQoNCi5zZWN1cml0eS10aXBzIGxpIHsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjQ7DQp9DQoNCi8qIOWPs+S+p+ihqOWNlemdouadvyAqLw0KLmZvcm0tcGFuZWwgew0KICBmbGV4OiAxOw0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLyog6L+b5bqm5oyH56S65ZmoICovDQoucHJvZ3Jlc3MtaW5kaWNhdG9yIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgcGFkZGluZzogMjRweCAzMnB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnByb2dyZXNzLWJhciB7DQogIGhlaWdodDogNnB4Ow0KICBiYWNrZ3JvdW5kOiAjZTllY2VmOw0KICBib3JkZXItcmFkaXVzOiAzcHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLnByb2dyZXNzLWZpbGwgew0KICBoZWlnaHQ6IDEwMCU7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzM0OThkYiwgIzI5ODBiOSk7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlOw0KfQ0KDQoucHJvZ3Jlc3MtdGV4dCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICM2Yzc1N2Q7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLyog6KGo5Y2V5YaF5a65ICovDQouZm9ybS1jb250ZW50IHsNCiAgcGFkZGluZzogMzJweDsNCiAgbWF4LWhlaWdodDogY2FsYygxMDB2aCAtIDMwMHB4KTsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCg0KLmZvcm0taGVhZGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQouZm9ybS1oZWFkZXIgaDMgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBnYXA6IDEycHg7DQp9DQoNCi5mb3JtLWhlYWRlciBwIHsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQovKiDpqozor4HmlrnlvI/nvZHmoLwgKi8NCi52ZXJpZnktZ3JpZCB7DQogIGRpc3BsYXk6IGdyaWQ7DQogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7DQogIGdhcDogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCn0NCg0KLnZlcmlmeS1jYXJkIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBwYWRkaW5nOiAyNHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KLnZlcmlmeS1jYXJkOmhvdmVyIHsNCiAgYm9yZGVyLWNvbG9yOiAjMzQ5OGRiOw0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3gtc2hhZG93OiAwIDRweCAxNnB4IHJnYmEoNTIsIDE1MiwgMjE5LCAwLjE1KTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KfQ0KDQoudmVyaWZ5LWNhcmQuc2VsZWN0ZWQgew0KICBib3JkZXItY29sb3I6ICMzNDk4ZGI7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNDk4ZGIsICMyOTgwYjkpOw0KICBjb2xvcjogd2hpdGU7DQogIGJveC1zaGFkb3c6IDAgOHB4IDI0cHggcmdiYSg1MiwgMTUyLCAyMTksIDAuMyk7DQp9DQoNCi52ZXJpZnktaWNvbiB7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQp9DQoNCi52ZXJpZnktaWNvbiBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBjb2xvcjogIzM0OThkYjsNCn0NCg0KLnZlcmlmeS1jYXJkLnNlbGVjdGVkIC52ZXJpZnktaWNvbiBpIHsNCiAgY29sb3I6IHdoaXRlOw0KfQ0KDQoudmVyaWZ5LWNhcmQgaDQgew0KICBtYXJnaW46IDAgMCA4cHggMDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQoudmVyaWZ5LWNhcmQgcCB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBvcGFjaXR5OiAwLjg7DQp9DQoNCi52ZXJpZnktc3RhdHVzIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDEycHg7DQogIHJpZ2h0OiAxMnB4Ow0KICB3aWR0aDogMjRweDsNCiAgaGVpZ2h0OiAyNHB4Ow0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQp9DQoNCi8qIOmqjOivgeaTjeS9nOWMuuWfnyAqLw0KLnZlcmlmeS1vcGVyYXRpb24gew0KICBtYXJnaW4tdG9wOiAzMnB4Ow0KICBwYWRkaW5nOiAyNHB4Ow0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KfQ0KDQoub3BlcmF0aW9uLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLm9wZXJhdGlvbi1oZWFkZXIgaDQgew0KICBtYXJnaW46IDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi8qIOi6q+S7veihqOWNlSAqLw0KLmlkZW50aXR5LWZvcm0gew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgcGFkZGluZzogMjRweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCn0NCg0KLyog6Lqr5Lu96K+B6K+75Y2h5ZmoICovDQouaWQtY2FyZC1yZWFkZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5yZWFkZXItdmlzdWFsIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDQwcHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQp9DQoNCi5yZWFkZXItYW5pbWF0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnJvdGF0aW5nIHsNCiAgYW5pbWF0aW9uOiByb3RhdGUgMnMgbGluZWFyIGluZmluaXRlOw0KfQ0KDQpAa2V5ZnJhbWVzIHJvdGF0ZSB7DQogIGZyb20geyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfQ0KICB0byB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH0NCn0NCg0KLnJlYWRlci1hbmltYXRpb24gaSB7DQogIGZvbnQtc2l6ZTogNjRweDsNCiAgY29sb3I6ICMzNDk4ZGI7DQp9DQoNCi5yZWFkZXItdmlzdWFsIGg0IHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLnJlYWRlci12aXN1YWwgcCB7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQoucmVhZGVyLXRpcHMgew0KICBtYXJnaW46IDI0cHggMDsNCn0NCg0KLyog5Lq66IS46K+G5YirICovDQouZmFjZS1yZWNvZ25pdGlvbiB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmNhbWVyYS1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgcGFkZGluZzogNDBweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCn0NCg0KLmNhbWVyYS1mcmFtZSB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgd2lkdGg6IDI4MHB4Ow0KICBoZWlnaHQ6IDIxMHB4Ow0KICBtYXJnaW46IDAgYXV0byAyNHB4Ow0KICBib3JkZXI6IDNweCBzb2xpZCAjMzQ5OGRiOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLmNhbWVyYS1vdmVybGF5IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNnB4Ow0KfQ0KDQouZmFjZS1vdXRsaW5lIHsNCiAgd2lkdGg6IDEyMHB4Ow0KICBoZWlnaHQ6IDE1MHB4Ow0KICBib3JkZXI6IDJweCBkYXNoZWQgIzM0OThkYjsNCiAgYm9yZGVyLXJhZGl1czogNjBweDsNCiAgb3BhY2l0eTogMC42Ow0KfQ0KDQouY2FtZXJhLWljb24gew0KICBmb250LXNpemU6IDMycHg7DQogIGNvbG9yOiAjMzQ5OGRiOw0KfQ0KDQouc2Nhbm5pbmctbGluZSB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiAwOw0KICBsZWZ0OiAwOw0KICByaWdodDogMDsNCiAgaGVpZ2h0OiAycHg7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQsICMzNDk4ZGIsIHRyYW5zcGFyZW50KTsNCiAgYW5pbWF0aW9uOiBzY2FuIDJzIGVhc2UtaW4tb3V0IGluZmluaXRlOw0KfQ0KDQpAa2V5ZnJhbWVzIHNjYW4gew0KICAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsgfQ0KICA1MCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMjA2cHgpOyB9DQogIDEwMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7IH0NCn0NCg0KLyog6KGo5Y2V5YiG57uEICovDQouZm9ybS1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCn0NCg0KLnNlY3Rpb24tdGl0bGUgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBwYWRkaW5nLWJvdHRvbTogOHB4Ow0KICBib3JkZXItYm90dG9tOiAycHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnNlY3Rpb24taGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KfQ0KDQovKiDlkIzooYzkurrlkZggKi8NCi5uby1jb21wYW5pb25zIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBwYWRkaW5nOiA0MHB4Ow0KICBjb2xvcjogIzdmOGM4ZDsNCn0NCg0KLm5vLWNvbXBhbmlvbnMgaSB7DQogIGZvbnQtc2l6ZTogNDhweDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgY29sb3I6ICNiZGMzYzc7DQp9DQoNCi5jb21wYW5pb25zLXRhYmxlIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgcGFkZGluZzogMTZweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQovKiDmiJDlip/pobXpnaIgKi8NCi5zdWNjZXNzLWNvbnRlbnQgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5zdWNjZXNzLWljb24gew0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQouc3VjY2Vzcy1pY29uIGkgew0KICBmb250LXNpemU6IDgwcHg7DQogIGNvbG9yOiAjMjdhZTYwOw0KfQ0KDQouc3VjY2Vzcy1jb250ZW50IGg0IHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMjhweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogMTJweDsNCn0NCg0KLnN1Y2Nlc3MtbWVzc2FnZSB7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBmb250LXNpemU6IDE2cHg7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi5yZWdpc3Rlci1zdW1tYXJ5IHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgcGFkZGluZzogMjRweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCn0NCg0KLnJlZ2lzdGVyLXN1bW1hcnkgaDUgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5hY2Nlc3MtY3JlZGVudGlhbCB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHRleHQtYWxpZ246IGxlZnQ7DQp9DQoNCi5hY2Nlc3MtY3JlZGVudGlhbCBoNSB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmNyZWRlbnRpYWwtY2FyZCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMjRweDsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIGJvcmRlcjogMnB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5xci1jb2RlLWNvbnRhaW5lciB7DQogIGZsZXg6IDAgMCAxMjBweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQoucXItY29kZSB7DQogIHdpZHRoOiAxMjBweDsNCiAgaGVpZ2h0OiAxMjBweDsNCiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQoucXItY29kZSBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBjb2xvcjogIzdmOGM4ZDsNCn0NCg0KLnFyLWNvZGUtaWQgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjNmM3NTdkOw0KICBmb250LWZhbWlseTogbW9ub3NwYWNlOw0KICBtYXJnaW46IDA7DQp9DQoNCi5jcmVkZW50aWFsLWluZm8gew0KICBmbGV4OiAxOw0KfQ0KDQouY3JlZGVudGlhbC1pbmZvIGg2IHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogMTJweDsNCn0NCg0KLmNyZWRlbnRpYWwtaW5mbyB1bCB7DQogIG1hcmdpbjogMDsNCiAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KICBjb2xvcjogIzZjNzU3ZDsNCn0NCg0KLmNyZWRlbnRpYWwtaW5mbyBsaSB7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS40Ow0KfQ0KDQovKiDlupXpg6jmk43kvZzmjInpkq4gKi8NCi5mb3JtLWFjdGlvbnMgew0KICBwYWRkaW5nOiAyNHB4IDMycHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTllY2VmOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5maW5hbC1hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxNnB4Ow0KfQ0KDQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHsNCiAgLm1haW4tY29udGVudCB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDI0cHg7DQogIH0NCiAgDQogIC5pbmZvLXBhbmVsIHsNCiAgICBmbGV4OiBub25lOw0KICB9DQogIA0KICAud2VsY29tZS1jYXJkIHsNCiAgICBwb3NpdGlvbjogc3RhdGljOw0KICB9DQogIA0KICAudmVyaWZ5LWdyaWQgew0KICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyOw0KICB9DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAubWFpbi1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAyMHB4Ow0KICB9DQogIA0KICAubmF2LWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDAgMjBweDsNCiAgfQ0KICANCiAgLmNvbXBhbnktbmFtZSB7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICB9DQogIA0KICAuZm9ybS1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAyMHB4Ow0KICB9DQogIA0KICAuY3JlZGVudGlhbC1jYXJkIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgfQ0KICANCiAgLmZvcm0tYWN0aW9ucyB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDE2cHg7DQogIH0NCn0NCg0KLyogRWxlbWVudCBVSSDmoLflvI/opobnm5YgKi8NCi5lbC1mb3JtLWl0ZW0gew0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQouZWwtaW5wdXRfX2lubmVyIHsNCiAgaGVpZ2h0OiA0NHB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5lbC1pbnB1dF9faW5uZXI6Zm9jdXMgew0KICBib3JkZXItY29sb3I6ICMzNDk4ZGI7DQogIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDUyLCAxNTIsIDIxOSwgMC4xKTsNCn0NCg0KLmVsLXNlbGVjdCB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouZWwtZGF0ZS1lZGl0b3Igew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLmVsLWJ1dHRvbi0tbGFyZ2Ugew0KICBoZWlnaHQ6IDQ0cHg7DQogIHBhZGRpbmc6IDEycHggMjRweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5lbC1idXR0b24tLXByaW1hcnkgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzQ5OGRiLCAjMjk4MGI5KTsNCiAgYm9yZGVyOiBub25lOw0KfQ0KDQouZWwtYnV0dG9uLS1wcmltYXJ5OmhvdmVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzI5ODBiOSwgIzFmNGU3OSk7DQp9DQoNCi5lbC1kZXNjcmlwdGlvbnMgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi5lbC10YWJsZSB7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLmVsLWFsZXJ0IHsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQovKiDmn6Xor6Llr7nor53moYbmoLflvI8gKi8NCi5xdWVyeS1kaWFsb2cgLmVsLWRpYWxvZyB7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQp9DQoNCi5xdWVyeS1kaWFsb2cgLmVsLWRpYWxvZ19faGVhZGVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgY29sb3I6ICNmZmY7DQogIHBhZGRpbmc6IDIwcHggMjRweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweCAxMnB4IDAgMDsNCn0NCg0KLnF1ZXJ5LWRpYWxvZyAuZWwtZGlhbG9nX190aXRsZSB7DQogIGNvbG9yOiAjZmZmOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5xdWVyeS1kaWFsb2cgLmVsLWRpYWxvZ19faGVhZGVyYnRuIC5lbC1kaWFsb2dfX2Nsb3NlIHsNCiAgY29sb3I6ICNmZmY7DQogIGZvbnQtc2l6ZTogMjBweDsNCn0NCg0KLnF1ZXJ5LWRpYWxvZyAuZWwtZGlhbG9nX19ib2R5IHsNCiAgcGFkZGluZzogMDsNCn0NCg0KLnF1ZXJ5LWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDI0cHg7DQp9DQoNCi5xdWVyeS1mb3JtLXNlY3Rpb24gew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHBhZGRpbmc6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5xdWVyeS1mb3JtLXNlY3Rpb24gaDQgew0KICBtYXJnaW46IDAgMCAyMHB4IDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQoucXVlcnktZm9ybS1zZWN0aW9uIGg0IGkgew0KICBjb2xvcjogIzY2N2VlYTsNCn0NCg0KLnF1ZXJ5LXJlc3VsdHMtc2VjdGlvbiB7DQogIG1hcmdpbi10b3A6IDI0cHg7DQp9DQoNCi5xdWVyeS1yZXN1bHRzLXNlY3Rpb24gaDQgew0KICBtYXJnaW46IDAgMCAxNnB4IDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KICBwYWRkaW5nLWJvdHRvbTogOHB4Ow0KICBib3JkZXItYm90dG9tOiAycHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnF1ZXJ5LXJlc3VsdHMtc2VjdGlvbiBoNCBpIHsNCiAgY29sb3I6ICM2NjdlZWE7DQp9DQoNCi5yZXN1bHRzLWxpc3Qgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDE2cHg7DQp9DQoNCi5yZXN1bHQtaXRlbSB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcGFkZGluZzogMjBweDsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQp9DQoNCi5yZXN1bHQtaXRlbTpob3ZlciB7DQogIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQp9DQoNCi5yZXN1bHQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBwYWRkaW5nLWJvdHRvbTogMTJweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi52aXNpdG9yLWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDRweDsNCn0NCg0KLnZpc2l0b3ItbmFtZSB7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICMyYzNlNTA7DQp9DQoNCi52aXNpdG9yLXBob25lIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzZjNzU3ZDsNCn0NCg0KLnN0YXR1cy1iYWRnZSB7DQogIHBhZGRpbmc6IDZweCAxMnB4Ow0KICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWluLXdpZHRoOiA2MHB4Ow0KfQ0KDQouc3RhdHVzLXBlbmRpbmcgew0KICBiYWNrZ3JvdW5kOiAjZmZmM2NkOw0KICBjb2xvcjogIzg1NjQwNDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2ZmZWFhNzsNCn0NCg0KLnN0YXR1cy1hcHByb3ZlZCB7DQogIGJhY2tncm91bmQ6ICNkNGVkZGE7DQogIGNvbG9yOiAjMTU1NzI0Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjYzNlNmNiOw0KfQ0KDQouc3RhdHVzLXJlamVjdGVkIHsNCiAgYmFja2dyb3VuZDogI2Y4ZDdkYTsNCiAgY29sb3I6ICM3MjFjMjQ7DQogIGJvcmRlcjogMXB4IHNvbGlkICNmNWM2Y2I7DQp9DQoNCi5zdGF0dXMtY2FuY2VsbGVkIHsNCiAgYmFja2dyb3VuZDogI2UyZTNlNTsNCiAgY29sb3I6ICMzODNkNDE7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkNmQ4ZGI7DQp9DQoNCi5zdGF0dXMtY29tcGxldGVkIHsNCiAgYmFja2dyb3VuZDogI2QxZWNmMTsNCiAgY29sb3I6ICMwYzU0NjA7DQogIGJvcmRlcjogMXB4IHNvbGlkICNiZWU1ZWI7DQp9DQoNCi5zdGF0dXMtZGVmYXVsdCB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGNvbG9yOiAjNmM3NTdkOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZGVlMmU2Ow0KfQ0KDQoucmVzdWx0LWRldGFpbHMgew0KICBkaXNwbGF5OiBncmlkOw0KICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7DQogIGdhcDogMTJweDsNCn0NCg0KLmRldGFpbC1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5kZXRhaWwtcm93IC5sYWJlbCB7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjNDk1MDU3Ow0KICBtaW4td2lkdGg6IDgwcHg7DQogIGZsZXgtc2hyaW5rOiAwOw0KfQ0KDQouZGV0YWlsLXJvdyAudmFsdWUgew0KICBjb2xvcjogIzZjNzU3ZDsNCiAgZmxleDogMTsNCiAgd29yZC1icmVhazogYnJlYWstYWxsOw0KfQ0KDQoubm8tcmVzdWx0cyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogNjBweCAyMHB4Ow0KICBjb2xvcjogIzZjNzU3ZDsNCn0NCg0KLm5vLXJlc3VsdHMgaSB7DQogIGZvbnQtc2l6ZTogNjRweDsNCiAgY29sb3I6ICNkZWUyZTY7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQp9DQoNCi5uby1yZXN1bHRzIGg0IHsNCiAgbWFyZ2luOiAwIDAgOHB4IDA7DQogIGNvbG9yOiAjNDk1MDU3Ow0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5uby1yZXN1bHRzIHAgew0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDEuNTsNCn0NCg0KLmRpYWxvZy1mb290ZXIgew0KICBwYWRkaW5nOiAxNnB4IDI0cHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTllY2VmOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5xdWVyeS1kaWFsb2cgew0KICAgIHdpZHRoOiA5NSUgIWltcG9ydGFudDsNCiAgfQ0KDQogIC5xdWVyeS1jb250YWluZXIgew0KICAgIHBhZGRpbmc6IDE2cHg7DQogIH0NCg0KICAucXVlcnktZm9ybS1zZWN0aW9uIHsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KICB9DQoNCiAgLnJlc3VsdC1kZXRhaWxzIHsNCiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsNCiAgICBnYXA6IDhweDsNCiAgfQ0KDQogIC5yZXN1bHQtaGVhZGVyIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICAgIGdhcDogMTJweDsNCiAgfQ0KDQogIC52aXNpdG9yLWluZm8gew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQoNCiAgLnN0YXR1cy1iYWRnZSB7DQogICAgYWxpZ24tc2VsZjogZmxleC1zdGFydDsNCiAgfQ0KDQogIC5kaWFsb2ctZm9vdGVyIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogMTJweDsNCiAgfQ0KDQogIC5uYXYtYWN0aW9ucyB7DQogICAgZ2FwOiAxMHB4Ow0KICB9DQoNCiAgLm5hdi1hY3Rpb25zIC5lbC1idXR0b24gew0KICAgIHBhZGRpbmc6IDZweCAxNnB4Ow0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHsNCiAgLmNvbXBhbnktbmFtZSB7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICB9DQoNCiAgLm5hdi1hY3Rpb25zIC5lbC1idXR0b24gew0KICAgIHBhZGRpbmc6IDRweCAxMnB4Ow0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgfQ0KDQogIC52aXNpdG9yLW5hbWUgew0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgfQ0KDQogIC5kZXRhaWwtcm93IHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogNHB4Ow0KICB9DQoNCiAgLmRldGFpbC1yb3cgLmxhYmVsIHsNCiAgICBtaW4td2lkdGg6IGF1dG87DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["self-register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6hCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "self-register.vue", "sourceRoot": "src/views/asc/visitor", "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <!-- <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" /> -->\r\n          <span class=\"company-name\">高义钢铁访客登记系统</span>\r\n        </div>\r\n        <div class=\"nav-actions\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"showQueryDialog\">查询登记</el-button>\r\n          <!-- <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 - 移动端优化 -->\r\n    <div class=\"main-content mobile-layout\">\r\n      <!-- 移动端欢迎区域 -->\r\n      <!-- <div class=\"mobile-welcome\" v-if=\"!registrationCompleted\"> -->\r\n        <!-- <div class=\"welcome-header\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>访客登记</h2>\r\n          <p class=\"welcome-desc\">请填写以下信息完成登记</p>\r\n        </div> -->\r\n\r\n        <!-- 简化的进度指示 -->\r\n        <!-- <div class=\"progress-indicator\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: registrationCompleted ? '100%' : '60%' }\"></div>\r\n          </div>\r\n          <div class=\"progress-text\">\r\n            {{ registrationCompleted ? '登记完成' : '正在填写信息...' }}\r\n          </div>\r\n        </div> -->\r\n      <!-- </div> -->\r\n\r\n      <!-- 表单容器 - 全屏移动端布局 -->\r\n      <div class=\"mobile-form-container\">\r\n        <!-- 表单标题 -->\r\n        <div class=\"mobile-form-header\" v-if=\"!registrationCompleted\">\r\n          <h3><i class=\"el-icon-edit-outline\"></i> 填写登记信息</h3>\r\n          <p class=\"form-subtitle\">共{{ totalVisitors }}人，请确保信息准确</p>\r\n        </div>\r\n\r\n        <!-- 成功页面标题 -->\r\n        <!-- <div class=\"mobile-form-header\" v-else>\r\n          <h3><i class=\"el-icon-circle-check\"></i> 登记成功</h3>\r\n          <p class=\"form-subtitle\">您的访客登记已提交成功</p>\r\n        </div> -->\r\n\r\n        <!-- 移动端表单内容 -->\r\n        <div class=\"mobile-form-content\" v-if=\"!registrationCompleted\">\r\n          <el-form ref=\"visitForm\" :model=\"formData\" :rules=\"visitRules\"\r\n                   label-width=\"100px\" class=\"mobile-visit-form\">\r\n\r\n            <!-- 访问信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"card-title\">访问信息</span>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <el-form-item label=\"来访事由\" prop=\"visitInfo.reasonForVisit\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                            type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit\r\n                            class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"被访人\" prop=\"visitInfo.hostEmployeeName\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                            maxlength=\"20\" class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"被访部门\" prop=\"visitInfo.departmentVisited\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                            maxlength=\"50\" class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"车牌号\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.vehiclePlateNumber\" placeholder=\"如有车辆请填写（可选）\"\r\n                            class=\"mobile-input\" />\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 时间信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span class=\"card-title\">访问时间</span>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <el-form-item label=\"到访时间\" prop=\"visitInfo.plannedEntryDatetime\" class=\"mobile-form-item\">\r\n                  <el-date-picker\r\n                    v-model=\"formData.visitInfo.plannedEntryDatetime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"选择到访时间\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    :picker-options=\"entryPickerOptions\"\r\n                    @change=\"onArrivalTimeChange\"\r\n                    class=\"mobile-date-picker\"\r\n                  />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"离开时间\" prop=\"visitInfo.plannedExitDatetime\" class=\"mobile-form-item\">\r\n                  <el-date-picker\r\n                    v-model=\"formData.visitInfo.plannedExitDatetime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"选择离开时间\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    :picker-options=\"exitPickerOptions\"\r\n                    @change=\"onDepartureTimeChange\"\r\n                    class=\"mobile-date-picker\"\r\n                  />\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 访客信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span class=\"card-title\">访客信息（共{{ totalVisitors }}人）</span>\r\n                <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addVisitor\"\r\n                           :disabled=\"formData.visitors.length >= 10\" class=\"add-visitor-btn\">\r\n                  添加\r\n                </el-button>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <div v-for=\"(visitor, index) in formData.visitors\" :key=\"index\" class=\"mobile-visitor-item\">\r\n                  <div class=\"visitor-header\">\r\n                    <div class=\"visitor-badge\" :class=\"{ primary: index === 0 }\">\r\n                      {{ index === 0 ? '主联系人' : `访客${index + 1}` }}\r\n                    </div>\r\n                    <el-button v-if=\"index > 0\" size=\"mini\" type=\"danger\" icon=\"el-icon-delete\"\r\n                               @click=\"removeVisitor(index)\" circle class=\"remove-btn\"></el-button>\r\n                  </div>\r\n\r\n                  <div class=\"visitor-form\">\r\n                    <el-form-item label=\"姓名\" :prop=\"`visitors.${index}.name`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.name\" placeholder=\"请输入姓名\" maxlength=\"20\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"手机号\" :prop=\"`visitors.${index}.phone`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.phone\" placeholder=\"请输入手机号\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"身份证号\" :prop=\"`visitors.${index}.idCard`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.idCard\" placeholder=\"请输入身份证号\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"公司名称\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.company\" placeholder=\"请输入公司名称（可选）\"\r\n                                maxlength=\"100\" class=\"mobile-input\" />\r\n                    </el-form-item>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 登记成功页面 -->\r\n        <div class=\"success-content\" v-if=\"registrationCompleted\">\r\n          <div class=\"success-icon\">\r\n            <i class=\"el-icon-circle-check\"></i>\r\n          </div>\r\n\r\n          <h4>登记成功！</h4>\r\n          <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n\r\n          <!-- 登记信息摘要 -->\r\n          <div class=\"register-summary\">\r\n            <h5>登记信息摘要</h5>\r\n            <el-descriptions :column=\"2\" border>\r\n              <el-descriptions-item label=\"主联系人\">\r\n                {{ mainContact.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"联系电话\">\r\n                {{ mainContact.phone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访人\">\r\n                {{ formData.visitInfo.hostEmployeeName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访部门\">\r\n                {{ formData.visitInfo.departmentVisited }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                {{ formData.visitInfo.reasonForVisit }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计到访时间\">\r\n                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计离开时间\">\r\n                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"访客总数\">\r\n                {{ totalVisitors }} 人\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n\r\n          <!-- 访问凭证 -->\r\n          <!-- <div class=\"access-credential\" v-if=\"registrationId\">\r\n            <h5>访问凭证</h5>\r\n            <div class=\"credential-card\">\r\n              <div class=\"qr-code-container\">\r\n                <div class=\"qr-code\">\r\n                  <i class=\"el-icon-qrcode\"></i>\r\n                </div>\r\n                <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n              </div>\r\n              <div class=\"credential-info\">\r\n                <h6>使用说明</h6>\r\n                <ul>\r\n                  <li>请保存此二维码截图</li>\r\n                  <li>审核通过后可用于园区门禁</li>\r\n                  <li>凭证仅限当次访问使用</li>\r\n                  <li>如有疑问请联系园区前台</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div> -->\r\n        </div>\r\n\r\n        <!-- 移动端底部操作区 -->\r\n        <div class=\"mobile-actions\">\r\n          <!-- 表单提交按钮 -->\r\n          <div v-if=\"!registrationCompleted\" class=\"submit-actions\">\r\n            <el-button type=\"primary\" @click=\"submitForm\" size=\"large\" :loading=\"submitting\"\r\n                       class=\"mobile-submit-btn\">\r\n              <i class=\"el-icon-check\" v-if=\"!submitting\"></i>\r\n              <i class=\"el-icon-loading\" v-if=\"submitting\"></i>\r\n              {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}\r\n            </el-button>\r\n\r\n            <el-button @click=\"resetForm\" size=\"large\" class=\"mobile-reset-btn\">\r\n              <i class=\"el-icon-refresh-left\"></i>\r\n              重置表单\r\n            </el-button>\r\n          </div>\r\n\r\n          <!-- 成功后操作按钮 -->\r\n          <div v-if=\"registrationCompleted\" class=\"success-actions\">\r\n            <!-- <el-button type=\"primary\" @click=\"printCredential\" size=\"large\" class=\"mobile-action-btn\">\r\n              <i class=\"el-icon-printer\"></i>\r\n              打印凭证\r\n            </el-button> -->\r\n            <!-- <el-button @click=\"resetForm\" size=\"large\" class=\"mobile-action-btn\">\r\n              <i class=\"el-icon-refresh\"></i>\r\n              重新登记\r\n            </el-button> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 查询登记对话框 -->\r\n    <el-dialog\r\n      title=\"查询今日登记记录\"\r\n      :visible.sync=\"queryDialog.visible\"\r\n      width=\"90%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"query-dialog\"\r\n    >\r\n      <div class=\"query-container\">\r\n        <!-- 查询表单 -->\r\n        <div class=\"query-form-section\">\r\n          <h4><i class=\"el-icon-search\"></i> 请输入查询信息</h4>\r\n          <el-form ref=\"queryForm\" :model=\"queryForm\" :rules=\"queryRules\" label-width=\"100px\">\r\n            <el-form-item label=\"查询方式\" prop=\"queryType\">\r\n              <el-radio-group v-model=\"queryForm.queryType\" @change=\"handleQueryTypeChange\">\r\n                <el-radio label=\"idCard\">身份证号</el-radio>\r\n                <el-radio label=\"phone\">手机号</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"queryForm.queryType === 'idCard' ? '身份证号' : '手机号'\" prop=\"queryValue\">\r\n              <el-input\r\n                v-model=\"queryForm.queryValue\"\r\n                :placeholder=\"queryForm.queryType === 'idCard' ? '请输入身份证号' : '请输入手机号'\"\r\n                maxlength=\"18\"\r\n                clearable\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleQuery\" :loading=\"queryLoading\">\r\n                <i class=\"el-icon-search\"></i> 查询\r\n              </el-button>\r\n              <el-button @click=\"resetQuery\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 查询结果 -->\r\n        <div class=\"query-results-section\" v-if=\"queryResults.length > 0\">\r\n          <h4><i class=\"el-icon-document\"></i> 查询结果（共{{ queryResults.length }}条）</h4>\r\n          <div class=\"results-list\">\r\n            <div v-for=\"(record, index) in queryResults\" :key=\"index\" class=\"result-item\">\r\n              <div class=\"result-header\">\r\n                <div class=\"visitor-info\">\r\n                  <span class=\"visitor-name\">{{ record.primaryContactName }}</span>\r\n                  <span class=\"visitor-phone\">{{ record.primaryContactPhone }}</span>\r\n                </div>\r\n                <div class=\"status-badge\" :class=\"getStatusClass(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"result-details\">\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">来访事由：</span>\r\n                  <span class=\"value\">{{ record.reasonForVisit }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">被访人：</span>\r\n                  <span class=\"value\">{{ record.hostEmployeeName }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">被访部门：</span>\r\n                  <span class=\"value\">{{ record.departmentVisited }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">预计到访：</span>\r\n                  <span class=\"value\">{{ parseTime(record.plannedEntryDatetime) }}</span>\r\n                </div>\r\n                <div class=\"detail-row\">\r\n                  <span class=\"label\">预计离开：</span>\r\n                  <span class=\"value\">{{ parseTime(record.plannedExitDatetime) }}</span>\r\n                </div>\r\n                <div class=\"detail-row\" v-if=\"record.totalCompanions > 0\">\r\n                  <span class=\"label\">同行人数：</span>\r\n                  <span class=\"value\">{{ record.totalCompanions }}人</span>\r\n                </div>\r\n                <div class=\"detail-row\" v-if=\"record.submittedDatetime\">\r\n                  <span class=\"label\">提交时间：</span>\r\n                  <span class=\"value\">{{ parseTime(record.submittedDatetime) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 无结果提示 -->\r\n        <div class=\"no-results\" v-if=\"queryExecuted && queryResults.length === 0\">\r\n          <i class=\"el-icon-document-remove\"></i>\r\n          <h4>未找到登记记录</h4>\r\n          <p>今日暂无相关登记记录，请确认输入信息是否正确</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"queryDialog.visible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"printResults\" v-if=\"queryResults.length > 0\">\r\n          <i class=\"el-icon-printer\"></i> 打印凭证\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration, listRegistrations } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      submitting: false,\r\n      registrationCompleted: false,\r\n\r\n      // 查询对话框相关\r\n      queryDialog: {\r\n        visible: false\r\n      },\r\n      queryForm: {\r\n        queryType: 'idCard', // 查询类型：idCard 或 phone\r\n        queryValue: '' // 查询值\r\n      },\r\n      queryRules: {\r\n        queryType: [\r\n          { required: true, message: '请选择查询方式', trigger: 'change' }\r\n        ],\r\n        queryValue: [\r\n          { required: true, message: '请输入查询信息', trigger: 'blur' },\r\n          {\r\n            validator: (rule, value, callback) => {\r\n              if (!value) {\r\n                callback(new Error('请输入查询信息'));\r\n                return;\r\n              }\r\n\r\n              if (this.queryForm.queryType === 'idCard') {\r\n                // 身份证号验证\r\n                const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n                if (!idPattern.test(value)) {\r\n                  callback(new Error('请输入正确的身份证号'));\r\n                  return;\r\n                }\r\n              } else if (this.queryForm.queryType === 'phone') {\r\n                // 手机号验证\r\n                const phonePattern = /^1[3-9]\\d{9}$/;\r\n                if (!phonePattern.test(value)) {\r\n                  callback(new Error('请输入正确的手机号'));\r\n                  return;\r\n                }\r\n              }\r\n\r\n              callback();\r\n            },\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      queryLoading: false,\r\n      queryExecuted: false, // 是否已执行过查询\r\n      queryResults: [], // 查询结果\r\n\r\n      // 表单数据 - 与微信端保持一致的结构\r\n      formData: {\r\n        // 访客列表（第一位为主联系人）\r\n        visitors: [\r\n          {\r\n            name: '',\r\n            phone: '',\r\n            idCard: '',\r\n            company: '',\r\n            isMainContact: true\r\n          }\r\n        ],\r\n        // 来访信息\r\n        visitInfo: {\r\n          reasonForVisit: '',\r\n          hostEmployeeName: '',\r\n          departmentVisited: '',\r\n          vehiclePlateNumber: '',\r\n          plannedEntryDatetime: '',\r\n          plannedExitDatetime: ''\r\n        }\r\n      },\r\n\r\n      // 表单验证规则 - 与微信端保持一致\r\n      visitRules: {\r\n        'visitInfo.reasonForVisit': [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.hostEmployeeName': [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.departmentVisited': [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.plannedEntryDatetime': [\r\n          { required: true, message: '请选择预计来访时间', trigger: 'change' }\r\n        ],\r\n        'visitInfo.plannedExitDatetime': [\r\n          { required: true, message: '请选择预计离开时间', trigger: 'change' }\r\n        ],\r\n        // 动态访客验证规则\r\n        'visitors.0.name': [\r\n          { required: true, message: '请输入主联系人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.phone': [\r\n          { required: true, message: '请输入主联系人手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.idCard': [\r\n          { required: true, message: '请输入主联系人身份证号', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n\r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '中午12点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(12, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 总访客人数\r\n    totalVisitors() {\r\n      return this.formData.visitors.length;\r\n    },\r\n\r\n    // 主联系人信息\r\n    mainContact() {\r\n      return this.formData.visitors[0] || {};\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 添加访客\r\n    addVisitor() {\r\n      if (this.formData.visitors.length >= 10) {\r\n        this.$message.warning('最多只能添加10名访客');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.push({\r\n        name: '',\r\n        phone: '',\r\n        idCard: '',\r\n        company: '',\r\n        isMainContact: false\r\n      });\r\n\r\n      this.$message.success('已添加访客');\r\n    },\r\n\r\n    // 移除访客\r\n    removeVisitor(index) {\r\n      if (index === 0) {\r\n        this.$message.warning('不能删除主联系人');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.splice(index, 1);\r\n      this.$message.success('已移除访客');\r\n    },\r\n\r\n    // 格式化日期为后端期望的格式\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n\r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.formData.visitInfo.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n\r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n\r\n        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.formData.visitInfo.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.formData.visitInfo.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 提交表单 */\r\n    async submitForm() {\r\n      try {\r\n        // 验证表单\r\n        await this.$refs.visitForm.validate();\r\n\r\n        // 验证访客信息\r\n        if (!this.validateVisitors()) {\r\n          return;\r\n        }\r\n\r\n        // 验证时间\r\n        if (!this.validateTimes()) {\r\n          return;\r\n        }\r\n\r\n        this.submitting = true;\r\n\r\n        // 获取主联系人信息\r\n        const mainContact = this.formData.visitors[0];\r\n\r\n        const submitData = {\r\n          // VisitRegistrations 对象\r\n          registration: {\r\n            primaryContactName: mainContact.name.trim(),\r\n            primaryContactPhone: mainContact.phone.trim(),\r\n            reasonForVisit: this.formData.visitInfo.reasonForVisit,\r\n            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,\r\n            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n            departmentVisited: this.formData.visitInfo.departmentVisited,\r\n            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),\r\n            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),\r\n            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',\r\n            totalCompanions: this.formData.visitors.length - 1\r\n          },\r\n          // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n          attendeesList: this.formData.visitors.map((visitor, index) => ({\r\n            visitorName: visitor.name.trim(),\r\n            visitorPhone: visitor.phone.trim(),\r\n            visitorIdCard: visitor.idCard.trim().toUpperCase(),\r\n            visitorCompany: (visitor.company || '').trim(),\r\n            isPrimary: index === 0 ? \"1\" : \"0\", // 第一个访客必须是主联系人\r\n            visitorAvatarPhoto: null // 暂时不支持头像上传\r\n          }))\r\n        };\r\n\r\n        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n\r\n        // 调用API提交数据\r\n        const response = await submitVisitorRegistration(submitData);\r\n\r\n        if (response.code === 200) {\r\n          this.registrationId = response.data || 'VR' + Date.now();\r\n          this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n          this.registrationCompleted = true;\r\n        } else {\r\n          this.$message.error(response.msg || '登记失败，请重试');\r\n        }\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error);\r\n        this.$message.error('登记失败，请检查网络连接');\r\n      } finally {\r\n        this.submitting = false;\r\n      }\r\n    },\r\n\r\n    // 验证访客信息\r\n    validateVisitors() {\r\n      for (let i = 0; i < this.formData.visitors.length; i++) {\r\n        const visitor = this.formData.visitors[i];\r\n        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n\r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号格式（与后端保持一致）\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idCard || visitor.idCard.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n\r\n        const idCard = visitor.idCard.trim().toUpperCase();\r\n\r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idCard = visitor.idCard.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 验证时间\r\n    validateTimes() {\r\n      if (!this.formData.visitInfo.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.formData.visitInfo.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 检查来访时间不能早于当前时间1小时前\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (entryTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能早于当前时间1小时前');\r\n        return false;\r\n      }\r\n\r\n      // 检查离开时间必须晚于来访时间\r\n      if (exitTime <= entryTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      // 检查访问时长不能超过24小时\r\n      const visitDuration = exitTime.getTime() - entryTime.getTime();\r\n      const maxDuration = 24 * 60 * 60 * 1000; // 24小时\r\n      if (visitDuration > maxDuration) {\r\n        this.$message.error('单次访问时长不能超过24小时');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 重置所有数据\r\n        this.registrationCompleted = false;\r\n\r\n        this.formData = {\r\n          visitors: [\r\n            {\r\n              name: '',\r\n              phone: '',\r\n              idCard: '',\r\n              company: '',\r\n              isMainContact: true\r\n            }\r\n          ],\r\n          visitInfo: {\r\n            reasonForVisit: '',\r\n            hostEmployeeName: '',\r\n            departmentVisited: '',\r\n            vehiclePlateNumber: '',\r\n            plannedEntryDatetime: '',\r\n            plannedExitDatetime: ''\r\n          }\r\n        };\r\n\r\n        this.registrationId = null;\r\n\r\n        this.$message.success('表单已重置');\r\n      });\r\n    },\r\n\r\n    // 打印凭证\r\n    printCredential() {\r\n      this.$message.info('打印功能开发中...');\r\n    },\r\n\r\n    // 格式化时间显示\r\n    parseTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    },\r\n\r\n    // 显示帮助信息\r\n    showHelp() {\r\n      this.$alert(`\r\n        <div style=\"text-align: left;\">\r\n          <h4>使用说明</h4>\r\n          <p><strong>1. 填写基本信息</strong></p>\r\n          <ul>\r\n            <li>请如实填写来访事由、被访人等信息</li>\r\n            <li>主联系人信息必须准确，用于后续联系</li>\r\n          </ul>\r\n          <p><strong>2. 添加同行人员</strong></p>\r\n          <ul>\r\n            <li>如有同行人员，请点击\"添加访客\"按钮</li>\r\n            <li>每位访客都需要填写完整身份信息</li>\r\n          </ul>\r\n          <p><strong>3. 选择访问时间</strong></p>\r\n          <ul>\r\n            <li>请合理安排访问时间，避免过长逗留</li>\r\n            <li>如需延长访问时间，请联系被访人</li>\r\n          </ul>\r\n          <p><strong>4. 审核与通行</strong></p>\r\n          <ul>\r\n            <li>提交后等待审核，通过后可凭二维码进入园区</li>\r\n            <li>请按照预约时间准时到访</li>\r\n          </ul>\r\n        </div>\r\n      `, '使用帮助', {\r\n        confirmButtonText: '知道了',\r\n        dangerouslyUseHTMLString: true\r\n      });\r\n    },\r\n\r\n    // 联系客服\r\n    contactService() {\r\n      this.$confirm('需要帮助吗？', '联系客服', {\r\n        confirmButtonText: '拨打电话',\r\n        cancelButtonText: '取消',\r\n        type: 'info'\r\n      }).then(() => {\r\n        // 这里可以实现拨打客服电话的功能\r\n        this.$message({\r\n          type: 'info',\r\n          message: '客服电话：400-xxx-xxxx'\r\n        });\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    },\r\n\r\n    // 显示查询对话框\r\n    showQueryDialog() {\r\n      this.queryDialog.visible = true;\r\n      this.resetQuery();\r\n    },\r\n\r\n    // 查询类型变更\r\n    handleQueryTypeChange() {\r\n      this.queryForm.queryValue = '';\r\n      this.queryResults = [];\r\n      this.queryExecuted = false;\r\n      this.$refs.queryForm.clearValidate('queryValue');\r\n    },\r\n\r\n    // 执行查询\r\n    async handleQuery() {\r\n      try {\r\n        await this.$refs.queryForm.validate();\r\n\r\n        this.queryLoading = true;\r\n        this.queryExecuted = false;\r\n\r\n        // 构建查询参数\r\n        const today = new Date().toISOString().split('T')[0];\r\n        const queryParams = {\r\n          pageNum: 1,\r\n          pageSize: 50,\r\n          params: {\r\n            beginTime: today,\r\n            endTime: today\r\n          }\r\n        };\r\n\r\n        // 根据查询类型添加查询条件\r\n        if (this.queryForm.queryType === 'idCard') {\r\n          queryParams.idCardNumber = this.queryForm.queryValue.trim().toUpperCase();\r\n        } else if (this.queryForm.queryType === 'phone') {\r\n          queryParams.primaryContactPhone = this.queryForm.queryValue.trim();\r\n        }\r\n\r\n        console.log('查询参数:', queryParams);\r\n\r\n        // 调用查询接口\r\n        const response = await listRegistrations(queryParams);\r\n\r\n        if (response.code === 200) {\r\n          this.queryResults = response.rows || [];\r\n          this.queryExecuted = true;\r\n\r\n          if (this.queryResults.length > 0) {\r\n            this.$message.success(`找到 ${this.queryResults.length} 条登记记录`);\r\n          } else {\r\n            this.$message.info('未找到相关登记记录');\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg || '查询失败');\r\n          this.queryResults = [];\r\n          this.queryExecuted = true;\r\n        }\r\n      } catch (error) {\r\n        console.error('查询失败:', error);\r\n        this.$message.error('查询失败，请检查网络连接');\r\n        this.queryResults = [];\r\n        this.queryExecuted = true;\r\n      } finally {\r\n        this.queryLoading = false;\r\n      }\r\n    },\r\n\r\n    // 重置查询\r\n    resetQuery() {\r\n      this.queryForm = {\r\n        queryType: 'idCard',\r\n        queryValue: ''\r\n      };\r\n      this.queryResults = [];\r\n      this.queryExecuted = false;\r\n      this.$refs.queryForm && this.$refs.queryForm.resetFields();\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'pending': '待审核',\r\n        'approved': '已通过',\r\n        'rejected': '已拒绝',\r\n        'cancelled': '已取消',\r\n        'completed': '已完成'\r\n      };\r\n      return statusMap[status] || status;\r\n    },\r\n\r\n    // 获取状态样式类\r\n    getStatusClass(status) {\r\n      const classMap = {\r\n        'pending': 'status-pending',\r\n        'approved': 'status-approved',\r\n        'rejected': 'status-rejected',\r\n        'cancelled': 'status-cancelled',\r\n        'completed': 'status-completed'\r\n      };\r\n      return classMap[status] || 'status-default';\r\n    },\r\n\r\n    // 打印结果\r\n    printResults() {\r\n      this.$message.info('打印功能开发中...');\r\n      // 这里可以实现打印功能\r\n      // window.print();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.nav-actions .el-button {\r\n  border-radius: 20px;\r\n  padding: 8px 20px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 移动端布局优化 */\r\n.main-content.mobile-layout {\r\n  flex-direction: column;\r\n  padding: 20px;\r\n  gap: 20px;\r\n  max-width: 100%;\r\n}\r\n\r\n/* 移动端欢迎区域 */\r\n.mobile-welcome {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.welcome-header {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.welcome-header .welcome-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 15px;\r\n}\r\n\r\n.welcome-header .welcome-icon i {\r\n  font-size: 28px;\r\n  color: #fff;\r\n}\r\n\r\n.welcome-header h2 {\r\n  font-size: 24px;\r\n  color: #2c3e50;\r\n  margin: 0 0 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-header .welcome-desc {\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  margin-top: 20px;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #f0f0f0;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 移动端表单容器 */\r\n.mobile-form-container {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 移动端表单标题 */\r\n.mobile-form-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.mobile-form-header h3 {\r\n  margin: 0 0 8px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.mobile-form-header .form-subtitle {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 移动端表单内容 */\r\n.mobile-form-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 移动端表单卡片 */\r\n.mobile-form-card {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  overflow: hidden;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.mobile-form-card .card-header {\r\n  background: #fff;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.mobile-form-card .card-header i {\r\n  font-size: 16px;\r\n  color: #667eea;\r\n  margin-right: 8px;\r\n}\r\n\r\n.mobile-form-card .card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.mobile-form-card .add-visitor-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.mobile-form-card .card-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 移动端表单项 */\r\n.mobile-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.mobile-form-item .el-form-item__label {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  line-height: 1.4;\r\n  padding-bottom: 8px;\r\n}\r\n\r\n/* 移动端输入框 */\r\n.mobile-input .el-input__inner {\r\n  height: 44px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 0 15px;\r\n}\r\n\r\n.mobile-input .el-textarea__inner {\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 12px 15px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 移动端日期选择器 */\r\n.mobile-date-picker .el-input__inner {\r\n  height: 44px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 移动端访客项 */\r\n.mobile-visitor-item {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n  position: relative;\r\n}\r\n\r\n.mobile-visitor-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mobile-visitor-item .visitor-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.mobile-visitor-item .visitor-badge {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n  padding: 4px 12px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.mobile-visitor-item .visitor-badge.primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  border: none;\r\n}\r\n\r\n.mobile-visitor-item .remove-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n/* 移动端操作区域 */\r\n.mobile-actions {\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n.submit-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.success-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n/* 移动端按钮 */\r\n.mobile-submit-btn,\r\n.mobile-reset-btn,\r\n.mobile-action-btn {\r\n  width: 100%;\r\n  height: 48px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  border-radius: 8px;\r\n  border: none;\r\n}\r\n\r\n.mobile-submit-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n}\r\n\r\n.mobile-submit-btn:hover {\r\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\r\n}\r\n\r\n.mobile-reset-btn {\r\n  background: #fff;\r\n  color: #6c757d;\r\n  border: 1px solid #dcdfe6;\r\n}\r\n\r\n.mobile-reset-btn:hover {\r\n  background: #f8f9fa;\r\n  color: #495057;\r\n}\r\n\r\n.mobile-action-btn {\r\n  background: #fff;\r\n  color: #667eea;\r\n  border: 1px solid #667eea;\r\n}\r\n\r\n.mobile-action-btn:hover {\r\n  background: #667eea;\r\n  color: #fff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  /* 顶部导航移动端优化 */\r\n  .top-nav {\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .nav-content {\r\n    height: 60px;\r\n  }\r\n\r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-actions {\r\n    gap: 10px;\r\n  }\r\n\r\n  /* 主内容区域移动端优化 */\r\n  .main-content {\r\n    padding: 15px;\r\n    gap: 15px;\r\n  }\r\n\r\n  .main-content.mobile-layout {\r\n    padding: 10px;\r\n    gap: 15px;\r\n  }\r\n\r\n  /* 移动端欢迎区域优化 */\r\n  .mobile-welcome {\r\n    padding: 15px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .welcome-header .welcome-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .welcome-header .welcome-icon i {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .welcome-header h2 {\r\n    font-size: 20px;\r\n  }\r\n\r\n  /* 移动端表单优化 */\r\n  .mobile-form-container {\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .mobile-form-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-form-header h3 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .mobile-form-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-form-card {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .mobile-form-card .card-header {\r\n    padding: 12px 15px;\r\n  }\r\n\r\n  .mobile-form-card .card-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  /* 移动端访客项优化 */\r\n  .mobile-visitor-item {\r\n    padding: 12px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  /* 移动端操作区域优化 */\r\n  .mobile-actions {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-submit-btn,\r\n  .mobile-reset-btn,\r\n  .mobile-action-btn {\r\n    height: 44px;\r\n    font-size: 15px;\r\n  }\r\n}\r\n\r\n/* 超小屏幕优化 */\r\n@media (max-width: 480px) {\r\n  .top-nav {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .nav-content {\r\n    height: 55px;\r\n  }\r\n\r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .main-content.mobile-layout {\r\n    padding: 8px;\r\n  }\r\n\r\n  .mobile-welcome {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-form-header {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-form-content {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-actions {\r\n    padding: 12px;\r\n  }\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 查询对话框样式 */\r\n.query-dialog .el-dialog {\r\n  border-radius: 12px;\r\n}\r\n\r\n.query-dialog .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  padding: 20px 24px;\r\n  border-radius: 12px 12px 0 0;\r\n}\r\n\r\n.query-dialog .el-dialog__title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.query-dialog .el-dialog__headerbtn .el-dialog__close {\r\n  color: #fff;\r\n  font-size: 20px;\r\n}\r\n\r\n.query-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.query-container {\r\n  padding: 24px;\r\n}\r\n\r\n.query-form-section {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.query-form-section h4 {\r\n  margin: 0 0 20px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.query-form-section h4 i {\r\n  color: #667eea;\r\n}\r\n\r\n.query-results-section {\r\n  margin-top: 24px;\r\n}\r\n\r\n.query-results-section h4 {\r\n  margin: 0 0 16px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.query-results-section h4 i {\r\n  color: #667eea;\r\n}\r\n\r\n.results-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.result-item {\r\n  background: #fff;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.result-item:hover {\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.visitor-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.visitor-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.visitor-phone {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n}\r\n\r\n.status-badge {\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  min-width: 60px;\r\n}\r\n\r\n.status-pending {\r\n  background: #fff3cd;\r\n  color: #856404;\r\n  border: 1px solid #ffeaa7;\r\n}\r\n\r\n.status-approved {\r\n  background: #d4edda;\r\n  color: #155724;\r\n  border: 1px solid #c3e6cb;\r\n}\r\n\r\n.status-rejected {\r\n  background: #f8d7da;\r\n  color: #721c24;\r\n  border: 1px solid #f5c6cb;\r\n}\r\n\r\n.status-cancelled {\r\n  background: #e2e3e5;\r\n  color: #383d41;\r\n  border: 1px solid #d6d8db;\r\n}\r\n\r\n.status-completed {\r\n  background: #d1ecf1;\r\n  color: #0c5460;\r\n  border: 1px solid #bee5eb;\r\n}\r\n\r\n.status-default {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n  border: 1px solid #dee2e6;\r\n}\r\n\r\n.result-details {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.detail-row {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.detail-row .label {\r\n  font-weight: 600;\r\n  color: #495057;\r\n  min-width: 80px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.detail-row .value {\r\n  color: #6c757d;\r\n  flex: 1;\r\n  word-break: break-all;\r\n}\r\n\r\n.no-results {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.no-results i {\r\n  font-size: 64px;\r\n  color: #dee2e6;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.no-results h4 {\r\n  margin: 0 0 8px 0;\r\n  color: #495057;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.no-results p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.dialog-footer {\r\n  padding: 16px 24px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .query-dialog {\r\n    width: 95% !important;\r\n  }\r\n\r\n  .query-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .query-form-section {\r\n    padding: 16px;\r\n  }\r\n\r\n  .result-details {\r\n    grid-template-columns: 1fr;\r\n    gap: 8px;\r\n  }\r\n\r\n  .result-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n\r\n  .visitor-info {\r\n    width: 100%;\r\n  }\r\n\r\n  .status-badge {\r\n    align-self: flex-start;\r\n  }\r\n\r\n  .dialog-footer {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .nav-actions {\r\n    gap: 10px;\r\n  }\r\n\r\n  .nav-actions .el-button {\r\n    padding: 6px 16px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .nav-actions .el-button {\r\n    padding: 4px 12px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .visitor-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .detail-row {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .detail-row .label {\r\n    min-width: auto;\r\n  }\r\n}\r\n</style>\r\n"]}]}