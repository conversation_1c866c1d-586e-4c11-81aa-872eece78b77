{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=style&index=0&id=31326bdb&scoped=true&lang=css", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750989656917}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750836933411}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750836980592}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750836940519}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDkuLvlrrnlmaggKi8NCi5wYy1yZWdpc3Rlci1jb250YWluZXIgew0KICBtaW4taGVpZ2h0OiAxMDB2aDsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y1ZjdmYSAwJSwgI2MzY2ZlMiAxMDAlKTsNCiAgZm9udC1mYW1pbHk6ICdIZWx2ZXRpY2EgTmV1ZScsIEFyaWFsLCBzYW5zLXNlcmlmOw0KfQ0KDQovKiDpobbpg6jlr7zoiKogKi8NCi50b3AtbmF2IHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgcGFkZGluZzogMCA0MHB4Ow0KICBwb3NpdGlvbjogc3RpY2t5Ow0KICB0b3A6IDA7DQogIHotaW5kZXg6IDEwMDsNCn0NCg0KLm5hdi1jb250ZW50IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBoZWlnaHQ6IDcwcHg7DQogIG1heC13aWR0aDogMTQwMHB4Ow0KICBtYXJnaW46IDAgYXV0bzsNCn0NCg0KLmxvZ28tc2VjdGlvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTVweDsNCn0NCg0KLm5hdi1sb2dvIHsNCiAgaGVpZ2h0OiA0MHB4Ow0KfQ0KDQouY29tcGFueS1uYW1lIHsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCn0NCg0KLm5hdi1hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAyMHB4Ow0KfQ0KDQovKiDkuLvlhoXlrrnljLrln58gKi8NCi5tYWluLWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBtYXgtd2lkdGg6IDE0MDBweDsNCiAgbWFyZ2luOiAwIGF1dG87DQogIHBhZGRpbmc6IDQwcHg7DQogIGdhcDogNDBweDsNCiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDExMHB4KTsNCn0NCg0KLyog56e75Yqo56uv5biD5bGA5LyY5YyWICovDQoubWFpbi1jb250ZW50Lm1vYmlsZS1sYXlvdXQgew0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBnYXA6IDIwcHg7DQogIG1heC13aWR0aDogMTAwJTsNCn0NCg0KLyog56e75Yqo56uv5qyi6L+O5Yy65Z+fICovDQoubW9iaWxlLXdlbGNvbWUgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLndlbGNvbWUtaGVhZGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoud2VsY29tZS1oZWFkZXIgLndlbGNvbWUtaWNvbiB7DQogIHdpZHRoOiA2MHB4Ow0KICBoZWlnaHQ6IDYwcHg7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIG1hcmdpbjogMCBhdXRvIDE1cHg7DQp9DQoNCi53ZWxjb21lLWhlYWRlciAud2VsY29tZS1pY29uIGkgew0KICBmb250LXNpemU6IDI4cHg7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQoud2VsY29tZS1oZWFkZXIgaDIgew0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBtYXJnaW46IDAgMCAxMHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQoud2VsY29tZS1oZWFkZXIgLndlbGNvbWUtZGVzYyB7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbjogMDsNCn0NCg0KLyog6L+b5bqm5oyH56S65ZmoICovDQoucHJvZ3Jlc3MtaW5kaWNhdG9yIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCn0NCg0KLnByb2dyZXNzLWJhciB7DQogIGhlaWdodDogNnB4Ow0KICBiYWNrZ3JvdW5kOiAjZjBmMGYwOw0KICBib3JkZXItcmFkaXVzOiAzcHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5wcm9ncmVzcy1maWxsIHsNCiAgaGVpZ2h0OiAxMDAlOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlOw0KfQ0KDQoucHJvZ3Jlc3MtdGV4dCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzdmOGM4ZDsNCn0NCg0KLyog56e75Yqo56uv6KGo5Y2V5a655ZmoICovDQoubW9iaWxlLWZvcm0tY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi8qIOenu+WKqOerr+ihqOWNleagh+mimCAqLw0KLm1vYmlsZS1mb3JtLWhlYWRlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGNvbG9yOiAjZmZmOw0KICBwYWRkaW5nOiAyMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5tb2JpbGUtZm9ybS1oZWFkZXIgaDMgew0KICBtYXJnaW46IDAgMCA4cHg7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLm1vYmlsZS1mb3JtLWhlYWRlciAuZm9ybS1zdWJ0aXRsZSB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBvcGFjaXR5OiAwLjk7DQp9DQoNCi8qIOenu+WKqOerr+ihqOWNleWGheWuuSAqLw0KLm1vYmlsZS1mb3JtLWNvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQovKiDnp7vliqjnq6/ooajljZXljaHniYcgKi8NCi5tb2JpbGUtZm9ybS1jYXJkIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoubW9iaWxlLWZvcm0tY2FyZCAuY2FyZC1oZWFkZXIgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQp9DQoNCi5tb2JpbGUtZm9ybS1jYXJkIC5jYXJkLWhlYWRlciBpIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBjb2xvcjogIzY2N2VlYTsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5tb2JpbGUtZm9ybS1jYXJkIC5jYXJkLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgZmxleDogMTsNCn0NCg0KLm1vYmlsZS1mb3JtLWNhcmQgLmFkZC12aXNpdG9yLWJ0biB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KfQ0KDQoubW9iaWxlLWZvcm0tY2FyZCAuY2FyZC1jb250ZW50IHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLyog56e75Yqo56uv6KGo5Y2V6aG5ICovDQoubW9iaWxlLWZvcm0taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5tb2JpbGUtZm9ybS1pdGVtIC5lbC1mb3JtLWl0ZW1fX2xhYmVsIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCiAgcGFkZGluZy1ib3R0b206IDhweDsNCn0NCg0KLyog56e75Yqo56uv6L6T5YWl5qGGICovDQoubW9iaWxlLWlucHV0IC5lbC1pbnB1dF9faW5uZXIgew0KICBoZWlnaHQ6IDQ0cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICBwYWRkaW5nOiAwIDE1cHg7DQp9DQoNCi5tb2JpbGUtaW5wdXQgLmVsLXRleHRhcmVhX19pbm5lciB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICBwYWRkaW5nOiAxMnB4IDE1cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQp9DQoNCi8qIOenu+WKqOerr+aXpeacn+mAieaLqeWZqCAqLw0KLm1vYmlsZS1kYXRlLXBpY2tlciAuZWwtaW5wdXRfX2lubmVyIHsNCiAgaGVpZ2h0OiA0NHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLyog56e75Yqo56uv6K6/5a6i6aG5ICovDQoubW9iaWxlLXZpc2l0b3ItaXRlbSB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcGFkZGluZzogMTVweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KDQoubW9iaWxlLXZpc2l0b3ItaXRlbTpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLm1vYmlsZS12aXNpdG9yLWl0ZW0gLnZpc2l0b3ItaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQoubW9iaWxlLXZpc2l0b3ItaXRlbSAudmlzaXRvci1iYWRnZSB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGNvbG9yOiAjNmM3NTdkOw0KICBwYWRkaW5nOiA0cHggMTJweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoubW9iaWxlLXZpc2l0b3ItaXRlbSAudmlzaXRvci1iYWRnZS5wcmltYXJ5IHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgY29sb3I6ICNmZmY7DQogIGJvcmRlcjogbm9uZTsNCn0NCg0KLm1vYmlsZS12aXNpdG9yLWl0ZW0gLnJlbW92ZS1idG4gew0KICB3aWR0aDogMjhweDsNCiAgaGVpZ2h0OiAyOHB4Ow0KICBwYWRkaW5nOiAwOw0KfQ0KDQovKiDnp7vliqjnq6/mk43kvZzljLrln58gKi8NCi5tb2JpbGUtYWN0aW9ucyB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQouc3VibWl0LWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDEycHg7DQp9DQoNCi5zdWNjZXNzLWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDEycHg7DQp9DQoNCi8qIOenu+WKqOerr+aMiemSriAqLw0KLm1vYmlsZS1zdWJtaXQtYnRuLA0KLm1vYmlsZS1yZXNldC1idG4sDQoubW9iaWxlLWFjdGlvbi1idG4gew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiA0OHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiBub25lOw0KfQ0KDQoubW9iaWxlLXN1Ym1pdC1idG4gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBjb2xvcjogI2ZmZjsNCn0NCg0KLm1vYmlsZS1zdWJtaXQtYnRuOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzVhNmZkOCAwJSwgIzZhNDE5MCAxMDAlKTsNCn0NCg0KLm1vYmlsZS1yZXNldC1idG4gew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBjb2xvcjogIzZjNzU3ZDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2RjZGZlNjsNCn0NCg0KLm1vYmlsZS1yZXNldC1idG46aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBjb2xvcjogIzQ5NTA1NzsNCn0NCg0KLm1vYmlsZS1hY3Rpb24tYnRuIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgY29sb3I6ICM2NjdlZWE7DQogIGJvcmRlcjogMXB4IHNvbGlkICM2NjdlZWE7DQp9DQoNCi5tb2JpbGUtYWN0aW9uLWJ0bjpob3ZlciB7DQogIGJhY2tncm91bmQ6ICM2NjdlZWE7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAvKiDpobbpg6jlr7zoiKrnp7vliqjnq6/kvJjljJYgKi8NCiAgLnRvcC1uYXYgew0KICAgIHBhZGRpbmc6IDAgMjBweDsNCiAgfQ0KDQogIC5uYXYtY29udGVudCB7DQogICAgaGVpZ2h0OiA2MHB4Ow0KICB9DQoNCiAgLmNvbXBhbnktbmFtZSB7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICB9DQoNCiAgLm5hdi1hY3Rpb25zIHsNCiAgICBnYXA6IDEwcHg7DQogIH0NCg0KICAvKiDkuLvlhoXlrrnljLrln5/np7vliqjnq6/kvJjljJYgKi8NCiAgLm1haW4tY29udGVudCB7DQogICAgcGFkZGluZzogMTVweDsNCiAgICBnYXA6IDE1cHg7DQogIH0NCg0KICAubWFpbi1jb250ZW50Lm1vYmlsZS1sYXlvdXQgew0KICAgIHBhZGRpbmc6IDEwcHg7DQogICAgZ2FwOiAxNXB4Ow0KICB9DQoNCiAgLyog56e75Yqo56uv5qyi6L+O5Yy65Z+f5LyY5YyWICovDQogIC5tb2JpbGUtd2VsY29tZSB7DQogICAgcGFkZGluZzogMTVweDsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICB9DQoNCiAgLndlbGNvbWUtaGVhZGVyIC53ZWxjb21lLWljb24gew0KICAgIHdpZHRoOiA1MHB4Ow0KICAgIGhlaWdodDogNTBweDsNCiAgfQ0KDQogIC53ZWxjb21lLWhlYWRlciAud2VsY29tZS1pY29uIGkgew0KICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgfQ0KDQogIC53ZWxjb21lLWhlYWRlciBoMiB7DQogICAgZm9udC1zaXplOiAyMHB4Ow0KICB9DQoNCiAgLyog56e75Yqo56uv6KGo5Y2V5LyY5YyWICovDQogIC5tb2JpbGUtZm9ybS1jb250YWluZXIgew0KICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1oZWFkZXIgew0KICAgIHBhZGRpbmc6IDE1cHg7DQogIH0NCg0KICAubW9iaWxlLWZvcm0taGVhZGVyIGgzIHsNCiAgICBmb250LXNpemU6IDE2cHg7DQogIH0NCg0KICAubW9iaWxlLWZvcm0tY29udGVudCB7DQogICAgcGFkZGluZzogMTVweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1jYXJkIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICB9DQoNCiAgLm1vYmlsZS1mb3JtLWNhcmQgLmNhcmQtaGVhZGVyIHsNCiAgICBwYWRkaW5nOiAxMnB4IDE1cHg7DQogIH0NCg0KICAubW9iaWxlLWZvcm0tY2FyZCAuY2FyZC1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAxNXB4Ow0KICB9DQoNCiAgLyog56e75Yqo56uv6K6/5a6i6aG55LyY5YyWICovDQogIC5tb2JpbGUtdmlzaXRvci1pdGVtIHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogIH0NCg0KICAvKiDnp7vliqjnq6/mk43kvZzljLrln5/kvJjljJYgKi8NCiAgLm1vYmlsZS1hY3Rpb25zIHsNCiAgICBwYWRkaW5nOiAxNXB4Ow0KICB9DQoNCiAgLm1vYmlsZS1zdWJtaXQtYnRuLA0KICAubW9iaWxlLXJlc2V0LWJ0biwNCiAgLm1vYmlsZS1hY3Rpb24tYnRuIHsNCiAgICBoZWlnaHQ6IDQ0cHg7DQogICAgZm9udC1zaXplOiAxNXB4Ow0KICB9DQp9DQoNCi8qIOi2heWwj+Wxj+W5leS8mOWMliAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7DQogIC50b3AtbmF2IHsNCiAgICBwYWRkaW5nOiAwIDE1cHg7DQogIH0NCg0KICAubmF2LWNvbnRlbnQgew0KICAgIGhlaWdodDogNTVweDsNCiAgfQ0KDQogIC5jb21wYW55LW5hbWUgew0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgfQ0KDQogIC5tYWluLWNvbnRlbnQubW9iaWxlLWxheW91dCB7DQogICAgcGFkZGluZzogOHB4Ow0KICB9DQoNCiAgLm1vYmlsZS13ZWxjb21lIHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICB9DQoNCiAgLm1vYmlsZS1mb3JtLWhlYWRlciB7DQogICAgcGFkZGluZzogMTJweDsNCiAgfQ0KDQogIC5tb2JpbGUtZm9ybS1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICB9DQoNCiAgLm1vYmlsZS1hY3Rpb25zIHsNCiAgICBwYWRkaW5nOiAxMnB4Ow0KICB9DQp9DQoNCi8qIOW3puS+p+S/oeaBr+mdouadvyAqLw0KLmluZm8tcGFuZWwgew0KICBmbGV4OiAwIDAgNDAwcHg7DQp9DQoNCi53ZWxjb21lLWNhcmQgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICBwYWRkaW5nOiAzMnB4Ow0KICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgaGVpZ2h0OiBmaXQtY29udGVudDsNCiAgcG9zaXRpb246IHN0aWNreTsNCiAgdG9wOiAxMTBweDsNCn0NCg0KLndlbGNvbWUtaWNvbiB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLndlbGNvbWUtaWNvbiBpIHsNCiAgZm9udC1zaXplOiA2NHB4Ow0KICBjb2xvcjogIzM0OThkYjsNCn0NCg0KLndlbGNvbWUtY2FyZCBoMiB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICMyYzNlNTA7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIGZvbnQtc2l6ZTogMjhweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLndlbGNvbWUtZGVzYyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgbGluZS1oZWlnaHQ6IDEuNjsNCn0NCg0KLyog5rWB56iL5q2l6aqkICovDQoucHJvY2Vzcy1zdGVwcyB7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi5zdGVwLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDE2cHg7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQogIHBhZGRpbmc6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQp9DQoNCi5zdGVwLWl0ZW0uYWN0aXZlIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM0OThkYiwgIzI5ODBiOSk7DQogIGNvbG9yOiB3aGl0ZTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDhweCk7DQp9DQoNCi5zdGVwLWl0ZW06bm90KC5hY3RpdmUpIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgY29sb3I6ICM2Yzc1N2Q7DQp9DQoNCi5zdGVwLWNpcmNsZSB7DQogIHdpZHRoOiAzMnB4Ow0KICBoZWlnaHQ6IDMycHg7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE0cHg7DQogIGZsZXgtc2hyaW5rOiAwOw0KfQ0KDQouc3RlcC1pdGVtLmFjdGl2ZSAuc3RlcC1jaXJjbGUgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLnN0ZXAtaXRlbTpub3QoLmFjdGl2ZSkgLnN0ZXAtY2lyY2xlIHsNCiAgYmFja2dyb3VuZDogI2RlZTJlNjsNCiAgY29sb3I6ICM2Yzc1N2Q7DQp9DQoNCi5zdGVwLXRleHQgaDQgew0KICBtYXJnaW46IDAgMCA0cHggMDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQouc3RlcC10ZXh0IHAgew0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgb3BhY2l0eTogMC44Ow0KfQ0KDQovKiDlronlhajmj5DnpLogKi8NCi5zZWN1cml0eS10aXBzIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMzQ5OGRiOw0KfQ0KDQouc2VjdXJpdHktdGlwcyBoNCB7DQogIG1hcmdpbjogMCAwIDEycHggMDsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5zZWN1cml0eS10aXBzIHVsIHsNCiAgbWFyZ2luOiAwOw0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQogIGNvbG9yOiAjNmM3NTdkOw0KfQ0KDQouc2VjdXJpdHktdGlwcyBsaSB7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS40Ow0KfQ0KDQovKiDlj7PkvqfooajljZXpnaLmnb8gKi8NCi5mb3JtLXBhbmVsIHsNCiAgZmxleDogMTsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTZweDsNCiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi8qIOi/m+W6puaMh+ekuuWZqCAqLw0KLnByb2dyZXNzLWluZGljYXRvciB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDI0cHggMzJweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5wcm9ncmVzcy1iYXIgew0KICBoZWlnaHQ6IDZweDsNCiAgYmFja2dyb3VuZDogI2U5ZWNlZjsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5wcm9ncmVzcy1maWxsIHsNCiAgaGVpZ2h0OiAxMDAlOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNDk4ZGIsICMyOTgwYjkpOw0KICBib3JkZXItcmFkaXVzOiAzcHg7DQogIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTsNCn0NCg0KLnByb2dyZXNzLXRleHQgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjNmM3NTdkOw0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi8qIOihqOWNleWGheWuuSAqLw0KLmZvcm0tY29udGVudCB7DQogIHBhZGRpbmc6IDMycHg7DQogIG1heC1oZWlnaHQ6IGNhbGMoMTAwdmggLSAzMDBweCk7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5mb3JtLWhlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCn0NCg0KLmZvcm0taGVhZGVyIGgzIHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouZm9ybS1oZWFkZXIgcCB7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLyog6aqM6K+B5pa55byP572R5qC8ICovDQoudmVyaWZ5LWdyaWQgew0KICBkaXNwbGF5OiBncmlkOw0KICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpOw0KICBnYXA6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi52ZXJpZnktY2FyZCB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMjRweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQp9DQoNCi52ZXJpZnktY2FyZDpob3ZlciB7DQogIGJvcmRlci1jb2xvcjogIzM0OThkYjsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm94LXNoYWRvdzogMCA0cHggMTZweCByZ2JhKDUyLCAxNTIsIDIxOSwgMC4xNSk7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCn0NCg0KLnZlcmlmeS1jYXJkLnNlbGVjdGVkIHsNCiAgYm9yZGVyLWNvbG9yOiAjMzQ5OGRiOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzQ5OGRiLCAjMjk4MGI5KTsNCiAgY29sb3I6IHdoaXRlOw0KICBib3gtc2hhZG93OiAwIDhweCAyNHB4IHJnYmEoNTIsIDE1MiwgMjE5LCAwLjMpOw0KfQ0KDQoudmVyaWZ5LWljb24gew0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KfQ0KDQoudmVyaWZ5LWljb24gaSB7DQogIGZvbnQtc2l6ZTogNDhweDsNCiAgY29sb3I6ICMzNDk4ZGI7DQp9DQoNCi52ZXJpZnktY2FyZC5zZWxlY3RlZCAudmVyaWZ5LWljb24gaSB7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLnZlcmlmeS1jYXJkIGg0IHsNCiAgbWFyZ2luOiAwIDAgOHB4IDA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLnZlcmlmeS1jYXJkIHAgew0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgb3BhY2l0eTogMC44Ow0KfQ0KDQoudmVyaWZ5LXN0YXR1cyB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiAxMnB4Ow0KICByaWdodDogMTJweDsNCiAgd2lkdGg6IDI0cHg7DQogIGhlaWdodDogMjRweDsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOw0KICBib3JkZXItcmFkaXVzOiA1MCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQovKiDpqozor4Hmk43kvZzljLrln58gKi8NCi52ZXJpZnktb3BlcmF0aW9uIHsNCiAgbWFyZ2luLXRvcDogMzJweDsNCiAgcGFkZGluZzogMjRweDsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCn0NCg0KLm9wZXJhdGlvbi1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5vcGVyYXRpb24taGVhZGVyIGg0IHsNCiAgbWFyZ2luOiAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQovKiDouqvku73ooajljZUgKi8NCi5pZGVudGl0eS1mb3JtIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQp9DQoNCi8qIOi6q+S7veivgeivu+WNoeWZqCAqLw0KLmlkLWNhcmQtcmVhZGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQoucmVhZGVyLXZpc3VhbCB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBwYWRkaW5nOiA0MHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KfQ0KDQoucmVhZGVyLWFuaW1hdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5yb3RhdGluZyB7DQogIGFuaW1hdGlvbjogcm90YXRlIDJzIGxpbmVhciBpbmZpbml0ZTsNCn0NCg0KQGtleWZyYW1lcyByb3RhdGUgew0KICBmcm9tIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH0NCiAgdG8geyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9DQp9DQoNCi5yZWFkZXItYW5pbWF0aW9uIGkgew0KICBmb250LXNpemU6IDY0cHg7DQogIGNvbG9yOiAjMzQ5OGRiOw0KfQ0KDQoucmVhZGVyLXZpc3VhbCBoNCB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5yZWFkZXItdmlzdWFsIHAgew0KICBjb2xvcjogIzdmOGM4ZDsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnJlYWRlci10aXBzIHsNCiAgbWFyZ2luOiAyNHB4IDA7DQp9DQoNCi8qIOS6uuiEuOivhuWIqyAqLw0KLmZhY2UtcmVjb2duaXRpb24gew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5jYW1lcmEtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDQwcHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQp9DQoNCi5jYW1lcmEtZnJhbWUgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHdpZHRoOiAyODBweDsNCiAgaGVpZ2h0OiAyMTBweDsNCiAgbWFyZ2luOiAwIGF1dG8gMjRweDsNCiAgYm9yZGVyOiAzcHggc29saWQgIzM0OThkYjsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5jYW1lcmEtb3ZlcmxheSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTZweDsNCn0NCg0KLmZhY2Utb3V0bGluZSB7DQogIHdpZHRoOiAxMjBweDsNCiAgaGVpZ2h0OiAxNTBweDsNCiAgYm9yZGVyOiAycHggZGFzaGVkICMzNDk4ZGI7DQogIGJvcmRlci1yYWRpdXM6IDYwcHg7DQogIG9wYWNpdHk6IDAuNjsNCn0NCg0KLmNhbWVyYS1pY29uIHsNCiAgZm9udC1zaXplOiAzMnB4Ow0KICBjb2xvcjogIzM0OThkYjsNCn0NCg0KLnNjYW5uaW5nLWxpbmUgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMDsNCiAgbGVmdDogMDsNCiAgcmlnaHQ6IDA7DQogIGhlaWdodDogMnB4Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50LCAjMzQ5OGRiLCB0cmFuc3BhcmVudCk7DQogIGFuaW1hdGlvbjogc2NhbiAycyBlYXNlLWluLW91dCBpbmZpbml0ZTsNCn0NCg0KQGtleWZyYW1lcyBzY2FuIHsNCiAgMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7IH0NCiAgNTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDIwNnB4KTsgfQ0KICAxMDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOyB9DQp9DQoNCi8qIOihqOWNleWIhue7hCAqLw0KLmZvcm0tc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi5zZWN0aW9uLXRpdGxlIHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgcGFkZGluZy1ib3R0b206IDhweDsNCiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5zZWN0aW9uLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCn0NCg0KLyog5ZCM6KGM5Lq65ZGYICovDQoubm8tY29tcGFuaW9ucyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogNDBweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi5uby1jb21wYW5pb25zIGkgew0KICBmb250LXNpemU6IDQ4cHg7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIGNvbG9yOiAjYmRjM2M3Ow0KfQ0KDQouY29tcGFuaW9ucy10YWJsZSB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLyog5oiQ5Yqf6aG16Z2iICovDQouc3VjY2Vzcy1jb250ZW50IHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouc3VjY2Vzcy1pY29uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnN1Y2Nlc3MtaWNvbiBpIHsNCiAgZm9udC1zaXplOiA4MHB4Ow0KICBjb2xvcjogIzI3YWU2MDsNCn0NCg0KLnN1Y2Nlc3MtY29udGVudCBoNCB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDI4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQp9DQoNCi5zdWNjZXNzLW1lc3NhZ2Ugew0KICBjb2xvcjogIzdmOGM4ZDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQoucmVnaXN0ZXItc3VtbWFyeSB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQogIHRleHQtYWxpZ246IGxlZnQ7DQp9DQoNCi5yZWdpc3Rlci1zdW1tYXJ5IGg1IHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouYWNjZXNzLWNyZWRlbnRpYWwgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KfQ0KDQouYWNjZXNzLWNyZWRlbnRpYWwgaDUgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5jcmVkZW50aWFsLWNhcmQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDI0cHg7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoucXItY29kZS1jb250YWluZXIgew0KICBmbGV4OiAwIDAgMTIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnFyLWNvZGUgew0KICB3aWR0aDogMTIwcHg7DQogIGhlaWdodDogMTIwcHg7DQogIGJvcmRlcjogMnB4IHNvbGlkICNlOWVjZWY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLnFyLWNvZGUgaSB7DQogIGZvbnQtc2l6ZTogNDhweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi5xci1jb2RlLWlkIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzZjNzU3ZDsNCiAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTsNCiAgbWFyZ2luOiAwOw0KfQ0KDQouY3JlZGVudGlhbC1pbmZvIHsNCiAgZmxleDogMTsNCn0NCg0KLmNyZWRlbnRpYWwtaW5mbyBoNiB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQp9DQoNCi5jcmVkZW50aWFsLWluZm8gdWwgew0KICBtYXJnaW46IDA7DQogIHBhZGRpbmctbGVmdDogMjBweDsNCiAgY29sb3I6ICM2Yzc1N2Q7DQp9DQoNCi5jcmVkZW50aWFsLWluZm8gbGkgew0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCn0NCg0KLyog5bqV6YOo5pON5L2c5oyJ6ZKuICovDQouZm9ybS1hY3Rpb25zIHsNCiAgcGFkZGluZzogMjRweCAzMnB4Ow0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2U5ZWNlZjsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouZmluYWwtYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTZweDsNCn0NCg0KLyog5ZON5bqU5byP6K6+6K6hICovDQpAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7DQogIC5tYWluLWNvbnRlbnQgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAyNHB4Ow0KICB9DQogIA0KICAuaW5mby1wYW5lbCB7DQogICAgZmxleDogbm9uZTsNCiAgfQ0KICANCiAgLndlbGNvbWUtY2FyZCB7DQogICAgcG9zaXRpb246IHN0YXRpYzsNCiAgfQ0KICANCiAgLnZlcmlmeS1ncmlkIHsNCiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsNCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLm1haW4tY29udGVudCB7DQogICAgcGFkZGluZzogMjBweDsNCiAgfQ0KICANCiAgLm5hdi1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAwIDIwcHg7DQogIH0NCiAgDQogIC5jb21wYW55LW5hbWUgew0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgfQ0KICANCiAgLmZvcm0tY29udGVudCB7DQogICAgcGFkZGluZzogMjBweDsNCiAgfQ0KICANCiAgLmNyZWRlbnRpYWwtY2FyZCB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIH0NCiAgDQogIC5mb3JtLWFjdGlvbnMgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAxNnB4Ow0KICB9DQp9DQoNCi8qIEVsZW1lbnQgVUkg5qC35byP6KaG55uWICovDQouZWwtZm9ybS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLmVsLWlucHV0X19pbm5lciB7DQogIGhlaWdodDogNDRweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQouZWwtaW5wdXRfX2lubmVyOmZvY3VzIHsNCiAgYm9yZGVyLWNvbG9yOiAjMzQ5OGRiOw0KICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSg1MiwgMTUyLCAyMTksIDAuMSk7DQp9DQoNCi5lbC1zZWxlY3Qgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLmVsLWRhdGUtZWRpdG9yIHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5lbC1idXR0b24tLWxhcmdlIHsNCiAgaGVpZ2h0OiA0NHB4Ow0KICBwYWRkaW5nOiAxMnB4IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouZWwtYnV0dG9uLS1wcmltYXJ5IHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM0OThkYiwgIzI5ODBiOSk7DQogIGJvcmRlcjogbm9uZTsNCn0NCg0KLmVsLWJ1dHRvbi0tcHJpbWFyeTpob3ZlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyOTgwYjksICMxZjRlNzkpOw0KfQ0KDQouZWwtZGVzY3JpcHRpb25zIHsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQouZWwtdGFibGUgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi5lbC1hbGVydCB7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg=="}, {"version": 3, "sources": ["self-register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsxBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "self-register.vue", "sourceRoot": "src/views/asc/visitor", "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <!-- <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" /> -->\r\n          <span class=\"company-name\">高义钢铁访客登记系统</span>\r\n        </div>\r\n        <!-- <div class=\"nav-actions\">\r\n          <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button>\r\n        </div> -->\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 - 移动端优化 -->\r\n    <div class=\"main-content mobile-layout\">\r\n      <!-- 移动端欢迎区域 -->\r\n      <!-- <div class=\"mobile-welcome\" v-if=\"!registrationCompleted\"> -->\r\n        <!-- <div class=\"welcome-header\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>访客登记</h2>\r\n          <p class=\"welcome-desc\">请填写以下信息完成登记</p>\r\n        </div> -->\r\n\r\n        <!-- 简化的进度指示 -->\r\n        <!-- <div class=\"progress-indicator\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: registrationCompleted ? '100%' : '60%' }\"></div>\r\n          </div>\r\n          <div class=\"progress-text\">\r\n            {{ registrationCompleted ? '登记完成' : '正在填写信息...' }}\r\n          </div>\r\n        </div> -->\r\n      <!-- </div> -->\r\n\r\n      <!-- 表单容器 - 全屏移动端布局 -->\r\n      <div class=\"mobile-form-container\">\r\n        <!-- 表单标题 -->\r\n        <div class=\"mobile-form-header\" v-if=\"!registrationCompleted\">\r\n          <h3><i class=\"el-icon-edit-outline\"></i> 填写登记信息</h3>\r\n          <p class=\"form-subtitle\">共{{ totalVisitors }}人，请确保信息准确</p>\r\n        </div>\r\n\r\n        <!-- 成功页面标题 -->\r\n        <div class=\"mobile-form-header\" v-else>\r\n          <h3><i class=\"el-icon-circle-check\"></i> 登记成功</h3>\r\n          <p class=\"form-subtitle\">您的访客登记已提交成功</p>\r\n        </div>\r\n\r\n        <!-- 移动端表单内容 -->\r\n        <div class=\"mobile-form-content\" v-if=\"!registrationCompleted\">\r\n          <el-form ref=\"visitForm\" :model=\"formData\" :rules=\"visitRules\"\r\n                   label-width=\"100px\" class=\"mobile-visit-form\">\r\n\r\n            <!-- 访问信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"card-title\">访问信息</span>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <el-form-item label=\"来访事由\" prop=\"visitInfo.reasonForVisit\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                            type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit\r\n                            class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"被访人\" prop=\"visitInfo.hostEmployeeName\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                            maxlength=\"20\" class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"被访部门\" prop=\"visitInfo.departmentVisited\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                            maxlength=\"50\" class=\"mobile-input\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"车牌号\" class=\"mobile-form-item\">\r\n                  <el-input v-model=\"formData.visitInfo.vehiclePlateNumber\" placeholder=\"如有车辆请填写（可选）\"\r\n                            class=\"mobile-input\" />\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 时间信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span class=\"card-title\">访问时间</span>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <el-form-item label=\"到访时间\" prop=\"visitInfo.plannedEntryDatetime\" class=\"mobile-form-item\">\r\n                  <el-date-picker\r\n                    v-model=\"formData.visitInfo.plannedEntryDatetime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"选择到访时间\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    :picker-options=\"entryPickerOptions\"\r\n                    @change=\"onArrivalTimeChange\"\r\n                    class=\"mobile-date-picker\"\r\n                  />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"离开时间\" prop=\"visitInfo.plannedExitDatetime\" class=\"mobile-form-item\">\r\n                  <el-date-picker\r\n                    v-model=\"formData.visitInfo.plannedExitDatetime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"选择离开时间\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    :picker-options=\"exitPickerOptions\"\r\n                    @change=\"onDepartureTimeChange\"\r\n                    class=\"mobile-date-picker\"\r\n                  />\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 访客信息卡片 -->\r\n            <div class=\"mobile-form-card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span class=\"card-title\">访客信息（共{{ totalVisitors }}人）</span>\r\n                <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addVisitor\"\r\n                           :disabled=\"formData.visitors.length >= 10\" class=\"add-visitor-btn\">\r\n                  添加\r\n                </el-button>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <div v-for=\"(visitor, index) in formData.visitors\" :key=\"index\" class=\"mobile-visitor-item\">\r\n                  <div class=\"visitor-header\">\r\n                    <div class=\"visitor-badge\" :class=\"{ primary: index === 0 }\">\r\n                      {{ index === 0 ? '主联系人' : `访客${index + 1}` }}\r\n                    </div>\r\n                    <el-button v-if=\"index > 0\" size=\"mini\" type=\"danger\" icon=\"el-icon-delete\"\r\n                               @click=\"removeVisitor(index)\" circle class=\"remove-btn\"></el-button>\r\n                  </div>\r\n\r\n                  <div class=\"visitor-form\">\r\n                    <el-form-item label=\"姓名\" :prop=\"`visitors.${index}.name`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.name\" placeholder=\"请输入姓名\" maxlength=\"20\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"手机号\" :prop=\"`visitors.${index}.phone`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.phone\" placeholder=\"请输入手机号\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"身份证号\" :prop=\"`visitors.${index}.idCard`\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.idCard\" placeholder=\"请输入身份证号\"\r\n                                class=\"mobile-input\" />\r\n                    </el-form-item>\r\n\r\n                    <el-form-item label=\"公司名称\" class=\"mobile-form-item\">\r\n                      <el-input v-model=\"visitor.company\" placeholder=\"请输入公司名称（可选）\"\r\n                                maxlength=\"100\" class=\"mobile-input\" />\r\n                    </el-form-item>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 登记成功页面 -->\r\n        <div class=\"success-content\" v-if=\"registrationCompleted\">\r\n          <div class=\"success-icon\">\r\n            <i class=\"el-icon-circle-check\"></i>\r\n          </div>\r\n\r\n          <h4>登记成功！</h4>\r\n          <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n\r\n          <!-- 登记信息摘要 -->\r\n          <div class=\"register-summary\">\r\n            <h5>登记信息摘要</h5>\r\n            <el-descriptions :column=\"2\" border>\r\n              <el-descriptions-item label=\"主联系人\">\r\n                {{ mainContact.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"联系电话\">\r\n                {{ mainContact.phone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访人\">\r\n                {{ formData.visitInfo.hostEmployeeName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访部门\">\r\n                {{ formData.visitInfo.departmentVisited }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                {{ formData.visitInfo.reasonForVisit }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计到访时间\">\r\n                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计离开时间\">\r\n                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"访客总数\">\r\n                {{ totalVisitors }} 人\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n\r\n          <!-- 访问凭证 -->\r\n          <!-- <div class=\"access-credential\" v-if=\"registrationId\">\r\n            <h5>访问凭证</h5>\r\n            <div class=\"credential-card\">\r\n              <div class=\"qr-code-container\">\r\n                <div class=\"qr-code\">\r\n                  <i class=\"el-icon-qrcode\"></i>\r\n                </div>\r\n                <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n              </div>\r\n              <div class=\"credential-info\">\r\n                <h6>使用说明</h6>\r\n                <ul>\r\n                  <li>请保存此二维码截图</li>\r\n                  <li>审核通过后可用于园区门禁</li>\r\n                  <li>凭证仅限当次访问使用</li>\r\n                  <li>如有疑问请联系园区前台</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div> -->\r\n        </div>\r\n\r\n        <!-- 移动端底部操作区 -->\r\n        <div class=\"mobile-actions\">\r\n          <!-- 表单提交按钮 -->\r\n          <div v-if=\"!registrationCompleted\" class=\"submit-actions\">\r\n            <el-button type=\"primary\" @click=\"submitForm\" size=\"large\" :loading=\"submitting\"\r\n                       class=\"mobile-submit-btn\">\r\n              <i class=\"el-icon-check\" v-if=\"!submitting\"></i>\r\n              <i class=\"el-icon-loading\" v-if=\"submitting\"></i>\r\n              {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}\r\n            </el-button>\r\n\r\n            <el-button @click=\"resetForm\" size=\"large\" class=\"mobile-reset-btn\">\r\n              <i class=\"el-icon-refresh-left\"></i>\r\n              重置表单\r\n            </el-button>\r\n          </div>\r\n\r\n          <!-- 成功后操作按钮 -->\r\n          <div v-if=\"registrationCompleted\" class=\"success-actions\">\r\n            <el-button type=\"primary\" @click=\"printCredential\" size=\"large\" class=\"mobile-action-btn\">\r\n              <i class=\"el-icon-printer\"></i>\r\n              打印凭证\r\n            </el-button>\r\n            <el-button @click=\"resetForm\" size=\"large\" class=\"mobile-action-btn\">\r\n              <i class=\"el-icon-refresh\"></i>\r\n              重新登记\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      submitting: false,\r\n      registrationCompleted: false,\r\n\r\n      // 表单数据 - 与微信端保持一致的结构\r\n      formData: {\r\n        // 访客列表（第一位为主联系人）\r\n        visitors: [\r\n          {\r\n            name: '',\r\n            phone: '',\r\n            idCard: '',\r\n            company: '',\r\n            isMainContact: true\r\n          }\r\n        ],\r\n        // 来访信息\r\n        visitInfo: {\r\n          reasonForVisit: '',\r\n          hostEmployeeName: '',\r\n          departmentVisited: '',\r\n          vehiclePlateNumber: '',\r\n          plannedEntryDatetime: '',\r\n          plannedExitDatetime: ''\r\n        }\r\n      },\r\n\r\n      // 表单验证规则 - 与微信端保持一致\r\n      visitRules: {\r\n        'visitInfo.reasonForVisit': [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.hostEmployeeName': [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.departmentVisited': [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.plannedEntryDatetime': [\r\n          { required: true, message: '请选择预计来访时间', trigger: 'change' }\r\n        ],\r\n        'visitInfo.plannedExitDatetime': [\r\n          { required: true, message: '请选择预计离开时间', trigger: 'change' }\r\n        ],\r\n        // 动态访客验证规则\r\n        'visitors.0.name': [\r\n          { required: true, message: '请输入主联系人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.phone': [\r\n          { required: true, message: '请输入主联系人手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.idCard': [\r\n          { required: true, message: '请输入主联系人身份证号', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n\r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '中午12点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(12, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 总访客人数\r\n    totalVisitors() {\r\n      return this.formData.visitors.length;\r\n    },\r\n\r\n    // 主联系人信息\r\n    mainContact() {\r\n      return this.formData.visitors[0] || {};\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 添加访客\r\n    addVisitor() {\r\n      if (this.formData.visitors.length >= 10) {\r\n        this.$message.warning('最多只能添加10名访客');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.push({\r\n        name: '',\r\n        phone: '',\r\n        idCard: '',\r\n        company: '',\r\n        isMainContact: false\r\n      });\r\n\r\n      this.$message.success('已添加访客');\r\n    },\r\n\r\n    // 移除访客\r\n    removeVisitor(index) {\r\n      if (index === 0) {\r\n        this.$message.warning('不能删除主联系人');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.splice(index, 1);\r\n      this.$message.success('已移除访客');\r\n    },\r\n\r\n    // 格式化日期为后端期望的格式\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n\r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.formData.visitInfo.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n\r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n\r\n        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.formData.visitInfo.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.formData.visitInfo.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 提交表单 */\r\n    async submitForm() {\r\n      try {\r\n        // 验证表单\r\n        await this.$refs.visitForm.validate();\r\n\r\n        // 验证访客信息\r\n        if (!this.validateVisitors()) {\r\n          return;\r\n        }\r\n\r\n        // 验证时间\r\n        if (!this.validateTimes()) {\r\n          return;\r\n        }\r\n\r\n        this.submitting = true;\r\n\r\n        // 获取主联系人信息\r\n        const mainContact = this.formData.visitors[0];\r\n\r\n        const submitData = {\r\n          // VisitRegistrations 对象\r\n          registration: {\r\n            primaryContactName: mainContact.name.trim(),\r\n            primaryContactPhone: mainContact.phone.trim(),\r\n            reasonForVisit: this.formData.visitInfo.reasonForVisit,\r\n            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,\r\n            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n            departmentVisited: this.formData.visitInfo.departmentVisited,\r\n            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),\r\n            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),\r\n            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',\r\n            totalCompanions: this.formData.visitors.length - 1\r\n          },\r\n          // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n          attendeesList: this.formData.visitors.map((visitor, index) => ({\r\n            visitorName: visitor.name.trim(),\r\n            visitorPhone: visitor.phone.trim(),\r\n            visitorIdCard: visitor.idCard.trim().toUpperCase(),\r\n            visitorCompany: (visitor.company || '').trim(),\r\n            isPrimary: index === 0 ? \"1\" : \"0\", // 第一个访客必须是主联系人\r\n            visitorAvatarPhoto: null // 暂时不支持头像上传\r\n          }))\r\n        };\r\n\r\n        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n\r\n        // 调用API提交数据\r\n        const response = await submitVisitorRegistration(submitData);\r\n\r\n        if (response.code === 200) {\r\n          this.registrationId = response.data || 'VR' + Date.now();\r\n          this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n          this.registrationCompleted = true;\r\n        } else {\r\n          this.$message.error(response.msg || '登记失败，请重试');\r\n        }\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error);\r\n        this.$message.error('登记失败，请检查网络连接');\r\n      } finally {\r\n        this.submitting = false;\r\n      }\r\n    },\r\n\r\n    // 验证访客信息\r\n    validateVisitors() {\r\n      for (let i = 0; i < this.formData.visitors.length; i++) {\r\n        const visitor = this.formData.visitors[i];\r\n        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n\r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号格式（与后端保持一致）\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idCard || visitor.idCard.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n\r\n        const idCard = visitor.idCard.trim().toUpperCase();\r\n\r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idCard = visitor.idCard.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 验证时间\r\n    validateTimes() {\r\n      if (!this.formData.visitInfo.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.formData.visitInfo.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 检查来访时间不能早于当前时间1小时前\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (entryTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能早于当前时间1小时前');\r\n        return false;\r\n      }\r\n\r\n      // 检查离开时间必须晚于来访时间\r\n      if (exitTime <= entryTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      // 检查访问时长不能超过24小时\r\n      const visitDuration = exitTime.getTime() - entryTime.getTime();\r\n      const maxDuration = 24 * 60 * 60 * 1000; // 24小时\r\n      if (visitDuration > maxDuration) {\r\n        this.$message.error('单次访问时长不能超过24小时');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 重置所有数据\r\n        this.registrationCompleted = false;\r\n\r\n        this.formData = {\r\n          visitors: [\r\n            {\r\n              name: '',\r\n              phone: '',\r\n              idCard: '',\r\n              company: '',\r\n              isMainContact: true\r\n            }\r\n          ],\r\n          visitInfo: {\r\n            reasonForVisit: '',\r\n            hostEmployeeName: '',\r\n            departmentVisited: '',\r\n            vehiclePlateNumber: '',\r\n            plannedEntryDatetime: '',\r\n            plannedExitDatetime: ''\r\n          }\r\n        };\r\n\r\n        this.registrationId = null;\r\n\r\n        this.$message.success('表单已重置');\r\n      });\r\n    },\r\n\r\n    // 打印凭证\r\n    printCredential() {\r\n      this.$message.info('打印功能开发中...');\r\n    },\r\n\r\n    // 格式化时间显示\r\n    parseTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    },\r\n\r\n    // 显示帮助信息\r\n    showHelp() {\r\n      this.$alert(`\r\n        <div style=\"text-align: left;\">\r\n          <h4>使用说明</h4>\r\n          <p><strong>1. 填写基本信息</strong></p>\r\n          <ul>\r\n            <li>请如实填写来访事由、被访人等信息</li>\r\n            <li>主联系人信息必须准确，用于后续联系</li>\r\n          </ul>\r\n          <p><strong>2. 添加同行人员</strong></p>\r\n          <ul>\r\n            <li>如有同行人员，请点击\"添加访客\"按钮</li>\r\n            <li>每位访客都需要填写完整身份信息</li>\r\n          </ul>\r\n          <p><strong>3. 选择访问时间</strong></p>\r\n          <ul>\r\n            <li>请合理安排访问时间，避免过长逗留</li>\r\n            <li>如需延长访问时间，请联系被访人</li>\r\n          </ul>\r\n          <p><strong>4. 审核与通行</strong></p>\r\n          <ul>\r\n            <li>提交后等待审核，通过后可凭二维码进入园区</li>\r\n            <li>请按照预约时间准时到访</li>\r\n          </ul>\r\n        </div>\r\n      `, '使用帮助', {\r\n        confirmButtonText: '知道了',\r\n        dangerouslyUseHTMLString: true\r\n      });\r\n    },\r\n\r\n    // 联系客服\r\n    contactService() {\r\n      this.$confirm('需要帮助吗？', '联系客服', {\r\n        confirmButtonText: '拨打电话',\r\n        cancelButtonText: '取消',\r\n        type: 'info'\r\n      }).then(() => {\r\n        // 这里可以实现拨打客服电话的功能\r\n        this.$message({\r\n          type: 'info',\r\n          message: '客服电话：400-xxx-xxxx'\r\n        });\r\n      }).catch(() => {\r\n        // 用户取消\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 移动端布局优化 */\r\n.main-content.mobile-layout {\r\n  flex-direction: column;\r\n  padding: 20px;\r\n  gap: 20px;\r\n  max-width: 100%;\r\n}\r\n\r\n/* 移动端欢迎区域 */\r\n.mobile-welcome {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.welcome-header {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.welcome-header .welcome-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 15px;\r\n}\r\n\r\n.welcome-header .welcome-icon i {\r\n  font-size: 28px;\r\n  color: #fff;\r\n}\r\n\r\n.welcome-header h2 {\r\n  font-size: 24px;\r\n  color: #2c3e50;\r\n  margin: 0 0 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-header .welcome-desc {\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  margin-top: 20px;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #f0f0f0;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 移动端表单容器 */\r\n.mobile-form-container {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 移动端表单标题 */\r\n.mobile-form-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.mobile-form-header h3 {\r\n  margin: 0 0 8px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.mobile-form-header .form-subtitle {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 移动端表单内容 */\r\n.mobile-form-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 移动端表单卡片 */\r\n.mobile-form-card {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  overflow: hidden;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.mobile-form-card .card-header {\r\n  background: #fff;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.mobile-form-card .card-header i {\r\n  font-size: 16px;\r\n  color: #667eea;\r\n  margin-right: 8px;\r\n}\r\n\r\n.mobile-form-card .card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.mobile-form-card .add-visitor-btn {\r\n  margin-left: 10px;\r\n}\r\n\r\n.mobile-form-card .card-content {\r\n  padding: 20px;\r\n}\r\n\r\n/* 移动端表单项 */\r\n.mobile-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.mobile-form-item .el-form-item__label {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  line-height: 1.4;\r\n  padding-bottom: 8px;\r\n}\r\n\r\n/* 移动端输入框 */\r\n.mobile-input .el-input__inner {\r\n  height: 44px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 0 15px;\r\n}\r\n\r\n.mobile-input .el-textarea__inner {\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 12px 15px;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 移动端日期选择器 */\r\n.mobile-date-picker .el-input__inner {\r\n  height: 44px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 移动端访客项 */\r\n.mobile-visitor-item {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e9ecef;\r\n  position: relative;\r\n}\r\n\r\n.mobile-visitor-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mobile-visitor-item .visitor-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.mobile-visitor-item .visitor-badge {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n  padding: 4px 12px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.mobile-visitor-item .visitor-badge.primary {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  border: none;\r\n}\r\n\r\n.mobile-visitor-item .remove-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n/* 移动端操作区域 */\r\n.mobile-actions {\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n.submit-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.success-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n/* 移动端按钮 */\r\n.mobile-submit-btn,\r\n.mobile-reset-btn,\r\n.mobile-action-btn {\r\n  width: 100%;\r\n  height: 48px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  border-radius: 8px;\r\n  border: none;\r\n}\r\n\r\n.mobile-submit-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n}\r\n\r\n.mobile-submit-btn:hover {\r\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\r\n}\r\n\r\n.mobile-reset-btn {\r\n  background: #fff;\r\n  color: #6c757d;\r\n  border: 1px solid #dcdfe6;\r\n}\r\n\r\n.mobile-reset-btn:hover {\r\n  background: #f8f9fa;\r\n  color: #495057;\r\n}\r\n\r\n.mobile-action-btn {\r\n  background: #fff;\r\n  color: #667eea;\r\n  border: 1px solid #667eea;\r\n}\r\n\r\n.mobile-action-btn:hover {\r\n  background: #667eea;\r\n  color: #fff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  /* 顶部导航移动端优化 */\r\n  .top-nav {\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .nav-content {\r\n    height: 60px;\r\n  }\r\n\r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .nav-actions {\r\n    gap: 10px;\r\n  }\r\n\r\n  /* 主内容区域移动端优化 */\r\n  .main-content {\r\n    padding: 15px;\r\n    gap: 15px;\r\n  }\r\n\r\n  .main-content.mobile-layout {\r\n    padding: 10px;\r\n    gap: 15px;\r\n  }\r\n\r\n  /* 移动端欢迎区域优化 */\r\n  .mobile-welcome {\r\n    padding: 15px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .welcome-header .welcome-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .welcome-header .welcome-icon i {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .welcome-header h2 {\r\n    font-size: 20px;\r\n  }\r\n\r\n  /* 移动端表单优化 */\r\n  .mobile-form-container {\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .mobile-form-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-form-header h3 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .mobile-form-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-form-card {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .mobile-form-card .card-header {\r\n    padding: 12px 15px;\r\n  }\r\n\r\n  .mobile-form-card .card-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  /* 移动端访客项优化 */\r\n  .mobile-visitor-item {\r\n    padding: 12px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  /* 移动端操作区域优化 */\r\n  .mobile-actions {\r\n    padding: 15px;\r\n  }\r\n\r\n  .mobile-submit-btn,\r\n  .mobile-reset-btn,\r\n  .mobile-action-btn {\r\n    height: 44px;\r\n    font-size: 15px;\r\n  }\r\n}\r\n\r\n/* 超小屏幕优化 */\r\n@media (max-width: 480px) {\r\n  .top-nav {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .nav-content {\r\n    height: 55px;\r\n  }\r\n\r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .main-content.mobile-layout {\r\n    padding: 8px;\r\n  }\r\n\r\n  .mobile-welcome {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-form-header {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-form-content {\r\n    padding: 12px;\r\n  }\r\n\r\n  .mobile-actions {\r\n    padding: 12px;\r\n  }\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n"]}]}