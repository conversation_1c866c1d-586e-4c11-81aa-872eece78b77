package com.sxgygt.asc.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.sxgygt.asc.mapper.VisitRegistrationsMapper;
import com.sxgygt.asc.mapper.RegistrationAttendeesMapper;
import com.sxgygt.asc.domain.VisitRegistrations;
import com.sxgygt.asc.domain.RegistrationAttendees;
import com.sxgygt.asc.service.IVisitRegistrationsService;
import com.sxgygt.asc.service.IVisitorBasicsService;
import com.sxgygt.asc.service.IRegistrationAttendeesService;
import com.sxgygt.common.utils.DateUtils;

/**
 * 访问登记信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class VisitRegistrationsServiceImpl implements IVisitRegistrationsService 
{
    @Autowired
    private VisitRegistrationsMapper visitRegistrationsMapper;
    
    @Autowired
    private RegistrationAttendeesMapper registrationAttendeesMapper;
    
    @Autowired
    private IVisitorBasicsService visitorBasicsService;
    
    @Autowired
    private IRegistrationAttendeesService registrationAttendeesService;

    /**
     * 查询访问登记信息
     * 
     * @param registrationId 访问登记信息主键
     * @return 访问登记信息
     */
    @Override
    public VisitRegistrations selectVisitRegistrationsById(Long registrationId)
    {
        return visitRegistrationsMapper.selectVisitRegistrationsById(registrationId);
    }

    /**
     * 查询访问登记信息列表
     * 
     * @param visitRegistrations 访问登记信息
     * @return 访问登记信息
     */
    @Override
    public List<VisitRegistrations> selectVisitRegistrationsList(VisitRegistrations visitRegistrations)
    {
        return visitRegistrationsMapper.selectVisitRegistrationsList(visitRegistrations);
    }

    /**
     * 查询待审核的登记信息
     * 
     * @return 访问登记信息集合
     */
    @Override
    public List<VisitRegistrations> selectPendingRegistrations()
    {
        return visitRegistrationsMapper.selectPendingRegistrations(new VisitRegistrations());
    }

    /**
     * 根据被访人查询登记信息
     * 
     * @param hostEmployeeId 被访人用户ID
     * @return 访问登记信息集合
     */
    @Override
    public List<VisitRegistrations> selectRegistrationsByHostEmployeeId(Long hostEmployeeId)
    {
        return visitRegistrationsMapper.selectRegistrationsByHostEmployeeId(hostEmployeeId);
    }

    /**
     * 查询今日登记信息
     *
     * @return 访问登记信息集合
     */
    @Override
    public List<VisitRegistrations> selectTodayRegistrations()
    {
        return visitRegistrationsMapper.selectTodayRegistrations();
    }

    /**
     * 访客查询今日登记记录（根据身份证号或手机号）
     *
     * @param idCardNumber 身份证号
     * @param primaryContactPhone 主联系人手机号
     * @return 访问登记信息集合
     */
    @Override
    public List<VisitRegistrations> queryTodayRegistrationsByVisitor(String idCardNumber, String primaryContactPhone)
    {
        return visitRegistrationsMapper.queryTodayRegistrationsByVisitor(idCardNumber, primaryContactPhone);
    }

    /**
     * 新增访问登记信息（支持多人登记）
     * 
     * @param visitRegistrations 访问登记信息
     * @param attendeesList 参与人员列表
     * @return 结果
     */
    @Override
    @Transactional
    public int insertVisitRegistrations(VisitRegistrations visitRegistrations, List<RegistrationAttendees> attendeesList)
    {
        visitRegistrations.setSubmittedDatetime(DateUtils.getNowDate());
        visitRegistrations.setCreateTime(DateUtils.getNowDate());
        visitRegistrations.setStatus("pending"); // 默认待审核状态
        
        // 设置同行人数（不含主访客）
        if (attendeesList != null && attendeesList.size() > 1) {
            visitRegistrations.setTotalCompanions(attendeesList.size() - 1);
        } else {
            visitRegistrations.setTotalCompanions(0);
        }
        
        // 插入登记记录
        int result = visitRegistrationsMapper.insertVisitRegistrations(visitRegistrations);
        
        // 处理参与人员
        if (result > 0 && attendeesList != null && !attendeesList.isEmpty()) {
            for (int i = 0; i < attendeesList.size(); i++) {
                RegistrationAttendees attendee = attendeesList.get(i);
                
                // 创建或获取访客信息
                var visitor = visitorBasicsService.createOrGetVisitor(
                    attendee.getVisitorName(),
                    attendee.getVisitorPhone(), 
                    attendee.getVisitorIdCard(),
                    attendee.getVisitorCompany(),
                    attendee.getVisitorAvatarPhoto()
                );
                
                // 设置关联信息
                attendee.setRegistrationId(visitRegistrations.getRegistrationId());
                attendee.setVisitorId(visitor.getVisitorId());
                attendee.setIsPrimary(i == 0 ? "1" : "0"); // 第一个为主访客
                attendee.setDelFlag("0");
                attendee.setCreateTime(DateUtils.getNowDate());
                
                registrationAttendeesMapper.insertRegistrationAttendees(attendee);
            }
        }
        
        return result;
    }

    /**
     * 修改访问登记信息
     * 
     * @param visitRegistrations 访问登记信息
     * @return 结果
     */
    @Override
    public int updateVisitRegistrations(VisitRegistrations visitRegistrations)
    {
        visitRegistrations.setUpdateTime(DateUtils.getNowDate());
        return visitRegistrationsMapper.updateVisitRegistrations(visitRegistrations);
    }

    /**
     * 批量删除访问登记信息
     * 
     * @param registrationIds 需要删除的访问登记信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteVisitRegistrationsByIds(Long[] registrationIds)
    {
        // 同时删除关联的参与人记录
        for (Long registrationId : registrationIds) {
            registrationAttendeesMapper.deleteAttendeesByRegistrationId(registrationId);
        }
        return visitRegistrationsMapper.deleteVisitRegistrationsByIds(registrationIds);
    }

    /**
     * 删除访问登记信息信息
     * 
     * @param registrationId 访问登记信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteVisitRegistrationsById(Long registrationId)
    {
        // 同时删除关联的参与人记录
        registrationAttendeesMapper.deleteAttendeesByRegistrationId(registrationId);
        return visitRegistrationsMapper.deleteVisitRegistrationsById(registrationId);
    }

    /**
     * 审核通过访问登记
     * 
     * @param registrationId 登记ID
     * @param approvalStaffId 审核人ID
     * @param approvalStaffName 审核人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int approveRegistration(Long registrationId, Long approvalStaffId, String approvalStaffName)
    {
        // 更新登记状态
        VisitRegistrations registration = new VisitRegistrations();
        registration.setRegistrationId(registrationId);
        registration.setStatus("approved");
        registration.setApprovalStaffId(approvalStaffId);
        registration.setApprovalStaffName(approvalStaffName);
        registration.setApprovalDatetime(DateUtils.getNowDate());
        registration.setUpdateTime(DateUtils.getNowDate());
        
        int result = visitRegistrationsMapper.updateVisitRegistrations(registration);
        
        if (result > 0) {
            // 获取关联的访客列表
            List<RegistrationAttendees> attendeesList = registrationAttendeesMapper.selectAttendeesByRegistrationId(registrationId);
            
            // 更新访客状态和生成通行凭证
            for (RegistrationAttendees attendee : attendeesList) {
                // 更新访客状态
                if ("provisional".equals(attendee.getVisitorBasics().getStatus())) {
                    visitorBasicsService.updateVisitorStatusToVerified(attendee.getVisitorId());
                } else {
                    visitorBasicsService.updateLastVisitDate(attendee.getVisitorId());
                }
            }
            
            // 批量生成通行凭证
            registrationAttendeesService.generateCredentialsForRegistration(registrationId);
        }
        
        return result;
    }

    /**
     * 拒绝访问登记
     * 
     * @param registrationId 登记ID
     * @param rejectionReason 拒绝原因
     * @param approvalStaffId 审核人ID
     * @param approvalStaffName 审核人姓名
     * @return 结果
     */
    @Override
    public int rejectRegistration(Long registrationId, String rejectionReason, Long approvalStaffId, String approvalStaffName)
    {
        VisitRegistrations registration = new VisitRegistrations();
        registration.setRegistrationId(registrationId);
        registration.setStatus("rejected");
        registration.setRejectionReason(rejectionReason);
        registration.setApprovalStaffId(approvalStaffId);
        registration.setApprovalStaffName(approvalStaffName);
        registration.setApprovalDatetime(DateUtils.getNowDate());
        registration.setUpdateTime(DateUtils.getNowDate());
        
        return visitRegistrationsMapper.updateVisitRegistrations(registration);
    }

    /**
     * 取消访问登记
     * 
     * @param registrationId 登记ID
     * @return 结果
     */
    @Override
    public int cancelRegistration(Long registrationId)
    {
        VisitRegistrations registration = new VisitRegistrations();
        registration.setRegistrationId(registrationId);
        registration.setStatus("cancelled");
        registration.setUpdateTime(DateUtils.getNowDate());
        
        return visitRegistrationsMapper.updateVisitRegistrations(registration);
    }

    /**
     * 完成访问登记（所有访客已离场）
     * 
     * @param registrationId 登记ID
     * @return 结果
     */
    @Override
    public int completeRegistration(Long registrationId)
    {
        VisitRegistrations registration = new VisitRegistrations();
        registration.setRegistrationId(registrationId);
        registration.setStatus("completed");
        registration.setUpdateTime(DateUtils.getNowDate());
        
        return visitRegistrationsMapper.updateVisitRegistrations(registration);
    }
}
