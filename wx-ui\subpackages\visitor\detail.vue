<template>
  <view class="detail-container">
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
    </view>
    
    <view v-else-if="visitorInfo" class="content">
      <!-- 访客基本信息 -->
      <uni-card title="登记信息" :is-shadow="false" margin="15px 0">
        <view class="info-item">
          <text class="label">主联系人：</text>
          <text class="value">{{ visitorInfo.primaryContactName }}</text>
        </view>
        <view class="info-item">
          <text class="label">联系电话：</text>
          <text class="value">{{ visitorInfo.primaryContactPhone }}</text>
        </view>
        <view class="info-item">
          <text class="label">访客人数：</text>
          <text class="value">{{ (visitorInfo.totalCompanions || 0) + 1 }}人</text>
        </view>
        <view class="info-item">
          <text class="label">登记状态：</text>
          <view class="status-badge" :class="statusClass">
            {{ statusText }}
          </view>
        </view>
      </uni-card>

      <!-- 来访信息 -->
      <uni-card title="来访信息" :is-shadow="false" margin="15px 0">
        <view class="info-item">
          <text class="label">来访事由：</text>
          <text class="value">{{ visitorInfo.reasonForVisit }}</text>
        </view>
        <view class="info-item">
          <text class="label">被访人：</text>
          <text class="value">{{ visitorInfo.hostEmployeeName }}</text>
        </view>
        <view class="info-item">
          <text class="label">被访部门：</text>
          <text class="value">{{ visitorInfo.departmentVisited }}</text>
        </view>
        <view class="info-item">
          <text class="label">预计来访时间：</text>
          <text class="value">{{ formatDateTime(visitorInfo.plannedEntryDatetime) }}</text>
        </view>
        <view class="info-item">
          <text class="label">预计离开时间：</text>
          <text class="value">{{ formatDateTime(visitorInfo.plannedExitDatetime) }}</text>
        </view>
        <view class="info-item" v-if="visitorInfo.vehiclePlateNumber">
          <text class="label">车牌号：</text>
          <text class="value">{{ visitorInfo.vehiclePlateNumber }}</text>
        </view>
      </uni-card>

      <!-- 访问状态 -->
      <uni-card title="访问状态" :is-shadow="false" margin="15px 0">
        <view class="status-item">
          <view class="status-badge" :class="statusClass">
            {{ statusText }}
          </view>
        </view>
        <view class="info-item" v-if="visitorInfo.submittedDatetime">
          <text class="label">提交时间：</text>
          <text class="value">{{ formatDateTime(visitorInfo.submittedDatetime) }}</text>
        </view>
        <view class="info-item" v-if="visitorInfo.approvalDatetime">
          <text class="label">审核时间：</text>
          <text class="value">{{ formatDateTime(visitorInfo.approvalDatetime) }}</text>
        </view>
        <view class="info-item" v-if="visitorInfo.approvalStaffName">
          <text class="label">审核人：</text>
          <text class="value">{{ visitorInfo.approvalStaffName }}</text>
        </view>
        <view class="info-item" v-if="visitorInfo.rejectionReason">
          <text class="label">拒绝原因：</text>
          <text class="value">{{ visitorInfo.rejectionReason }}</text>
        </view>
      </uni-card>

      <!-- 参与人员 -->
      <uni-card v-if="attendeesList && attendeesList.length > 0" title="访客列表" :is-shadow="false" margin="15px 0">
        <view v-for="(attendee, index) in attendeesList" :key="index" class="companion-item">
          <view class="companion-header">
            <text class="companion-title">
              {{ attendee.isPrimary === '1' ? '主联系人' : `随行人员 ${index}` }}
            </text>
          </view>
          <view class="info-item" v-if="attendee.visitorBasics">
            <text class="label">姓名：</text>
            <text class="value">{{ attendee.visitorBasics.name }}</text>
          </view>
          <view class="info-item" v-if="attendee.visitorBasics">
            <text class="label">身份证号：</text>
            <text class="value">{{ formatIdCard(attendee.visitorBasics.idCardNumber) }}</text>
          </view>
          <view class="info-item" v-if="attendee.visitorBasics && attendee.visitorBasics.phone">
            <text class="label">联系电话：</text>
            <text class="value">{{ attendee.visitorBasics.phone }}</text>
          </view>
          <view class="info-item" v-if="attendee.visitorBasics && attendee.visitorBasics.company">
            <text class="label">所属公司：</text>
            <text class="value">{{ attendee.visitorBasics.company }}</text>
          </view>
          <view class="info-item" v-if="attendee.accessCredential">
            <text class="label">通行凭证：</text>
            <text class="value">{{ attendee.accessCredential }}</text>
          </view>
        </view>
      </uni-card>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button v-if="canCheckOut" class="checkout-btn" @click="handleCheckOut">
          访客签出
        </button>
        <button class="print-btn" @click="handlePrint">
          打印凭证
        </button>
      </view>
    </view>

    <view v-else class="error-container">
      <uni-icons type="info" size="60" color="#ccc"></uni-icons>
      <text class="error-text">访客信息不存在</text>
    </view>
  </view>
</template>

<script>
import { getRegistrationInfo } from '@/api/system/visitor'

export default {
  data() {
    return {
      loading: true,
      visitorInfo: null,
      attendeesList: [],
      registrationId: null,
      loadingText: {
        contentdown: '上拉显示更多',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      }
    };
  },
  computed: {
    canCheckOut() {
      return this.visitorInfo && (this.visitorInfo.status === 'approved' || this.visitorInfo.status === 'APPROVED');
    },
    statusText() {
      if (!this.visitorInfo) return '';
      return this.getStatusText(this.visitorInfo.status);
    },
    statusClass() {
      if (!this.visitorInfo) return '';
      return this.getStatusClass(this.visitorInfo.status);
    }
  },
  onLoad(options) {
    if (options.registrationId) {
      this.registrationId = options.registrationId;
      this.loadVisitorDetail();
    } else if (options.id) {
      this.registrationId = options.id;
      this.loadVisitorDetail();
    } else {
      this.loading = false;
    }
  },
  methods: {
    // 加载访客详情
    async loadVisitorDetail() {
      try {
        this.loading = true;
        const response = await getRegistrationInfo(this.registrationId);
        
        if (response.code === 200 && response.data) {
          this.visitorInfo = response.data;
          this.attendeesList = response.data.attendeesList || [];
          
          uni.setNavigationBarTitle({
            title: `${this.visitorInfo.primaryContactName} - 访客详情`
          });
        } else {
          this.$modal.showError(response.msg || '获取访客信息失败');
        }
      } catch (error) {
        console.error('加载访客详情失败:', error);
        this.$modal.showError('加载失败，请检查网络连接');
      } finally {
        this.loading = false;
      }
    },

    // 处理签出
    async handleCheckOut() {
      try {
        const result = await this.$modal.confirm('确认为该访客办理签出吗？');
        if (!result) return;

        this.$modal.showLoading('正在办理签出...');
        // 这里调用签出API
        // const response = await checkoutVisitor(this.registrationId);
        
        this.$modal.showSuccess('签出成功');
        this.loadVisitorDetail(); // 刷新数据
      } catch (error) {
        console.error('签出失败:', error);
        this.$modal.showError('签出失败，请重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 处理打印
    async handlePrint() {
      try {
        this.$modal.showLoading('准备打印...');
        // 这里调用打印API
        this.$modal.showSuccess('打印任务已发送');
      } catch (error) {
        console.error('打印失败:', error);
        this.$modal.showError('打印失败，请重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 格式化身份证号
    formatIdCard(idCard) {
      if (!idCard) return '';
      return idCard.replace(/^(.{6}).*(.{4})$/, '$1****$2');
    },

    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝',
        'cancelled': '已取消',
        'completed': '已完成',
        // 兼容大写格式
        'PENDING': '待审核',
        'APPROVED': '已通过',
        'REJECTED': '已拒绝',
        'CANCELLED': '已取消',
        'COMPLETED': '已完成'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        'pending': 'status-pending',
        'approved': 'status-approved',
        'rejected': 'status-rejected',
        'cancelled': 'status-cancelled',
        'completed': 'status-completed',
        // 兼容大写格式
        'PENDING': 'status-pending',
        'APPROVED': 'status-approved',
        'REJECTED': 'status-rejected',
        'CANCELLED': 'status-cancelled',
        'COMPLETED': 'status-completed'
      };
      return statusClassMap[status] || 'status-unknown';
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  
  .error-text {
    margin-top: 15px;
    color: #999;
    font-size: 14px;
  }
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  
  .label {
    width: 100px;
    color: #666;
    font-size: 14px;
  }
  
  .value {
    flex: 1;
    color: #333;
    font-size: 14px;
    word-break: break-all;
  }
}

.status-item {
  margin-bottom: 15px;
  
  .status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    
    &.status-pending {
      background-color: #E6A23C;
      color: white;
    }
    
    &.status-approved {
      background-color: #67C23A;
      color: white;
    }
    
    &.status-checked-in {
      background-color: #409EFF;
      color: white;
    }
    
    &.status-checked-out {
      background-color: #909399;
      color: white;
    }
    
    &.status-rejected {
      background-color: #F56C6C;
      color: white;
    }
    
    &.status-unknown {
      background-color: #C0C4CC;
      color: white;
    }
  }
}

.companion-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fafafa;
  
  .companion-header {
    margin-bottom: 10px;
    
    .companion-title {
      font-weight: bold;
      color: #333;
      font-size: 14px;
    }
  }
}

.action-buttons {
  margin-top: 30px;
  display: flex;
  gap: 15px;
  
  .checkout-btn, .print-btn {
    flex: 1;
    height: 45px;
    border-radius: 22px;
    font-size: 14px;
    font-weight: bold;
    border: none;
    
    &:active {
      opacity: 0.8;
    }
  }
  
  .checkout-btn {
    background: linear-gradient(135deg, #F56C6C 0%, #FF8A80 100%);
    color: white;
  }
  
  .print-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
}

// 卡片样式优化
:deep(.uni-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .uni-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    
    .uni-card__header-title {
      color: white;
      font-weight: bold;
    }
  }
  
  .uni-card__content {
    padding: 20px;
  }
}
</style>
