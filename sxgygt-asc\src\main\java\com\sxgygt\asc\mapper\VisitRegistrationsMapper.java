package com.sxgygt.asc.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.sxgygt.asc.domain.VisitRegistrations;

/**
 * 访问登记信息Mapper接口
 * 
 * <AUTHOR>
 */
public interface VisitRegistrationsMapper 
{
    /**
     * 查询访问登记信息
     * 
     * @param registrationId 访问登记信息主键
     * @return 访问登记信息
     */
    public VisitRegistrations selectVisitRegistrationsById(Long registrationId);

    /**
     * 查询访问登记信息列表
     * 
     * @param visitRegistrations 访问登记信息
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectVisitRegistrationsList(VisitRegistrations visitRegistrations);

    /**
     * 查询待审核的登记信息列表
     * 
     * @param visitRegistrations 访问登记信息
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectPendingRegistrations(VisitRegistrations visitRegistrations);

    /**
     * 查询指定被访人的登记信息列表
     * 
     * @param hostEmployeeId 被访人ID
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectRegistrationsByHost(Long hostEmployeeId);

    /**
     * 根据被访人ID查询登记信息
     * 
     * @param hostEmployeeId 被访人员工ID
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectRegistrationsByHostEmployeeId(Long hostEmployeeId);

    /**
     * 查询今日登记信息
     *
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> selectTodayRegistrations();

    /**
     * 访客查询今日登记记录（根据身份证号或手机号）
     *
     * @param idCardNumber 身份证号
     * @param primaryContactPhone 主联系人手机号
     * @return 访问登记信息集合
     */
    public List<VisitRegistrations> queryTodayRegistrationsByVisitor(@Param("idCardNumber") String idCardNumber, @Param("primaryContactPhone") String primaryContactPhone);

    /**
     * 新增访问登记信息
     * 
     * @param visitRegistrations 访问登记信息
     * @return 结果
     */
    public int insertVisitRegistrations(VisitRegistrations visitRegistrations);

    /**
     * 修改访问登记信息
     * 
     * @param visitRegistrations 访问登记信息
     * @return 结果
     */
    public int updateVisitRegistrations(VisitRegistrations visitRegistrations);

    /**
     * 删除访问登记信息
     * 
     * @param registrationId 访问登记信息主键
     * @return 结果
     */
    public int deleteVisitRegistrationsById(Long registrationId);

    /**
     * 批量删除访问登记信息
     * 
     * @param registrationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVisitRegistrationsByIds(Long[] registrationIds);

    /**
     * 更新登记状态
     * 
     * @param visitRegistrations 访问登记信息
     * @return 结果
     */
    public int updateRegistrationStatus(VisitRegistrations visitRegistrations);

    /**
     * 查询今日登记统计
     * 
     * @return 统计结果
     */
    public int countTodayRegistrations();

    /**
     * 查询本周登记统计
     * 
     * @return 统计结果
     */
    public int countWeekRegistrations();

    /**
     * 查询本月登记统计
     * 
     * @return 统计结果
     */
    public int countMonthRegistrations();
}
