<template>
  <view class="vehicle-container">
    <!-- 搜索区域 -->
    <uni-section title="搜索条件" type="line">
      <view class="search-box">
        <view class="search-item">
          <input v-model="queryParams.licensePlate" placeholder="请输入车牌号" class="search-input" />
        </view>
        <view class="search-item">
          <input v-model="queryParams.userNickName" placeholder="请输入车主姓名" class="search-input" />
        </view>
        <button @click="handleQuery" class="cu-btn bg-blue">搜索</button>
        <button @click="resetQuery" class="cu-btn line-blue">重置</button>
        <!-- <button v-if="checkPermi(['asc:device:add'])" @click="handleAdd" class="cu-btn bg-green">新增</button> -->
      </view>
    </uni-section>

    <!-- 列表区域 -->
    <uni-section title="车辆列表" type="line">
      <view class="list-box">
        <view v-if="vehicleList.length === 0" class="empty-box">
          暂无数据
        </view>
        <template v-else>
          <view v-for="(item, index) in vehicleList" :key="index" class="list-item">
            <view class="item-main">
              <view class="item-title">
                <text class="plate-number">{{item.licensePlate}}</text>
                <!-- <text class="vehicle-brand">{{item.vehicleBrand}} {{item.vehicleModel}}</text> -->
              </view>
              <view class="item-info">
                <text>所属部门：{{(item.user && item.user.dept && item.user.dept.deptName) || '-'}}</text>
                <text>车主姓名：{{(item.user && item.user.nickName) || '-'}}</text>
                <text>联系方式：{{(item.user && item.user.phonenumber) || '-'}}</text>
                <view class="area-info">
                  <text class="area-label">授权区域：</text>
                  <text v-if="!item.areaPermissions || Object.keys(item.areaPermissions).length === 0" class="area-empty">暂无授权区域</text>
                  <text v-else class="area-list">{{Object.values(item.areaPermissions).join('、')}}</text>
                </view>
                <view class="parking-area-info">
                  <text class="area-label">停车位：</text>
                  <text v-if="!item.currentParkingAreaName" class="area-empty">暂无停车位</text>
                  <text v-else class="area-list">{{item.currentParkingAreaName}}</text>
                </view>
                <text>备注：{{item.remark || '-'}}</text>
              </view>
            </view>
          </view>
          <view class="pagination-box" v-if="total > 0">
            <uni-pagination
              :total="total"
              :pageSize="queryParams.pageSize"
              :current="queryParams.pageNum"
              @change="handlePageChange"
              show-icon
              :showIcon="true"
              prevText="上一页"
              nextText="下一页"
            />
          </view>
        </template>
      </view>
    </uni-section>

  </view>
</template>

<script>
import { listVehicle } from '@/api/system/vehicle';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';


export default {
  data() {
    return {
      loading: false,
      // 查询参数
      queryParams: {
        licensePlate: '',
        userNickName: '',
        pageNum: 1,
        pageSize: 10
      },
      // 车辆列表
      vehicleList: [],
      total: 0
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'permissions'
    ]),
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    /** 查询车辆列表 */
    async getList() {
      if (this.loading) return
      this.loading = true
      try {
        const res = await listVehicle(this.queryParams)
        console.log('API返回数据:', res)
        console.log('第一条数据部门信息:', res.rows[0]?.user?.dept)
        
        this.vehicleList = res.rows || []
        this.total = res.total || 0
        console.log('设置total:', this.total)
      } catch (error) {
        uni.showToast({
          title: '获取列表失败，请检查网络后重试',
          icon: 'error'
        })
        console.error('获取车辆列表失败:', error)
        this.vehicleList = []
        this.total = 0
      } finally {
        setTimeout(() => {
          this.loading = false
        }, 300)
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        licensePlate: '',
        userNickName: '',
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    // 分页改变事件处理函数
    handlePageChange(e) {
      console.log('页码改变:', e)
      this.queryParams.pageNum = e.current
      this.getList()
    }
  }
}
</script>

<style lang="scss">
.vehicle-container {
  padding: 15px;
}

.search-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 15px;
  gap: 10px;

  .search-item {
    flex: 1;
    min-width: 200px;

    .search-input {
      width: 100%;
      height: 35px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 0 10px;
    }
  }

  .cu-btn {
    margin: 0;
    padding: 0 15px;
    height: 35px;
    line-height: 35px;
  }
}

.list-box {
  padding: 15px;
  min-height: 100px;

  .loading-box,
  .empty-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    color: #909399;
    font-size: 14px;
  }

  .list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);

    .item-main {
      flex: 1;
    }

    .item-title {
      margin-bottom: 10px;

      .plate-number {
        font-size: 16px;
        font-weight: bold;
        margin-right: 10px;
      }

      .vehicle-brand {
        color: #909399;
        font-size: 14px;
      }
    }

    .item-info {
      color: #606266;
      font-size: 14px;
      padding: 5px 0;

      .area-info,
      .parking-area-info {
        margin: 5px 0;
        display: flex;
        align-items: flex-start;
        
        .area-label {
          color: #666;
          min-width: 80px;
          flex-shrink: 0;
        }
        
        .area-empty {
          color: #999;
        }
        
        .area-list {
          color: #333;
          flex: 1;
        }
      }

      text {
        display: block;
        margin-bottom: 8px;
        line-height: 24px;
      }
    }

  }
}

.pagination-box {
  margin-top: 20px;
  padding: 10px;
  display: flex;
  justify-content: center;
  background-color: #fff;
  width: 100%;

  /* #ifdef MP-WEIXIN */
  :deep(.uni-pagination) {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
  }

  :deep(.uni-pagination__btn) {
    min-width: 80px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    background-color: #007aff;
    color: #fff;

    &.uni-pagination--disabled {
      background-color: #eee;
      color: #999;
    }
  }

  :deep(.uni-pagination__num) {
    padding: 0 10px;
  }

  :deep(.uni-pagination__num-current-text) {
    font-size: 14px;
    color: #007aff;
  }
  /* #endif */

  /* #ifndef MP-WEIXIN */
  :deep(.uni-pagination) {
    width: 100%;
  }

  :deep(.uni-pagination__btn) {
    min-width: 60px;
    height: 36px;
    line-height: 36px;
    margin: 0 5px;
  }

  :deep(.uni-pagination__num) {
    height: 36px;
    line-height: 36px;
  }

  :deep(.uni-pagination__num-current-text) {
    font-size: 14px;
  }

  :deep(.uni-pagination__btn.uni-pagination--disabled) {
    opacity: 0.3;
    background-color: #eee;
  }

  :deep(.uni-icons) {
    line-height: 36px;
  }
  /* #endif */
}

</style>
