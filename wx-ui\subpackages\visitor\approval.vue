<template>
  <view class="approval-container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <uni-data-checkbox 
        v-model="statusFilter"
        :localdata="statusOptions"
        mode="button"
        @change="handleFilterChange"
      />
    </view>

    <!-- 待审核列表 -->
    <view class="approval-list">
      <uni-list v-if="registrationList.length > 0">
        <uni-list-item 
          v-for="registration in registrationList" 
          :key="registration.registrationId"
          :title="registration.primaryContactName"
          :note="`来访事由：${registration.reasonForVisit}`"
          :rightText="getStatusText(registration.status)"
          :clickable="true"
          @click="viewDetail(registration)"
        >
          <template v-slot:header>
            <view class="visitor-avatar">
              <text class="avatar-text">{{ registration.primaryContactName.charAt(0) }}</text>
            </view>
          </template>
          <template v-slot:footer>
            <view class="registration-info">
              <text class="host-info">被访人：{{ registration.hostEmployeeName }}（{{ registration.departmentVisited }}）</text>
              <text class="visit-time">预计来访：{{ formatTime(registration.plannedEntryDatetime) }}</text>
              <view class="action-buttons" v-if="registration.status === 'pending'">
                <button class="approve-btn" @click.stop="handleApprove(registration.registrationId)">通过</button>
                <button class="reject-btn" @click.stop="handleReject(registration)">拒绝</button>
              </view>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="empty-text">暂无访客登记记录</text>
      </view>

      <!-- 加载更多 -->
      <uni-load-more 
        v-if="registrationList.length > 0"
        :status="loadMoreStatus" 
        @clickLoadMore="loadMore"
      ></uni-load-more>
    </view>

    <!-- 拒绝理由弹窗 -->
    <uni-popup ref="rejectPopup" type="center">
      <view class="reject-dialog">
        <view class="dialog-title">{{ rejectDialogTitle }}</view>
        <view class="dialog-content">
          <uni-easyinput 
            v-model="rejectionReason"
            type="textarea"
            placeholder="请输入拒绝理由"
            :auto-height="true"
            maxlength="200"
          />
        </view>
        <view class="dialog-actions">
          <button class="cancel-btn" @click="closeRejectDialog">取消</button>
          <button class="confirm-btn" @click="confirmReject">确认</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { 
  listRegistrationInfo, 
  getPendingRegistrations, 
  approveRegistration, 
  rejectRegistration 
} from '@/api/system/visitor'

export default {
  name: 'VisitorApproval',
  data() {
    return {
      registrationList: [],
      loading: false,
      pageNum: 1,
      pageSize: 20,
      hasMore: true,
      loadMoreStatus: 'more',
      statusFilter: 'pending',
      statusOptions: [
        { text: '待审核', value: 'pending' },
        { text: '已通过', value: 'approved' },
        { text: '已拒绝', value: 'rejected' },
        { text: '全部', value: '' }
      ],
      currentRegistration: null,
      rejectionReason: ''
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '访客审核'
    });
    this.loadRegistrationList();
  },
  onPullDownRefresh() {
    this.refreshList();
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore();
    }
  },
  methods: {
    // 加载登记列表
    async loadRegistrationList(isLoadMore = false) {
      if (this.loading) return;
      
      this.loading = true;
      
      try {
        const params = {
          pageNum: isLoadMore ? this.pageNum + 1 : 1,
          pageSize: this.pageSize
        };
        
        // 添加状态筛选
        if (this.statusFilter) {
          params.status = this.statusFilter;
        }
        
        let response;
        if (this.statusFilter === 'pending') {
          // 专门查询待审核的
          response = await getPendingRegistrations();
        } else {
          // 查询所有状态的
          response = await listRegistrationInfo(params);
        }
        
        if (response.code === 200) {
          const newList = response.data || response.rows || [];
          
          if (isLoadMore) {
            this.registrationList = [...this.registrationList, ...newList];
            this.pageNum++;
          } else {
            this.registrationList = newList;
            this.pageNum = 1;
          }
          
          this.hasMore = newList.length === this.pageSize;
          this.loadMoreStatus = this.hasMore ? 'more' : 'noMore';
        } else {
          this.$modal.showError(response.msg || '获取数据失败');
        }
      } catch (error) {
        console.error('加载登记列表失败:', error);
        this.$modal.showError('加载失败，请检查网络连接');
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },

    // 刷新列表
    async refreshList() {
      this.hasMore = true;
      await this.loadRegistrationList();
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadMoreStatus = 'loading';
        this.loadRegistrationList(true);
      }
    },

    // 筛选状态变化
    handleFilterChange() {
      this.refreshList();
    },

    // 查看详情
    viewDetail(registration) {
      uni.navigateTo({
        url: `/subpackages/visitor/detail?id=${registration.registrationId}`
      });
    },

    // 审核通过
    async handleApprove(registrationId) {
      try {
        const result = await this.$modal.confirm('确认通过该访客登记吗？');
        if (!result) return;

        this.$modal.showLoading('审核中...');
        const response = await approveRegistration(registrationId);
        
        if (response.code === 200) {
          this.$modal.showSuccess('审核通过成功');
          this.refreshList();
        } else {
          this.$modal.showError(response.msg || '审核失败');
        }
      } catch (error) {
        console.error('审核通过失败:', error);
        this.$modal.showError('操作失败，请重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 拒绝登记
    handleReject(registration) {
      this.currentRegistration = registration;
      this.rejectionReason = '';
      this.$refs.rejectPopup.open();
    },

    // 确认拒绝
    async confirmReject() {
      if (!this.rejectionReason.trim()) {
        this.$modal.showError('请输入拒绝理由');
        return;
      }

      try {
        this.$modal.showLoading('处理中...');
        const response = await rejectRegistration(
          this.currentRegistration.registrationId, 
          { rejectionReason: this.rejectionReason.trim() }
        );
        
        if (response.code === 200) {
          this.$modal.showSuccess('已拒绝该登记申请');
          this.refreshList();
          this.closeRejectDialog();
        } else {
          this.$modal.showError(response.msg || '操作失败');
        }
      } catch (error) {
        console.error('拒绝登记失败:', error);
        this.$modal.showError('操作失败，请重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 关闭拒绝对话框
    closeRejectDialog() {
      this.$refs.rejectPopup.close();
      this.currentRegistration = null;
      this.rejectionReason = '';
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝',
        'cancelled': '已取消',
        'completed': '已完成',
        // 兼容大写状态值
        'PENDING': '待审核',
        'APPROVED': '已通过',
        'REJECTED': '已拒绝',
        'CANCELLED': '已取消',
        'COMPLETED': '已完成'
      };
      return statusMap[status] || status;
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '-';
      const date = new Date(timeStr);
      return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
  },
  computed: {
    // 拒绝对话框标题
    rejectDialogTitle() {
      const name = this.currentRegistration && this.currentRegistration.primaryContactName 
        ? this.currentRegistration.primaryContactName 
        : '该访客';
      return `拒绝 ${name} 的访客登记`;
    }
  }
};
</script>

<style scoped>
.approval-container {
  padding: 15px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-bar {
  margin-bottom: 15px;
  background: white;
  padding: 15px;
  border-radius: 8px;
}

.approval-list {
  flex: 1;
}

.visitor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.avatar-text {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.registration-info {
  flex: 1;
  font-size: 12px;
  color: #666;
}

.host-info {
  display: block;
  margin-bottom: 5px;
}

.visit-time {
  display: block;
  margin-bottom: 10px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.approve-btn, .reject-btn {
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 12px;
  border: none;
}

.approve-btn {
  background: #07c160;
  color: white;
}

.reject-btn {
  background: #fa5151;
  color: white;
}

.empty-state {
  text-align: center;
  padding: 50px 0;
  background: white;
  border-radius: 8px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

.reject-dialog {
  background: white;
  border-radius: 12px;
  padding: 20px;
  width: 300px;
  max-width: 80vw;
}

.dialog-title {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.dialog-content {
  margin-bottom: 20px;
}

.dialog-actions {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 10px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #fa5151;
  color: white;
}
</style>
