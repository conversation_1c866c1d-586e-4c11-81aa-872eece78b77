<template>
  <view class="container">
    <uni-card>
      <uni-forms ref="queryForm" :modelValue="queryForm" :rules="rules">
        <uni-forms-item label="查询类型" name="queryType">
          <uni-data-checkbox v-model="queryForm.queryType" :localdata="queryTypes" @change="handleQueryTypeChange"></uni-data-checkbox>
        </uni-forms-item>
        <uni-forms-item :label="queryForm.queryType === 'idCard' ? '身份证号' : '手机号'" name="queryValue">
          <uni-easyinput v-model="queryForm.queryValue" :placeholder="queryForm.queryType === 'idCard' ? '请输入身份证号' : '请输入手机号'" clearable></uni-easyinput>
        </uni-forms-item>
      </uni-forms>
      <button @click="submitQuery" class="query-btn">查询</button>
    </uni-card>

    <uni-card v-if="visitorList.length > 0" title="查询结果">
      <view v-for="(item, index) in visitorList" :key="index" class="visitor-item">
        <view class="item-row">
          <text class="label">姓名:</text>
          <text class="value">{{ item.visitorName }}</text>
        </view>
        <view class="item-row">
          <text class="label">身份证:</text>
          <text class="value">{{ item.idCard }}</text>
        </view>
        <view class="item-row">
          <text class="label">手机号:</text>
          <text class="value">{{ item.phoneNumber }}</text>
        </view>
        <view class="item-row">
          <text class="label">来访时间:</text>
          <text class="value">{{ item.visitTime }}</text>
        </view>
        <view class="item-row">
          <text class="label">状态:</text>
          <text :class="['value', item.status === '已登记' ? 'status-success' : 'status-pending']">{{ item.status }}</text>
        </view>
      </view>
    </uni-card>
    <view v-if="showEmpty" text="暂无查询结果"></view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        queryForm: {
          queryType: 'idCard', // 默认按身份证查询
          queryValue: ''
        },
        queryTypes: [{
          text: '身份证号',
          value: 'idCard'
        }, {
          text: '手机号',
          value: 'phoneNumber'
        }],
        rules: {
          queryValue: {
            rules: [{
              required: true,
              errorMessage: '请输入查询内容'
            }, {
              validateFunction: (rule, value, data, callback) => {
                if (this.queryForm.queryType === 'idCard' && !/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0\d)|(10|11|12))(([0-2]\d)|30|31)\d{3}[0-9Xx]$/.test(value)) {
                  callback('请输入正确的身份证号')
                }
                if (this.queryForm.queryType === 'phoneNumber' && !/^1[3-9]\d{9}$/.test(value)) {
                  callback('请输入正确的手机号')
                }
                return true
              }
            }]
          }
        },
        visitorList: [],
        showEmpty: false
      }
    },
    methods: {
      handleQueryTypeChange(e) {
        this.queryForm.queryValue = '' // 切换查询类型时清空输入框
        this.$refs.queryForm.clearValidate('queryValue') // 清除校验信息
      },
      submitQuery() {
        this.$refs.queryForm.validate().then(res => {
          console.log('表单数据:', this.queryForm)
          // 模拟查询，实际应调用后端接口
          this.fetchVisitorRecords()
        }).catch(err => {
          console.log('表单错误:', err)
        })
      },
      fetchVisitorRecords() {
        // 模拟后端接口调用
        // 实际项目中，这里会调用 uni.request 或封装的 api 方法
        // 假设后端返回的数据结构如下：
        // {"code": 200, "msg": "操作成功", "data": [{ "visitorName": "张三", "idCard": "440XXXXXXXXXXXXXXX", "phoneNumber": "138XXXXXXXX", "visitTime": "2025-06-26 10:00:00", "status": "已登记" }]}

        this.$modal.loading("查询中...")
        setTimeout(() => {
          this.$modal.closeLoading()
          const mockData = [{
            visitorName: "张三",
            idCard: "******************",
            phoneNumber: "13812345678",
            visitTime: "2025-06-26 10:00:00",
            status: "已登记"
          }, {
            visitorName: "李四",
            idCard: "******************",
            phoneNumber: "13987654321",
            visitTime: "2025-06-26 14:30:00",
            status: "待审核"
          }, {
            visitorName: "王五",
            idCard: "******************",
            phoneNumber: "13011112222",
            visitTime: "2025-06-25 09:00:00", // 昨日数据，不应显示
            status: "已登记"
          }, ]

          // 过滤出本日数据
          const today = new Date().toISOString().slice(0, 10)
          this.visitorList = mockData.filter(item => item.visitTime.startsWith(today))

          this.showEmpty = this.visitorList.length === 0
        }, 1000)
      }
    }
  }
</script>

<style lang="scss">
  .container {
    padding: 15px;
  }

  .query-btn {
    margin-top: 15px;
    background-color: #007aff;
    color: white;
    border-radius: 5px;
  }

  .visitor-item {
    padding: 10px;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .item-row {
      display: flex;
      margin-bottom: 5px;

      .label {
        font-weight: bold;
        width: 80px;
      }

      .value {
        flex: 1;
      }

      .status-success {
        color: green;
      }

      .status-pending {
        color: orange;
      }
    }
  }
</style>