<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访客自主登记指南</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #667eea;
            font-size: 36px;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 18px;
        }
        .access-button {
            text-align: center;
            margin: 30px 0;
        }
        .access-button a {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            transition: transform 0.3s;
        }
        .access-button a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .feature {
            text-align: center;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            transition: border-color 0.3s;
        }
        .feature:hover {
            border-color: #667eea;
        }
        .feature-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }
        .steps {
            margin: 40px 0;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .step-number {
            background: #667eea;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }
        .step-content h3 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .step-content p {
            margin: 0;
            color: #666;
        }
        .contact-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 40px;
            text-align: center;
        }
        .contact-info h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 智慧园区访客登记</h1>
            <p>欢迎使用访客自主登记系统，快速完成访问申请</p>
        </div>

        <div class="access-button">
            <a href="/asc/visitor/self-register" target="_blank">🚀 开始自主登记</a>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🆔</div>
                <h3>多种验证方式</h3>
                <p>支持身份证、人脸识别、手动输入等多种身份验证方式</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📝</div>
                <h3>信息完整收集</h3>
                <p>收集访客基本信息、访问详情和同行人员信息</p>
            </div>
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <h3>快速便捷</h3>
                <p>三步完成登记，即时生成访问凭证</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🔒</div>
                <h3>安全可靠</h3>
                <p>信息加密传输，严格保护个人隐私</p>
            </div>
        </div>

        <div class="steps">
            <h2 style="text-align: center; color: #667eea; margin-bottom: 30px;">📋 登记流程</h2>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h3>身份验证</h3>
                    <p>选择合适的验证方式进行身份确认（身份证读卡器、人脸识别、手动输入、护照验证）</p>
                </div>
            </div>

            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h3>填写访问信息</h3>
                    <p>完善来访事由、被访人、部门、预计时间等详细信息，可添加同行人员</p>
                </div>
            </div>

            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h3>完成登记</h3>
                    <p>提交申请并获取临时凭证，等待审核通过后可用于进园</p>
                </div>
            </div>
        </div>

        <div class="contact-info">
            <h3>📞 需要帮助？</h3>
            <p>如遇到技术问题或需要协助，请联系园区前台</p>
            <p><strong>服务时间：</strong>周一至周五 8:00-18:00</p>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.querySelectorAll('.feature').forEach(feature => {
            feature.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 5px 15px rgba(102, 126, 234, 0.2)';
            });
            
            feature.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>
