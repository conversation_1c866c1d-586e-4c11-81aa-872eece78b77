<template>
  <div class="pc-register-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-content">
        <div class="logo-section">
          <!-- <img src="@/assets/logo/logo.png" alt="公司logo" class="nav-logo" /> -->
          <span class="company-name">高义钢铁访客登记系统</span>
        </div>
        <!-- <div class="nav-actions">
          <el-button type="text" @click="showHelp">使用帮助</el-button>
          <el-button type="text" @click="contactService">联系客服</el-button>
        </div> -->
      </div>
    </div>

    <!-- 主要内容区域 - 移动端优化 -->
    <div class="main-content mobile-layout">
      <!-- 移动端欢迎区域 -->
      <!-- <div class="mobile-welcome" v-if="!registrationCompleted"> -->
        <!-- <div class="welcome-header">
          <div class="welcome-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <h2>访客登记</h2>
          <p class="welcome-desc">请填写以下信息完成登记</p>
        </div> -->

        <!-- 简化的进度指示 -->
        <!-- <div class="progress-indicator">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: registrationCompleted ? '100%' : '60%' }"></div>
          </div>
          <div class="progress-text">
            {{ registrationCompleted ? '登记完成' : '正在填写信息...' }}
          </div>
        </div> -->
      <!-- </div> -->

      <!-- 表单容器 - 全屏移动端布局 -->
      <div class="mobile-form-container">
        <!-- 表单标题 -->
        <div class="mobile-form-header" v-if="!registrationCompleted">
          <h3><i class="el-icon-edit-outline"></i> 填写登记信息</h3>
          <p class="form-subtitle">共{{ totalVisitors }}人，请确保信息准确</p>
        </div>

        <!-- 成功页面标题 -->
        <!-- <div class="mobile-form-header" v-else>
          <h3><i class="el-icon-circle-check"></i> 登记成功</h3>
          <p class="form-subtitle">您的访客登记已提交成功</p>
        </div> -->

        <!-- 移动端表单内容 -->
        <div class="mobile-form-content" v-if="!registrationCompleted">
          <el-form ref="visitForm" :model="formData" :rules="visitRules"
                   label-width="100px" class="mobile-visit-form">

            <!-- 访问信息卡片 -->
            <div class="mobile-form-card">
              <div class="card-header">
                <i class="el-icon-document"></i>
                <span class="card-title">访问信息</span>
              </div>

              <div class="card-content">
                <el-form-item label="来访事由" prop="visitInfo.reasonForVisit" class="mobile-form-item">
                  <el-input v-model="formData.visitInfo.reasonForVisit" placeholder="请输入来访事由"
                            type="textarea" :rows="3" maxlength="200" show-word-limit
                            class="mobile-input" />
                </el-form-item>

                <el-form-item label="被访人" prop="visitInfo.hostEmployeeName" class="mobile-form-item">
                  <el-input v-model="formData.visitInfo.hostEmployeeName" placeholder="请输入被访人姓名"
                            maxlength="20" class="mobile-input" />
                </el-form-item>

                <el-form-item label="被访部门" prop="visitInfo.departmentVisited" class="mobile-form-item">
                  <el-input v-model="formData.visitInfo.departmentVisited" placeholder="请输入被访部门"
                            maxlength="50" class="mobile-input" />
                </el-form-item>

                <el-form-item label="车牌号" class="mobile-form-item">
                  <el-input v-model="formData.visitInfo.vehiclePlateNumber" placeholder="如有车辆请填写（可选）"
                            class="mobile-input" />
                </el-form-item>
              </div>
            </div>

            <!-- 时间信息卡片 -->
            <div class="mobile-form-card">
              <div class="card-header">
                <i class="el-icon-time"></i>
                <span class="card-title">访问时间</span>
              </div>

              <div class="card-content">
                <el-form-item label="到访时间" prop="visitInfo.plannedEntryDatetime" class="mobile-form-item">
                  <el-date-picker
                    v-model="formData.visitInfo.plannedEntryDatetime"
                    type="datetime"
                    placeholder="选择到访时间"
                    style="width: 100%"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="entryPickerOptions"
                    @change="onArrivalTimeChange"
                    class="mobile-date-picker"
                  />
                </el-form-item>

                <el-form-item label="离开时间" prop="visitInfo.plannedExitDatetime" class="mobile-form-item">
                  <el-date-picker
                    v-model="formData.visitInfo.plannedExitDatetime"
                    type="datetime"
                    placeholder="选择离开时间"
                    style="width: 100%"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="exitPickerOptions"
                    @change="onDepartureTimeChange"
                    class="mobile-date-picker"
                  />
                </el-form-item>
              </div>
            </div>

            <!-- 访客信息卡片 -->
            <div class="mobile-form-card">
              <div class="card-header">
                <i class="el-icon-user"></i>
                <span class="card-title">访客信息（共{{ totalVisitors }}人）</span>
                <el-button type="primary" size="mini" icon="el-icon-plus" @click="addVisitor"
                           :disabled="formData.visitors.length >= 10" class="add-visitor-btn">
                  添加
                </el-button>
              </div>

              <div class="card-content">
                <div v-for="(visitor, index) in formData.visitors" :key="index" class="mobile-visitor-item">
                  <div class="visitor-header">
                    <div class="visitor-badge" :class="{ primary: index === 0 }">
                      {{ index === 0 ? '主联系人' : `访客${index + 1}` }}
                    </div>
                    <el-button v-if="index > 0" size="mini" type="danger" icon="el-icon-delete"
                               @click="removeVisitor(index)" circle class="remove-btn"></el-button>
                  </div>

                  <div class="visitor-form">
                    <el-form-item label="姓名" :prop="`visitors.${index}.name`" class="mobile-form-item">
                      <el-input v-model="visitor.name" placeholder="请输入姓名" maxlength="20"
                                class="mobile-input" />
                    </el-form-item>

                    <el-form-item label="手机号" :prop="`visitors.${index}.phone`" class="mobile-form-item">
                      <el-input v-model="visitor.phone" placeholder="请输入手机号"
                                class="mobile-input" />
                    </el-form-item>

                    <el-form-item label="身份证号" :prop="`visitors.${index}.idCard`" class="mobile-form-item">
                      <el-input v-model="visitor.idCard" placeholder="请输入身份证号"
                                class="mobile-input" />
                    </el-form-item>

                    <el-form-item label="公司名称" class="mobile-form-item">
                      <el-input v-model="visitor.company" placeholder="请输入公司名称（可选）"
                                maxlength="100" class="mobile-input" />
                    </el-form-item>
                  </div>
                </div>
              </div>
            </div>
          </el-form>
        </div>

        <!-- 登记成功页面 -->
        <div class="success-content" v-if="registrationCompleted">
          <div class="success-icon">
            <i class="el-icon-circle-check"></i>
          </div>

          <h4>登记成功！</h4>
          <p class="success-message">您的访客登记已提交，请等待审核通过</p>

          <!-- 登记信息摘要 -->
          <div class="register-summary">
            <h5>登记信息摘要</h5>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="主联系人">
                {{ mainContact.name }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ mainContact.phone }}
              </el-descriptions-item>
              <el-descriptions-item label="被访人">
                {{ formData.visitInfo.hostEmployeeName }}
              </el-descriptions-item>
              <el-descriptions-item label="被访部门">
                {{ formData.visitInfo.departmentVisited }}
              </el-descriptions-item>
              <el-descriptions-item label="来访事由" span="2">
                {{ formData.visitInfo.reasonForVisit }}
              </el-descriptions-item>
              <el-descriptions-item label="预计到访时间">
                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}
              </el-descriptions-item>
              <el-descriptions-item label="预计离开时间">
                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}
              </el-descriptions-item>
              <el-descriptions-item label="访客总数">
                {{ totalVisitors }} 人
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 访问凭证 -->
          <!-- <div class="access-credential" v-if="registrationId">
            <h5>访问凭证</h5>
            <div class="credential-card">
              <div class="qr-code-container">
                <div class="qr-code">
                  <i class="el-icon-qrcode"></i>
                </div>
                <p class="qr-code-id">{{ registrationId }}</p>
              </div>
              <div class="credential-info">
                <h6>使用说明</h6>
                <ul>
                  <li>请保存此二维码截图</li>
                  <li>审核通过后可用于园区门禁</li>
                  <li>凭证仅限当次访问使用</li>
                  <li>如有疑问请联系园区前台</li>
                </ul>
              </div>
            </div>
          </div> -->
        </div>

        <!-- 移动端底部操作区 -->
        <div class="mobile-actions">
          <!-- 表单提交按钮 -->
          <div v-if="!registrationCompleted" class="submit-actions">
            <el-button type="primary" @click="submitForm" size="large" :loading="submitting"
                       class="mobile-submit-btn">
              <i class="el-icon-check" v-if="!submitting"></i>
              <i class="el-icon-loading" v-if="submitting"></i>
              {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}
            </el-button>

            <el-button @click="resetForm" size="large" class="mobile-reset-btn">
              <i class="el-icon-refresh-left"></i>
              重置表单
            </el-button>
          </div>

          <!-- 成功后操作按钮 -->
          <div v-if="registrationCompleted" class="success-actions">
            <!-- <el-button type="primary" @click="printCredential" size="large" class="mobile-action-btn">
              <i class="el-icon-printer"></i>
              打印凭证
            </el-button> -->
            <!-- <el-button @click="resetForm" size="large" class="mobile-action-btn">
              <i class="el-icon-refresh"></i>
              重新登记
            </el-button> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { submitVisitorRegistration } from "@/api/asc/visitor";

export default {
  name: "SelfRegister",
  data() {
    return {
      submitting: false,
      registrationCompleted: false,

      // 表单数据 - 与微信端保持一致的结构
      formData: {
        // 访客列表（第一位为主联系人）
        visitors: [
          {
            name: '',
            phone: '',
            idCard: '',
            company: '',
            isMainContact: true
          }
        ],
        // 来访信息
        visitInfo: {
          reasonForVisit: '',
          hostEmployeeName: '',
          departmentVisited: '',
          vehiclePlateNumber: '',
          plannedEntryDatetime: '',
          plannedExitDatetime: ''
        }
      },

      // 表单验证规则 - 与微信端保持一致
      visitRules: {
        'visitInfo.reasonForVisit': [
          { required: true, message: '请输入来访事由', trigger: 'blur' },
          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }
        ],
        'visitInfo.hostEmployeeName': [
          { required: true, message: '请输入被访人姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }
        ],
        'visitInfo.departmentVisited': [
          { required: true, message: '请输入被访部门', trigger: 'blur' },
          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }
        ],
        'visitInfo.plannedEntryDatetime': [
          { required: true, message: '请选择预计来访时间', trigger: 'change' }
        ],
        'visitInfo.plannedExitDatetime': [
          { required: true, message: '请选择预计离开时间', trigger: 'change' }
        ],
        // 动态访客验证规则
        'visitors.0.name': [
          { required: true, message: '请输入主联系人姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
        ],
        'visitors.0.phone': [
          { required: true, message: '请输入主联系人手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        'visitors.0.idCard': [
          { required: true, message: '请输入主联系人身份证号', trigger: 'blur' }
        ]
      },

      // 登记结果
      registrationId: null,

      // 时间选择器配置
      entryPickerOptions: {
        shortcuts: [{
          text: '现在',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '1小时后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000);
            picker.$emit('pick', date);
          }
        }, {
          text: '明天上午9点',
          onClick(picker) {
            const date = new Date();
            date.setDate(date.getDate() + 1);
            date.setHours(9, 0, 0, 0);
            picker.$emit('pick', date);
          }
        }],
        disabledDate(time) {
          // 允许选择当前时间前1小时到未来30天
          const oneHourBefore = Date.now() - 3600 * 1000;
          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;
          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;
        }
      },

      exitPickerOptions: {
        shortcuts: [{
          text: '2小时后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 2 * 3600 * 1000);
            picker.$emit('pick', date);
          }
        }, {
          text: '中午12点',
          onClick(picker) {
            const date = new Date();
            date.setHours(12, 0, 0, 0);
            if (date.getTime() < Date.now()) {
              date.setDate(date.getDate() + 1);
            }
            picker.$emit('pick', date);
          }
        }, {
          text: '今天下午6点',
          onClick(picker) {
            const date = new Date();
            date.setHours(18, 0, 0, 0);
            if (date.getTime() < Date.now()) {
              date.setDate(date.getDate() + 1);
            }
            picker.$emit('pick', date);
          }
        }],
        disabledDate(time) {
          // 允许选择当前时间到未来30天
          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;
          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;
        }
      }
    };
  },

  computed: {
    // 总访客人数
    totalVisitors() {
      return this.formData.visitors.length;
    },

    // 主联系人信息
    mainContact() {
      return this.formData.visitors[0] || {};
    }
  },

  methods: {
    // 添加访客
    addVisitor() {
      if (this.formData.visitors.length >= 10) {
        this.$message.warning('最多只能添加10名访客');
        return;
      }

      this.formData.visitors.push({
        name: '',
        phone: '',
        idCard: '',
        company: '',
        isMainContact: false
      });

      this.$message.success('已添加访客');
    },

    // 移除访客
    removeVisitor(index) {
      if (index === 0) {
        this.$message.warning('不能删除主联系人');
        return;
      }

      this.formData.visitors.splice(index, 1);
      this.$message.success('已移除访客');
    },

    // 格式化日期为后端期望的格式
    formatDateForBackend(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    /** 来访时间变更事件 */
    onArrivalTimeChange(value) {
      console.log('预计来访时间变更:', value);
      // 自动设置离开时间为来访时间后4小时
      if (value && !this.formData.visitInfo.plannedExitDatetime) {
        const arrivalTime = new Date(value);
        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);

        const year = departureTime.getFullYear();
        const month = String(departureTime.getMonth() + 1).padStart(2, '0');
        const day = String(departureTime.getDate()).padStart(2, '0');
        const hours = String(departureTime.getHours()).padStart(2, '0');
        const minutes = String(departureTime.getMinutes()).padStart(2, '0');
        const seconds = String(departureTime.getSeconds()).padStart(2, '0');

        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
    },

    /** 离开时间变更事件 */
    onDepartureTimeChange(value) {
      console.log('预计离开时间变更:', value);
      // 验证离开时间不能早于来访时间
      if (value && this.formData.visitInfo.plannedEntryDatetime) {
        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);
        const departureTime = new Date(value);

        if (departureTime <= arrivalTime) {
          this.$message.warning('预计离开时间不能早于或等于来访时间');
          this.formData.visitInfo.plannedExitDatetime = '';
        }
      }
    },

    /** 提交表单 */
    async submitForm() {
      try {
        // 验证表单
        await this.$refs.visitForm.validate();

        // 验证访客信息
        if (!this.validateVisitors()) {
          return;
        }

        // 验证时间
        if (!this.validateTimes()) {
          return;
        }

        this.submitting = true;

        // 获取主联系人信息
        const mainContact = this.formData.visitors[0];

        const submitData = {
          // VisitRegistrations 对象
          registration: {
            primaryContactName: mainContact.name.trim(),
            primaryContactPhone: mainContact.phone.trim(),
            reasonForVisit: this.formData.visitInfo.reasonForVisit,
            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,
            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理
            departmentVisited: this.formData.visitInfo.departmentVisited,
            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),
            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),
            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',
            totalCompanions: this.formData.visitors.length - 1
          },
          // RegistrationAttendees 数组 - 使用后端期望的临时字段
          attendeesList: this.formData.visitors.map((visitor, index) => ({
            visitorName: visitor.name.trim(),
            visitorPhone: visitor.phone.trim(),
            visitorIdCard: visitor.idCard.trim().toUpperCase(),
            visitorCompany: (visitor.company || '').trim(),
            isPrimary: index === 0 ? "1" : "0", // 第一个访客必须是主联系人
            visitorAvatarPhoto: null // 暂时不支持头像上传
          }))
        };

        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));

        // 调用API提交数据
        const response = await submitVisitorRegistration(submitData);

        if (response.code === 200) {
          this.registrationId = response.data || 'VR' + Date.now();
          this.$message.success(`${this.totalVisitors}名访客登记成功！`);
          this.registrationCompleted = true;
        } else {
          this.$message.error(response.msg || '登记失败，请重试');
        }
      } catch (error) {
        console.error('提交表单失败:', error);
        this.$message.error('登记失败，请检查网络连接');
      } finally {
        this.submitting = false;
      }
    },

    // 验证访客信息
    validateVisitors() {
      for (let i = 0; i < this.formData.visitors.length; i++) {
        const visitor = this.formData.visitors[i];
        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;

        // 验证姓名
        if (!visitor.name || visitor.name.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的姓名`);
          return false;
        }

        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {
          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);
          return false;
        }

        // 验证手机号
        if (!visitor.phone || visitor.phone.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的手机号`);
          return false;
        }

        // 验证手机号格式（与后端保持一致）
        const phonePattern = /^1[3-9]\d{9}$/;
        if (!phonePattern.test(visitor.phone.trim())) {
          this.$message.error(`${visitorTitle}的手机号格式不正确`);
          return false;
        }

        // 验证身份证号
        if (!visitor.idCard || visitor.idCard.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的身份证号`);
          return false;
        }

        const idCard = visitor.idCard.trim().toUpperCase();

        // 如果是18位身份证号，验证格式
        if (idCard.length === 18) {
          const idPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
          if (!idPattern.test(idCard)) {
            this.$message.error(`${visitorTitle}的身份证号格式不正确`);
            return false;
          }
        }

        // 检查身份证号是否重复
        for (let j = 0; j < this.formData.visitors.length; j++) {
          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {
            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);
            return false;
          }
        }

        // 检查手机号是否重复
        for (let j = 0; j < this.formData.visitors.length; j++) {
          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {
            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);
            return false;
          }
        }

        // 清理和标准化数据
        visitor.name = visitor.name.trim();
        visitor.phone = visitor.phone.trim();
        visitor.idCard = visitor.idCard.trim().toUpperCase();
        visitor.company = (visitor.company || '').trim();
      }

      return true;
    },

    // 验证时间
    validateTimes() {
      if (!this.formData.visitInfo.plannedEntryDatetime) {
        this.$message.error('请选择预计来访时间');
        return false;
      }

      if (!this.formData.visitInfo.plannedExitDatetime) {
        this.$message.error('请选择预计离开时间');
        return false;
      }

      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);
      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);
      const now = new Date();

      // 检查来访时间不能早于当前时间1小时前
      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);
      if (entryTime < oneHourBefore) {
        this.$message.error('预计来访时间不能早于当前时间1小时前');
        return false;
      }

      // 检查离开时间必须晚于来访时间
      if (exitTime <= entryTime) {
        this.$message.error('预计离开时间必须晚于来访时间');
        return false;
      }

      // 检查访问时长不能超过24小时
      const visitDuration = exitTime.getTime() - entryTime.getTime();
      const maxDuration = 24 * 60 * 60 * 1000; // 24小时
      if (visitDuration > maxDuration) {
        this.$message.error('单次访问时长不能超过24小时');
        return false;
      }

      return true;
    },

    // 重置表单
    resetForm() {
      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置所有数据
        this.registrationCompleted = false;

        this.formData = {
          visitors: [
            {
              name: '',
              phone: '',
              idCard: '',
              company: '',
              isMainContact: true
            }
          ],
          visitInfo: {
            reasonForVisit: '',
            hostEmployeeName: '',
            departmentVisited: '',
            vehiclePlateNumber: '',
            plannedEntryDatetime: '',
            plannedExitDatetime: ''
          }
        };

        this.registrationId = null;

        this.$message.success('表单已重置');
      });
    },

    // 打印凭证
    printCredential() {
      this.$message.info('打印功能开发中...');
    },

    // 格式化时间显示
    parseTime(time) {
      if (!time) return '';
      const date = new Date(time);
      if (isNaN(date.getTime())) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 显示帮助信息
    showHelp() {
      this.$alert(`
        <div style="text-align: left;">
          <h4>使用说明</h4>
          <p><strong>1. 填写基本信息</strong></p>
          <ul>
            <li>请如实填写来访事由、被访人等信息</li>
            <li>主联系人信息必须准确，用于后续联系</li>
          </ul>
          <p><strong>2. 添加同行人员</strong></p>
          <ul>
            <li>如有同行人员，请点击"添加访客"按钮</li>
            <li>每位访客都需要填写完整身份信息</li>
          </ul>
          <p><strong>3. 选择访问时间</strong></p>
          <ul>
            <li>请合理安排访问时间，避免过长逗留</li>
            <li>如需延长访问时间，请联系被访人</li>
          </ul>
          <p><strong>4. 审核与通行</strong></p>
          <ul>
            <li>提交后等待审核，通过后可凭二维码进入园区</li>
            <li>请按照预约时间准时到访</li>
          </ul>
        </div>
      `, '使用帮助', {
        confirmButtonText: '知道了',
        dangerouslyUseHTMLString: true
      });
    },

    // 联系客服
    contactService() {
      this.$confirm('需要帮助吗？', '联系客服', {
        confirmButtonText: '拨打电话',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        // 这里可以实现拨打客服电话的功能
        this.$message({
          type: 'info',
          message: '客服电话：400-xxx-xxxx'
        });
      }).catch(() => {
        // 用户取消
      });
    }
  }
};
</script>

<style scoped>
/* 主容器 */
.pc-register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航 */
.top-nav {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 40px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  max-width: 1400px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.nav-logo {
  height: 40px;
}

.company-name {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.nav-actions {
  display: flex;
  gap: 20px;
}

/* 主内容区域 */
.main-content {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px;
  gap: 40px;
  min-height: calc(100vh - 110px);
}

/* 移动端布局优化 */
.main-content.mobile-layout {
  flex-direction: column;
  padding: 20px;
  gap: 20px;
  max-width: 100%;
}

/* 移动端欢迎区域 */
.mobile-welcome {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.welcome-header {
  text-align: center;
  margin-bottom: 20px;
}

.welcome-header .welcome-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.welcome-header .welcome-icon i {
  font-size: 28px;
  color: #fff;
}

.welcome-header h2 {
  font-size: 24px;
  color: #2c3e50;
  margin: 0 0 10px;
  font-weight: 600;
}

.welcome-header .welcome-desc {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 进度指示器 */
.progress-indicator {
  margin-top: 20px;
}

.progress-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #7f8c8d;
}

/* 移动端表单容器 */
.mobile-form-container {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 移动端表单标题 */
.mobile-form-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 20px;
  text-align: center;
}

.mobile-form-header h3 {
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
}

.mobile-form-header .form-subtitle {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 移动端表单内容 */
.mobile-form-content {
  padding: 20px;
}

/* 移动端表单卡片 */
.mobile-form-card {
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.mobile-form-card .card-header {
  background: #fff;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-form-card .card-header i {
  font-size: 16px;
  color: #667eea;
  margin-right: 8px;
}

.mobile-form-card .card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

.mobile-form-card .add-visitor-btn {
  margin-left: 10px;
}

.mobile-form-card .card-content {
  padding: 20px;
}

/* 移动端表单项 */
.mobile-form-item {
  margin-bottom: 20px;
}

.mobile-form-item .el-form-item__label {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  padding-bottom: 8px;
}

/* 移动端输入框 */
.mobile-input .el-input__inner {
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  padding: 0 15px;
}

.mobile-input .el-textarea__inner {
  font-size: 16px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  padding: 12px 15px;
  line-height: 1.5;
}

/* 移动端日期选择器 */
.mobile-date-picker .el-input__inner {
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
}

/* 移动端访客项 */
.mobile-visitor-item {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #e9ecef;
  position: relative;
}

.mobile-visitor-item:last-child {
  margin-bottom: 0;
}

.mobile-visitor-item .visitor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.mobile-visitor-item .visitor-badge {
  background: #f8f9fa;
  color: #6c757d;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #e9ecef;
}

.mobile-visitor-item .visitor-badge.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
}

.mobile-visitor-item .remove-btn {
  width: 28px;
  height: 28px;
  padding: 0;
}

/* 移动端操作区域 */
.mobile-actions {
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.submit-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.success-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 移动端按钮 */
.mobile-submit-btn,
.mobile-reset-btn,
.mobile-action-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  border: none;
}

.mobile-submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.mobile-submit-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.mobile-reset-btn {
  background: #fff;
  color: #6c757d;
  border: 1px solid #dcdfe6;
}

.mobile-reset-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.mobile-action-btn {
  background: #fff;
  color: #667eea;
  border: 1px solid #667eea;
}

.mobile-action-btn:hover {
  background: #667eea;
  color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  /* 顶部导航移动端优化 */
  .top-nav {
    padding: 0 20px;
  }

  .nav-content {
    height: 60px;
  }

  .company-name {
    font-size: 16px;
  }

  .nav-actions {
    gap: 10px;
  }

  /* 主内容区域移动端优化 */
  .main-content {
    padding: 15px;
    gap: 15px;
  }

  .main-content.mobile-layout {
    padding: 10px;
    gap: 15px;
  }

  /* 移动端欢迎区域优化 */
  .mobile-welcome {
    padding: 15px;
    margin-bottom: 15px;
  }

  .welcome-header .welcome-icon {
    width: 50px;
    height: 50px;
  }

  .welcome-header .welcome-icon i {
    font-size: 24px;
  }

  .welcome-header h2 {
    font-size: 20px;
  }

  /* 移动端表单优化 */
  .mobile-form-container {
    border-radius: 8px;
  }

  .mobile-form-header {
    padding: 15px;
  }

  .mobile-form-header h3 {
    font-size: 16px;
  }

  .mobile-form-content {
    padding: 15px;
  }

  .mobile-form-card {
    margin-bottom: 15px;
  }

  .mobile-form-card .card-header {
    padding: 12px 15px;
  }

  .mobile-form-card .card-content {
    padding: 15px;
  }

  /* 移动端访客项优化 */
  .mobile-visitor-item {
    padding: 12px;
    margin-bottom: 12px;
  }

  /* 移动端操作区域优化 */
  .mobile-actions {
    padding: 15px;
  }

  .mobile-submit-btn,
  .mobile-reset-btn,
  .mobile-action-btn {
    height: 44px;
    font-size: 15px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .top-nav {
    padding: 0 15px;
  }

  .nav-content {
    height: 55px;
  }

  .company-name {
    font-size: 14px;
  }

  .main-content.mobile-layout {
    padding: 8px;
  }

  .mobile-welcome {
    padding: 12px;
  }

  .mobile-form-header {
    padding: 12px;
  }

  .mobile-form-content {
    padding: 12px;
  }

  .mobile-actions {
    padding: 12px;
  }
}

/* 左侧信息面板 */
.info-panel {
  flex: 0 0 400px;
}

.welcome-card {
  background: #fff;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 110px;
}

.welcome-icon {
  text-align: center;
  margin-bottom: 24px;
}

.welcome-icon i {
  font-size: 64px;
  color: #3498db;
}

.welcome-card h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 28px;
  font-weight: 600;
}

.welcome-desc {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 32px;
  font-size: 16px;
  line-height: 1.6;
}

/* 流程步骤 */
.process-steps {
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.step-item.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateX(8px);
}

.step-item:not(.active) {
  background: #f8f9fa;
  color: #6c757d;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.step-item.active .step-circle {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.step-item:not(.active) .step-circle {
  background: #dee2e6;
  color: #6c757d;
}

.step-text h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.step-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* 安全提示 */
.security-tips {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #3498db;
}

.security-tips h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.security-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.security-tips li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

/* 右侧表单面板 */
.form-panel {
  flex: 1;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 进度指示器 */
.progress-indicator {
  background: #f8f9fa;
  padding: 24px 32px;
  border-bottom: 1px solid #e9ecef;
}

.progress-bar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

/* 表单内容 */
.form-content {
  padding: 32px;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h3 {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.form-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 16px;
}

/* 验证方式网格 */
.verify-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.verify-card {
  position: relative;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.verify-card:hover {
  border-color: #3498db;
  background: #fff;
  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);
  transform: translateY(-2px);
}

.verify-card.selected {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
}

.verify-icon {
  margin-bottom: 16px;
}

.verify-icon i {
  font-size: 48px;
  color: #3498db;
}

.verify-card.selected .verify-icon i {
  color: white;
}

.verify-card h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.verify-card p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.verify-status {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 验证操作区域 */
.verify-operation {
  margin-top: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.operation-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

/* 身份表单 */
.identity-form {
  background: white;
  padding: 24px;
  border-radius: 12px;
}

/* 身份证读卡器 */
.id-card-reader {
  text-align: center;
}

.reader-visual {
  background: white;
  padding: 40px;
  border-radius: 12px;
}

.reader-animation {
  margin-bottom: 24px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.reader-animation i {
  font-size: 64px;
  color: #3498db;
}

.reader-visual h4 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.reader-visual p {
  color: #7f8c8d;
  margin-bottom: 24px;
}

.reader-tips {
  margin: 24px 0;
}

/* 人脸识别 */
.face-recognition {
  text-align: center;
}

.camera-container {
  background: white;
  padding: 40px;
  border-radius: 12px;
}

.camera-frame {
  position: relative;
  width: 280px;
  height: 210px;
  margin: 0 auto 24px;
  border: 3px solid #3498db;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  overflow: hidden;
}

.camera-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.face-outline {
  width: 120px;
  height: 150px;
  border: 2px dashed #3498db;
  border-radius: 60px;
  opacity: 0.6;
}

.camera-icon {
  font-size: 32px;
  color: #3498db;
}

.scanning-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3498db, transparent);
  animation: scan 2s ease-in-out infinite;
}

@keyframes scan {
  0% { transform: translateY(0); }
  50% { transform: translateY(206px); }
  100% { transform: translateY(0); }
}

/* 表单分组 */
.form-section {
  margin-bottom: 32px;
}

.section-title {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 同行人员 */
.no-companions {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.no-companions i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #bdc3c7;
}

.companions-table {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

/* 成功页面 */
.success-content {
  text-align: center;
}

.success-icon {
  margin-bottom: 24px;
}

.success-icon i {
  font-size: 80px;
  color: #27ae60;
}

.success-content h4 {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 12px;
}

.success-message {
  color: #7f8c8d;
  font-size: 16px;
  margin-bottom: 32px;
}

.register-summary {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 32px;
  text-align: left;
}

.register-summary h5 {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

.access-credential {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  text-align: left;
}

.access-credential h5 {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

.credential-card {
  display: flex;
  gap: 24px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.qr-code-container {
  flex: 0 0 120px;
  text-align: center;
}

.qr-code {
  width: 120px;
  height: 120px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  margin-bottom: 8px;
}

.qr-code i {
  font-size: 48px;
  color: #7f8c8d;
}

.qr-code-id {
  font-size: 12px;
  color: #6c757d;
  font-family: monospace;
  margin: 0;
}

.credential-info {
  flex: 1;
}

.credential-info h6 {
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.credential-info ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.credential-info li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

/* 底部操作按钮 */
.form-actions {
  padding: 24px 32px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.final-actions {
  display: flex;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 24px;
  }
  
  .info-panel {
    flex: none;
  }
  
  .welcome-card {
    position: static;
  }
  
  .verify-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px;
  }
  
  .nav-content {
    padding: 0 20px;
  }
  
  .company-name {
    font-size: 16px;
  }
  
  .form-content {
    padding: 20px;
  }
  
  .credential-card {
    flex-direction: column;
    text-align: center;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 16px;
  }
}

/* Element UI 样式覆盖 */
.el-form-item {
  margin-bottom: 24px;
}

.el-input__inner {
  height: 44px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.el-input__inner:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.el-select {
  width: 100%;
}

.el-date-editor {
  width: 100%;
}

.el-button--large {
  height: 44px;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
}

.el-descriptions {
  border-radius: 8px;
}

.el-table {
  border-radius: 8px;
}

.el-alert {
  border-radius: 8px;
}
</style>
